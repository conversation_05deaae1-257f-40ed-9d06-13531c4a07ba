[gd_scene load_steps=32 format=3 uid="uid://dvgmw3ndx3nfx"]

[ext_resource type="Shader" uid="uid://cfjms7igvjapc" path="res://power_up_shader.tres" id="1_5rwps"]
[ext_resource type="Script" uid="uid://doq3qjy351hur" path="res://vfx_power_up.gd" id="1_h2ngo"]
[ext_resource type="Texture2D" uid="uid://v8r288glr1u6" path="res://POWERUP_TEXTURES/Textures/T_Aurax12.jpg" id="2_p5psq"]
[ext_resource type="ArrayMesh" uid="uid://e371smbj4und" path="res://cylinder_long.obj" id="3_nm8rl"]
[ext_resource type="Texture2D" uid="uid://dfdoxu0b1fdx1" path="res://POWERUP_TEXTURES/Textures/T_disort_test2.PNG" id="4_h2ngo"]
[ext_resource type="Shader" uid="uid://dgwac6mam38tg" path="res://POWERUP_TEXTURES/s_simple1.tres" id="5_nm8rl"]
[ext_resource type="Texture2D" uid="uid://djtd4uc4abq6f" path="res://POWERUP_TEXTURES/Textures/T_VFX_Glo31.png" id="6_h2ngo"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_73v70"]
render_priority = 0
shader = ExtResource("1_5rwps")
shader_parameter/speed = Vector2(1.5, 3)
shader_parameter/aura_texture = ExtResource("2_p5psq")

[sub_resource type="Curve" id="Curve_7ykd1"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.469767, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_v4e51"]
texture_mode = 1
curve = SubResource("Curve_7ykd1")

[sub_resource type="Curve" id="Curve_hd6s3"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Curve" id="Curve_rdr4e"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Curve" id="Curve_2tdaa"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveXYZTexture" id="CurveXYZTexture_sb3rf"]
curve_x = SubResource("Curve_hd6s3")
curve_y = SubResource("Curve_rdr4e")
curve_z = SubResource("Curve_2tdaa")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_0hx7y"]
gravity = Vector3(0, 0, 0)
scale_min = 0.5
scale_max = 0.5
scale_curve = SubResource("CurveXYZTexture_sb3rf")
color = Color(2.5, 1.575, 0.284, 1)
alpha_curve = SubResource("CurveTexture_v4e51")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_hrx22"]
render_priority = 0
shader = ExtResource("1_5rwps")
shader_parameter/speed = Vector2(1, 3)
shader_parameter/aura_texture = ExtResource("4_h2ngo")

[sub_resource type="Curve" id="Curve_8cdnn"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Curve" id="Curve_ofi5q"]
_data = [Vector2(0, 0.506079), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Curve" id="Curve_sjnhf"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveXYZTexture" id="CurveXYZTexture_djxxo"]
curve_x = SubResource("Curve_8cdnn")
curve_y = SubResource("Curve_ofi5q")
curve_z = SubResource("Curve_sjnhf")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_27u1n"]
gravity = Vector3(0, 0, 0)
scale_min = 1.2
scale_max = 1.2
scale_curve = SubResource("CurveXYZTexture_djxxo")
color = Color(2.5, 1.575, 0.284, 1)
alpha_curve = SubResource("CurveTexture_v4e51")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_8cdnn"]
render_priority = 0
shader = ExtResource("5_nm8rl")
shader_parameter/Main_Texture = ExtResource("6_h2ngo")
shader_parameter/Proximity_Fade = 0.5

[sub_resource type="Curve" id="Curve_djxxo"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.21374, 1), 0.0, 0.0, 0, 0, Vector2(0.78626, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 4

[sub_resource type="CurveTexture" id="CurveTexture_dfo4r"]
texture_mode = 1
curve = SubResource("Curve_djxxo")

[sub_resource type="Curve" id="Curve_27u1n"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_hyvgs"]
curve = SubResource("Curve_27u1n")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_w7wss"]
emission_shape = 6
emission_ring_axis = Vector3(0, 1, 0)
emission_ring_height = 1.0
emission_ring_radius = 0.1
emission_ring_inner_radius = 1.0
emission_ring_cone_angle = 0.0
direction = Vector3(0, 1, 0)
spread = 50.0
initial_velocity_min = 6.0
initial_velocity_max = 9.0
gravity = Vector3(0, 0, 0)
damping_min = 3.0
damping_max = 5.0
scale_min = 0.5
scale_max = 0.5
scale_curve = SubResource("CurveTexture_hyvgs")
color = Color(2.5, 1.34, 0.271, 1)
alpha_curve = SubResource("CurveTexture_dfo4r")
turbulence_noise_strength = 0.1
turbulence_noise_speed_random = 0.1
turbulence_influence_min = 0.05

[sub_resource type="QuadMesh" id="QuadMesh_sh0yt"]

[sub_resource type="Animation" id="Animation_p5psq"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("AuraCylinder:emitting")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("AuraCylinder2:emitting")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("LittleParticles:emitting")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_5rwps"]
resource_name = "default"
length = 2.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("AuraCylinder:emitting")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("AuraCylinder2:emitting")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("LittleParticles:emitting")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_nm8rl"]
_data = {
&"RESET": SubResource("Animation_p5psq"),
&"default": SubResource("Animation_5rwps")
}

[node name="VFX_PowerUp" type="Node3D"]
script = ExtResource("1_h2ngo")

[node name="AuraCylinder" type="GPUParticles3D" parent="."]
material_override = SubResource("ShaderMaterial_73v70")
cast_shadow = 0
emitting = false
amount = 1
one_shot = true
local_coords = true
process_material = SubResource("ParticleProcessMaterial_0hx7y")
draw_pass_1 = ExtResource("3_nm8rl")

[node name="AuraCylinder2" type="GPUParticles3D" parent="."]
material_override = SubResource("ShaderMaterial_hrx22")
cast_shadow = 0
emitting = false
amount = 1
one_shot = true
local_coords = true
process_material = SubResource("ParticleProcessMaterial_27u1n")
draw_pass_1 = ExtResource("3_nm8rl")

[node name="LittleParticles" type="GPUParticles3D" parent="."]
material_override = SubResource("ShaderMaterial_8cdnn")
emitting = false
amount = 20
one_shot = true
explosiveness = 0.62
randomness = 1.0
local_coords = true
process_material = SubResource("ParticleProcessMaterial_w7wss")
draw_pass_1 = SubResource("QuadMesh_sh0yt")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_nm8rl")
}
