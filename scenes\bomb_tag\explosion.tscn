[gd_scene load_steps=22 format=3 uid="uid://bc13xpsu2fpb5"]

[ext_resource type="Script" uid="uid://pddyjx03x3qc" path="res://scripts/BombTag_game/explosion.gd" id="1_it0pn"]
[ext_resource type="AudioStream" uid="uid://dt78lotwx34fo" path="res://explosion.ogg" id="1_q7epf"]

[sub_resource type="Curve" id="Curve_7rj46"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(0.655431, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_q7epf"]
curve = SubResource("Curve_7rj46")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_vxas0"]
particle_flag_align_y = true
spread = 180.0
initial_velocity_min = 12.0
initial_velocity_max = 14.0
scale_min = 0.5
scale_max = 1.5
scale_curve = SubResource("CurveTexture_q7epf")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_j4sxf"]
albedo_color = Color(1, 0.392157, 0, 1)
emission_enabled = true
emission = Color(1, 0.4, 0, 1)
emission_energy_multiplier = 2.0

[sub_resource type="CylinderMesh" id="CylinderMesh_m5xho"]
material = SubResource("StandardMaterial3D_j4sxf")
top_radius = 0.1
bottom_radius = 0.0

[sub_resource type="Curve" id="Curve_q7epf"]
_data = [Vector2(0, 0.14401), 0.0, 0.0, 0, 0, Vector2(0.603139, 0.82328), 0.0, 0.0, 0, 0, Vector2(1, 0.09983), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_vxas0"]
curve = SubResource("Curve_q7epf")

[sub_resource type="Curve" id="Curve_j4sxf"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.260526, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_m5xho"]
curve = SubResource("Curve_j4sxf")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_kmxf7"]
emission_shape = 1
emission_sphere_radius = 1.0
direction = Vector3(0, -1, 0)
initial_velocity_min = 1.0
initial_velocity_max = 2.0
gravity = Vector3(0, 5, 0)
scale_min = 0.5
scale_max = 1.5
scale_curve = SubResource("CurveTexture_m5xho")
color = Color(0.286275, 0.278431, 0.168627, 1)
alpha_curve = SubResource("CurveTexture_vxas0")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_4lvei"]
transparency = 1
vertex_color_use_as_albedo = true

[sub_resource type="SphereMesh" id="SphereMesh_7rlbx"]
material = SubResource("StandardMaterial3D_4lvei")
radial_segments = 8
rings = 4

[sub_resource type="Gradient" id="Gradient_kmxf7"]
colors = PackedColorArray(1, 0.721569, 0, 1, 1, 0.215686, 0, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_l04k4"]
gradient = SubResource("Gradient_kmxf7")

[sub_resource type="Curve" id="Curve_cf70p"]
_limits = [0.0, 2.0, 0.0, 1.0]
_data = [Vector2(0.00224215, 2), 0.0, 0.0, 0, 0, Vector2(1, 1.01699), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_3xjsk"]
curve = SubResource("Curve_cf70p")

[sub_resource type="Curve" id="Curve_gjpps"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.260526, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_ew515"]
curve = SubResource("Curve_gjpps")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_6cxdk"]
emission_shape = 1
emission_sphere_radius = 1.0
direction = Vector3(0, -1, 0)
spread = 90.0
initial_velocity_min = 2.0
initial_velocity_max = 4.0
gravity = Vector3(0, 5, 0)
scale_min = 0.5
scale_max = 1.5
scale_curve = SubResource("CurveTexture_ew515")
color = Color(1, 0.721569, 0, 1)
color_ramp = SubResource("GradientTexture1D_l04k4")
emission_curve = SubResource("CurveTexture_3xjsk")

[node name="Explosion" type="Node3D"]
script = ExtResource("1_it0pn")

[node name="Debris" type="GPUParticles3D" parent="."]
emitting = false
amount = 24
lifetime = 0.5
one_shot = true
explosiveness = 1.0
process_material = SubResource("ParticleProcessMaterial_vxas0")
draw_pass_1 = SubResource("CylinderMesh_m5xho")

[node name="Smoke" type="GPUParticles3D" parent="."]
emitting = false
amount = 12
lifetime = 2.0
one_shot = true
explosiveness = 1.0
fixed_fps = 40
process_material = SubResource("ParticleProcessMaterial_kmxf7")
draw_pass_1 = SubResource("SphereMesh_7rlbx")

[node name="Fire" type="GPUParticles3D" parent="."]
emitting = false
amount = 12
one_shot = true
explosiveness = 1.0
fixed_fps = 40
process_material = SubResource("ParticleProcessMaterial_6cxdk")
draw_pass_1 = SubResource("SphereMesh_7rlbx")

[node name="ExplosionSound" type="AudioStreamPlayer3D" parent="."]
stream = ExtResource("1_q7epf")
volume_db = -5.0
