[gd_scene load_steps=6 format=4 uid="uid://c1d37gy1pj85v"]

[ext_resource type="Material" uid="uid://g5x7g5rmse4o" path="res://scenes/bomb_tag/level/cactus/materials/cactus_body.material" id="1_fd22k"]
[ext_resource type="Material" uid="uid://3oxpnywrvsyc" path="res://scenes/bomb_tag/level/cactus/materials/cactus_flowers.material" id="2_0m811"]

[sub_resource type="ArrayMesh" id="ArrayMesh_uj17j"]
_surfaces = [{
"aabb": AABB(-0.669505, -0.190451, -0.102441, 0.987486, 2.16433, 0.204881),
"format": 34896613377,
"index_count": 936,
"index_data": PackedByteArray("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"),
"lods": [0.0197468, PackedByteArray("DwAMAA4AAAAPAA4ADwANAAwAAAABAA8AAgABAAAADQAKAAwADQALAAoACwAIAAoACwAJAAgACQAGAAgACQAHAAYAAgADAAEABQADAAIABQACAAQABgAFAAQABgAHAAUAAgAZACEAGQAaACEABAACACEAAgAAABkABAAhACAAIQAaACAABgAEACAAAAAbABkAGgAZABsAAAAOABsABgAgAB8AIAAaAB8ACAAGAB8ADgAcABsAHAAaABsADgAMABwACAAfAB4AHwAaAB4ACgAIAB4ADAAdABwADAAKAB0ACgAeAB0AHQAaABwAHgAaAB0AWQBjAGIAYwA4AGIAOABpAGIAaQBZAGIAYwAqADgAOAA2AGkALAAqAGMANgBoAGkALABjAGUALgAsAGUAYwBTAGUAZQBTAFUANgA0AGgAMAAuAGUAMABlAGYAZQBVAGYAVQBTAFoAVQBaAGAAWgAvAGAAWgAtAC8AWgArAC0AUwBSAFoAUgArAFoAUgApACsANwApAFIAYwBSAFMAWQBSAGMANwBSAFkAYQA3AFkAaQBhAFkAYQA1ADcAWwA1AGEAWwAzADUAVgAzAFsAVgAxADMAVwBWAFsAYQBXAFsAaABXAGEAaABhAGkAZwBXAGgANABnAGgANAAyAGcAZwBWAFcAMgBmAGcAZgBWAGcAMgAwAGYAZgBVAFYAVgBVAGAAYAAxAFYAYAAvADEAKwBCAEoAQgBDAEoALQArAEoAKwApAEIALQBKAEkASgBDAEkALwAtAEkAKQBEAEIAQwBCAEQAKQA3AEQALwBJAEgASQBDAEgAMQAvAEgANwBFAEQARQBDAEQANwA1AEUAMQBIAEcASABDAEcAMwAxAEcANQBGAEUANQAzAEYAMwBHAEYARgBDAEUARwBDAEYAkgCcAJsAnACqAJsAnACjAKoApACjAJwAqgCiAJsAogCSAJsAqgCpAKIAkgCLAJwAnACLAIwAjACLAJMAiwBrAJMAiwBqAGsAcQBqAIsAcQCLAJIAmgBxAJIAogCaAJIAmgBwAHEAoQCaAKIAqQChAKIAqQCoAKEAqACgAKEAqACnAKAAoACQAKEAoQCQAJoApwCfAKAApwCmAJ8AnwCPAKAAoACPAJAAnwCOAI8AjwCOAJkAmQBuAI8AmQBtAG4AmgCQAJQAlABwAJoAkACPAJQAlABvAHAAjwBuAG8AjwBvAJQAawB7AIMAewB8AIMAbABrAIMAawBqAHsAbACDAIIAgwB8AIIAbQBsAIIAagB9AHsAfAB7AH0AagBxAH0AbQCCAIEAggB8AIEAbgBtAIEAcQB+AH0AfgB8AH0AcQBwAH4AbgCBAIAAgQB8AIAAbwBuAIAAcAB/AH4AcABvAH8AbwCAAH8AfwB8AH4AgAB8AH8AjgCTAJkAkwBtAJkAkwBsAG0AkwBrAGwAjgCMAJMAngCMAI4AngCOAJ8ApgCeAJ8ApgClAJ4ApQCkAJ4ApACcAJ4AnACMAJ4A"), 0.254083, PackedByteArray("DwANAAoADQALAAoACgABAA8ABAABAAoACwAJAAoACQAGAAoACQAHAAYABAAZACEAGQAaACEABAAhACAAIQAaACAABgAEACAAGgAZABsABgAgAB8AIAAaAB8ACgAGAB8ACgAfAB4AHwAaAB4ACgAeAB0AHgAaAB0ACgAdABwAHQAaABwAHAAaABsACgAcABsABAAKABkABgAFAAQABgAHAAUABQADAAQABAADAAEACgAbABkALQBCAEoAQgBDAEoALQBKAEkASgBDAEkALwAtAEkALwBJAEgASQBDAEgAQwBCAEQALQBEAEIASABDAEcANQBIAEcARwBDAEYANQBHAEYANQAvAEgANQBGAEUARgBDAEUARQBDAEQANQBFAEQALQA1AEQALAA1AGYAZgAtAC8ANQAtAGYANQAqADgALAAqADUANABmADUANgA0ADUAOAA2ADUANAAyAGYAMgAwAGYAMAAuAGYALgAsAGYAZgAvADUAbAB7AIMAewB8AIMAbACDAIIAgwB8AIIAbQBsAIIAbQCCAIEAggB8AIEAfAB7AH0AbAB9AHsAgQB8AIAAcACBAIAAgAB8AH8AcACAAH8AcAB+AH0AcAB/AH4AfgB8AH0AfwB8AH4AcABtAIEAbABwAH0ApACjAKoAqgBsAJ8ApgClAKQAqACfAKkAqACnAJ8ApwCmAJ8AnwBwAKoAcABsAKoApgCkAJ8AnwBsAG0AnwBtAHAAnwCqAKkAqgCfAKQA")],
"name": "Plant_2",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 171,
"vertex_data": PackedByteArray("j6Lv8AAAAACPogAAAAAAAAGT7/D6SgAAAZMAAPpKAAABk+/wBLUAAAGTAAAEtQAAj6Lv8P//AACPogAA//8AAJC47/D//wAAkLgAAP//AAAeyO/wBLUAAB7IAAAEtQAAHsjv8PpKAAAeyAAA+koAAJC47/AAAAAAkLgAAAAAAADFtzP1OAkAADTGM/XMTgAANMYz9TKxAADFtzP1xvYAAFqjM/XG9gAA65Qz9TKxAADrlDP1zE4AAFqjM/U4CQAAnKXQ+HsjAAD9qDr7yEoAAJCtE/z/fwAAIrI6+8hKAACauDr79GkAAJq4OvsKlgAAIrI6+za1AAD9qDr7NrUAAIWiOvsKlgAAhaI6+/RpAABemtD4rVkAAF6a0PhRpgAAnKXQ+IPcAACDtdD4g9wAAMHA0PhRpgAAwcDQ+K1ZAACDtdD4eyMAAJQI2vkNOQAAIKZzrA05AAAAANr5ZmIAALqliqhmYgAAAADa+eCcAAC6pYqo4JwAAJQI2vk6xgAAIKZzrDrGAAC2FNr5OsYAALCm+rE6xgAASh3a+eCcAAAWp+O14JwAAEod2vlmYgAAFqfjtWZiAAC2FNr5DTkAALCm+rENOQAARhQ0/CI+AAA8HDT8gWQAADwcNPzFmgAARhQ0/CTBAAAECTT8JMEAAA4BNPzFmgAADgE0/IFkAAAECTT8Ij4AAEIKMv6eTAAAHwyH/0piAAClDv//o38AACoRh/9KYgAAvBSH/3tzAAC8FIf/y4sAACoRh//8nAAAHwyH//ycAACOCIf/y4sAAI4Ih/97cwAADwQy/oFqAAAPBDL+xZQAAEIKMv6psgAABxMy/qmyAAA7GTL+xZQAADsZMv6BagAABxMy/p5MAAA4Dj65DTkAAA4HF7dmYgAADgcXt+CcAAA4Dj65OsYAAFoYSbw6xgAAhR9wvuCcAACFH3C+ZmIAAFoYSbwNOQAAAABRxOCcAABKHVHE4JwAAAAAUcRmYgAAthRRxDrGAAC2FFHEDTkAAJQIUcQNOQAAlAhRxDrGAABKHVHEZmIAAEgskbQNOQAAuCsKrw05AABSKyGrZmIAAFIrIavgnAAAuCsKrzrGAABILJG0OsYAAK0serjgnAAArSx6uGZiAABq9wzHV8UAAP7/DMf9mwAA//8Mx4NhAABq9wzHKjgAAEjrDMcqOAAAtOIMx4NhAAC04gzH/ZsAAEjrDMdXxQAAuOtlyUHAAADC42XJ4pkAAMLjZcmeYwAAuOtlyUA9AAD69mXJQD0AAPD+ZcmeYwAA8P5lyeKZAAD69mXJQcAAALz1ZMvGsQAA3/O5zBmcAABZ8TDNwH4AANTuucwZnAAAQuu5zOiKAABC67nMmHIAANTuucxnYQAA3/O5zGdhAABw97nMmHIAAHD3uczoigAA7/tky+KTAADv+2TLnmkAALz1ZMu7SwAA9+xky7tLAADD5mTLnmkAAMPmZMvikwAA9+xky8axAADG8W+GV8UAAPD4SIT9mwAA8PhIhINhAADG8W+GKjgAAKTne4kqOAAAeeCii4NhAAB54KKL/ZsAAKTne4lXxQAA/v+DkYNhAAC04oORg2EAAP7/g5H9mwAASOuDkSo4AABI64ORV8UAAGr3g5FXxQAAaveDkSo4AAC04oOR/ZsAALbTw4FXxQAARtQ8fFfFAACs1FN4/ZsAAKzUU3iDYQAARtQ8fCo4AAC208OBKjgAAFHTrIWDYQAAUdOshf2bAACTsoV7V8UAAPmynHf9mwAA+bKcd4NhAACTsoV7KjgAAASyDYEqOAAAnrH2hINhAACesfaE/ZsAAASyDYFXxQAA")
}, {
"aabb": AABB(-0.706003, -4.76837e-07, -0.168799, 1.06048, 2.0004, 0.336702),
"format": 34896613377,
"index_count": 1536,
"index_data": PackedByteArray("DAAAAAEADAAKAAAAAwAEAAUAAwACAAQABQAHAAkABQAEAAcAsQCuALAAsQCvAK4AUQBOAFAAUQBPAE4ADQAKAAwADQALAAoAFQAQAA4AFQAUABAAFgASABMAFgAXABIAEQAUABUAEQAPABQADgAXABYADgAQABcAJAAYABkAJAAiABgAGwAcAB0AGwAaABwAHQAfACEAHQAcAB8ACQAGAAgACQAHAAYACAALAA0ACAAGAAsAJQAiACQAJQAjACIALQAoACYALQAsACgALgAqACsALgAvACoAKQAsAC0AKQAnACwAJgAvAC4AJgAoAC8APAAwADEAPAA6ADAAMwA0ADUAMwAyADQANQA3ADkANQA0ADcAIQAeACAAIQAfAB4AIAAjACUAIAAeACMAPQA6ADwAPQA7ADoARQBAAD4ARQBEAEAARgBCAEMARgBHAEIAQQBEAEUAQQA/AEQAPgBHAEYAPgBAAEcAVABIAEkAVABSAEgASwBMAE0ASwBKAEwATQBPAFEATQBMAE8AOQA2ADgAOQA3ADYAOAA7AD0AOAA2ADsAVQBSAFQAVQBTAFIAXQBYAFYAXQBcAFgAXgBaAFsAXgBfAFoAWQBcAF0AWQBXAFwAVgBfAF4AVgBYAF8AbABgAGEAbABqAGAAYwBkAGUAYwBiAGQAZQBnAGkAZQBkAGcAUABTAFUAUABOAFMAbQBqAGwAbQBrAGoAdQBwAG4AdQB0AHAAdgByAHMAdgB3AHIAcQB0AHUAcQBvAHQAbgB3AHYAbgBwAHcAhAB4AHkAhACCAHgAewB8AH0AewB6AHwAfQB/AIEAfQB8AH8AaQBmAGgAaQBnAGYAaABrAG0AaABmAGsAhQCCAIQAhQCDAIIAjQCIAIYAjQCMAIgAjgCKAIsAjgCPAIoAiQCMAI0AiQCHAIwAhgCPAI4AhgCIAI8AnACQAJEAnACaAJAAkwCUAJUAkwCSAJQAlQCXAJkAlQCUAJcAgQB+AIAAgQB/AH4AgACDAIUAgAB+AIMAnQCaAJwAnQCbAJoApQCgAJ4ApQCkAKAApgCiAKMApgCnAKIAoQCkAKUAoQCfAKQAngCnAKYAngCgAKcAtACoAKkAtACyAKgAqwCsAK0AqwCqAKwArQCvALEArQCsAK8AmQCWAJgAmQCXAJYAmACbAJ0AmACWAJsAtQCyALQAtQCzALIAvQC4ALYAvQC8ALgAvgC6ALsAvgC/ALoAuQC8AL0AuQC3ALwAtgC/AL4AtgC4AL8AsACzALUAsACuALMAzADAAMEAzADKAMAAwwDEAMUAwwDCAMQAxQDHAMkAxQDEAMcAyQCTAZsByQDHAJMByACGAYkByADGAIYBzQDKAMwAzQDLAMoA1QDQAM4A1QDUANAA1gDSANMA1gDXANIA0QDUANUA0QDPANQAzgDXANYAzgDQANcA5ADYANkA5ADiANgA2wDcAN0A2wDaANwA3QDfAOEA3QDcAN8A4QCWAZ4B4QDfAJYB4ACKAY0B4ADeAIoB5QDiAOQA5QDjAOIA7QDoAOYA7QDsAOgA7gDqAOsA7gDvAOoA6QDsAO0A6QDnAOwA5gDvAO4A5gDoAO8A/ADwAPEA/AD6APAA8wD0APUA8wDyAPQA9QD3APkA9QD0APcA+QCZAZEB+QD3AJkB+ACOAYAB+AD2AI4B/QD6APwA/QD7APoABQEAAf4ABQEEAQABBgECAQMBBgEHAQIBAQEEAQUBAQH/AAQB/gAHAQYB/gAAAQcBFAEIAQkBFAESAQgBCwEMAQ0BCwEKAQwBDQEPAREBDQEMAQ8BEQGcAZQBEQEPAZwBEAGBAYIBEAEOAYEBFQESARQBFQETARIBHQEYARYBHQEcARgBHgEaARsBHgEfARoBGQEcAR0BGQEXARwBFgEfAR4BFgEYAR8BLAEgASEBLAEqASABIwEkASUBIwEiASQBJQEnASkBJQEkAScBKQGfAZcBKQEnAZ8BKAGDAYQBKAEmAYMBLQEqASwBLQErASoBNQEwAS4BNQE0ATABNgEyATMBNgE3ATIBMQE0ATUBMQEvATQBLgE3ATYBLgEwATcBRAE4ATkBRAFCATgBOwE8AT0BOwE6ATwBPQE/AUEBPQE8AT8BQQGSAZoBQQE/AZIBQAGFAYcBQAE+AYUBRQFCAUQBRQFDAUIBTQFIAUYBTQFMAUgBTgFKAUsBTgFPAUoBSQFMAU0BSQFHAUwBRgFPAU4BRgFIAU8BXAFQAVEBXAFaAVABUwFUAVUBUwFSAVQBVQFXAVkBVQFUAVcBWQGVAZ0BWQFXAZUBWAGIAYsBWAFWAYgBXQFaAVwBXQFbAVoBZQFgAV4BZQFkAWABZgFiAWMBZgFnAWIBYQFkAWUBYQFfAWQBXgFnAWYBXgFgAWcBdAFoAWkBdAFyAWgBawFsAW0BawFqAWwBbQFvAXEBbQFsAW8BcQGYAZABcQFvAZgBcAGMAY8BcAFuAYwBdQFyAXQBdQFzAXIBfQF4AXYBfQF8AXgBfgF6AXsBfgF/AXoBeQF8AX0BeQF3AXwBdgF/AX4BdgF4AX8BjAF1AY8BjAFzAXUBiAFdAYsBiAFbAV0BhQFFAYcBhQFDAUUBgwEtAYQBgwErAS0BgQEVAYIBgQETARUBjgH9AIABjgH7AP0AigHlAI0BigHjAOUAhgHNAIkBhgHLAM0AnAEQAZQBnAEOARABmQH4AJEBmQH2APgAlgHgAJ4BlgHeAOAAkwHIAJsBkwHGAMgAmAFwAZABmAFuAXABlQFYAZ0BlQFWAVgBkgFAAZoBkgE+AUABnwEoAZcBnwEmASgBqAGgAaEBqAGmAaABpQFTAlsCpQGjAVMCpAFGAkkCpAGiAUYCqQGmAagBqQGnAaYBsQGsAaoBsQGwAawBsgGuAa8BsgGzAa4BrQGwAbEBrQGrAbABqgGzAbIBqgGsAbMBvAG0AbUBvAG6AbQBuQFWAl4CuQG3AVYCuAFKAk0CuAG2AUoCvQG6AbwBvQG7AboBxQHAAb4BxQHEAcABxgHCAcMBxgHHAcIBwQHEAcUBwQG/AcQBvgHHAcYBvgHAAccB0AHIAckB0AHOAcgBzQFZAlECzQHLAVkCzAFOAkACzAHKAU4C0QHOAdAB0QHPAc4B2QHUAdIB2QHYAdQB2gHWAdcB2gHbAdYB1QHYAdkB1QHTAdgB0gHbAdoB0gHUAdsB5AHcAd0B5AHiAdwB4QFcAlQC4QHfAVwC4AFBAkIC4AHeAUEC5QHiAeQB5QHjAeIB7QHoAeYB7QHsAegB7gHqAesB7gHvAeoB6QHsAe0B6QHnAewB5gHvAe4B5gHoAe8B+AHwAfEB+AH2AfAB9QFfAlcC9QHzAV8C9AFDAkQC9AHyAUMC+QH2AfgB+QH3AfYBAQL8AfoBAQIAAvwBAgL+Af8BAgIDAv4B/QEAAgEC/QH7AQAC+gEDAgIC+gH8AQMCDAIEAgUCDAIKAgQCCQJSAloCCQIHAlICCAJFAkcCCAIGAkUCDQIKAgwCDQILAgoCFQIQAg4CFQIUAhACFgISAhMCFgIXAhICEQIUAhUCEQIPAhQCDgIXAhYCDgIQAhcCIAIYAhkCIAIeAhgCHQJVAl0CHQIbAlUCHAJIAksCHAIaAkgCIQIeAiACIQIfAh4CKQIkAiICKQIoAiQCKgImAicCKgIrAiYCJQIoAikCJQIjAigCIgIrAioCIgIkAisCNAIsAi0CNAIyAiwCMQJYAlACMQIvAlgCMAJMAk8CMAIuAkwCNQIyAjQCNQIzAjICPQI4AjYCPQI8AjgCPgI6AjsCPgI/AjoCOQI8Aj0COQI3AjwCNgI/Aj4CNgI4Aj8CTAI1Ak8CTAIzAjUCSAIhAksCSAIfAiECRQINAkcCRQILAg0CQwL5AUQCQwL3AfkBQQLlAUICQQLjAeUBTgLRAUACTgLPAdEBSgK9AU0CSgK7Ab0BRgKpAUkCRgKnAakBXALgAVQCXALeAeABWQLMAVECWQLKAcwBVgK4AV4CVgK2AbgBUwKkAVsCUwKiAaQBWAIwAlACWAIuAjACVQIcAl0CVQIaAhwCUgIIAloCUgIGAggCXwL0AVcCXwLyAfQB"),
"name": "Plant_1",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 608,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_8lqpd"]
resource_name = "CanyonPlantsRocksTiles_Cactus_Tall_5"
_surfaces = [{
"aabb": AABB(-0.669505, -0.190451, -0.102441, 0.987486, 2.16433, 0.204881),
"attribute_data": PackedByteArray("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"),
"format": 34896613399,
"index_count": 936,
"index_data": PackedByteArray("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"),
"lods": [0.0197468, PackedByteArray("VAFQARcAPgFUARcAVAFSAVABPgFAAVQBQgFAAT4BUgFNAVABUgFPAU0BTwFLAU0BTwEQAEsBEABHAUsBEABKAUcBQQEFAEABRgEFAEEBRgFBAUQBSAFGAUQBSAENAEYBQwFVAVwBVQEmAFwBRQFDAVwBQwE/AVUBRQFcAVsBXAEkAFsBSQFFAVsBPwFWAVUBKABVAVYBPwFTAVYBSQFbAVoBWwElAFoBTAFJAVoBUwFXAVYBVwEqAFYBUwFRAVcBTAFaAVkBWgEnAFkBTgFMAVkBUQFYAVcBUQFOAVgBTgFZAVgBWAErAFcBWQEpAFgBggGJAYYAiQFxAYYAcQGQAYYAkAGCAYYAiQE8AHEBcQFuAZABYQE8AIkBbgGPAZABYQGKAYsBQgBhAYsBiQF8AYsBiwF8AX4BbgFrAY8BZQFCAIwBZQGMAYsAjAF+AYsAfgF8AYMBfgGDAYQAgwFjAYQAgwFAAGMBgwFfAUAAfAF7AYQBewFfAYQBegFdAV8BbwFdAXoBiQF6AX0BggF6AYkBbwF6AYIBiAFvAYIBkAGHAYIBhwFsAW8BhQFsAYcBhQFpAWwBgAFpAYUBgAFmAWkBgQGAAYUBhwGBAYUBjwGBAYcBjwGHAZEBjgGBAY8BawGOAY8BawFoAY4BjgF/AYEBaAGNAY4BjQF/AY4BaAFGAI0BjQF3AH8BfwF3AIYBhgFmAX8BhgFDAGYBYAFyAXkBcgFgAHkBYgFgAXkBYAFeAXIBYgF5AXgBeQFeAHgBZAFiAXgBXgFzAXIBYgByAXMBXgFwAXMBZAF4AXcBeAFfAHcBZwFkAXcBcAF0AXMBdAFkAHMBcAFtAXQBZwF3AXYBdwFhAHYBagFnAXYBbQF1AXQBbQFqAXUBagF2AXUBdQFlAHQBdgFjAHUBsQG5AbgBuQHLAbgBuQHDAcsBxAHDAbkBywHCAbgBwgGxAbgBywHKAcIBsQGoAbkBuQGoAawBrAGoAbMBqgGTAbMBqAGPAJQBngGPAKgBngGpAbEBtwGeAbEBwgG2AbEBtgGcAJ4BwAG2AcIBygHAAcIBygHJAcAByQG/AcAByQHIAb8BvwGwAcABwQGwAbYByAG9Ab8ByAHGAb0BvQGuAb8BvwGuAbABvQHDAK4BrgHDALUBtQGaAa4BtQGVAJoBtgGwAbQBtAGcALYBsAGvAbQBtAGaAJwArwGaAZoArwGaALQBlQGgAacBoAGsAKcBlwGVAacBlQGSAaABlwGnAaYBpwGqAKYBmQGXAaYBkgGhAaABrgCgAaEBkgGfAaEBmQGmAaUBpgGrAKUBmwGZAaUBnwGiAaEBogGwAKEBnwGdAaIBmwGlAaQBpQGtAKQBnAGbAaQBnQGjAaIBnQGcAaMBnAGkAaMBowGxAKIBpAGvAKMBrQGyAdAAsgGYAdAAsgGWAZgBsgGTAZYBrQGrAbIBuwGrAa0BvAGtAb4BxwG8Ab4BxwHdALwB3QDFAbsBxAG6AbsBugGsAbsB"), 0.254083, PackedByteArray("+AD3APEA9wATAPEA8wDkAPgA6ADkAPMAEwDwAPIA8ADsAPIA8ADvAOwA6QD5ADIA+QAmADIA6QAyAP0AMgAkAP0A7gDpAP0AKAD5APsA7gD9APwA/QAlAPwA9QDuAPwA9QD8AC8A/AAnAC8A9QAvAC4ALwApAC4A9QAuAC0ALgArAC0ALQAqAPsA9gAtAPsA6gD0APoA7QDrAOcA7QANAOsA6wDmAOcA5wDmAOUAEgAsACMAAwFdAGwAXQBgAGwAAwFsABQBbABeABQBBwEDARQBBwEUAWoAFAFfAGoAYgBdAGYAQQBmAF0AagBhAGkATwBqAGkAaQBjAGgADgFpAGgADgEIARMBDgFoAGcAaABlAGcAZwBkAGYADwFnAGYABAEQARIBPwBOAIsAiwABAUQATgACAYsATgA8AFMA/wD+AE4ATQCKAE4AUABNAE4AEQFQAE4ADAEKARUBCwEJARUBRwAFARcBBQEAARcBFgEGAQ0BGwGpALgAqQCsALgAGwG4ACUBuACqACUBHgEbASUBHgElAbYAJQGrALYArgCpALIAlACyAKkAtgCtALUAnQC2ALUAtQCvALQAIQG1ALQAIQGzALIAIQG0ALMAswCwALIAtACxALMAIQEfASQBHAEiASMBMAEvATsBOwEaAS0BNQEzATABNwEmATkBOAE2AScBNgHeACgBJwGcAOMAnAAYAeMANAExASsBLAEZAZYAKQEdASABKgE8AToBPQEuATIB")],
"material": ExtResource("1_fd22k"),
"name": "Plant_2",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 460,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.706003, -4.76837e-07, -0.168799, 1.06048, 2.0004, 0.336702),
"attribute_data": PackedByteArray("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"),
"format": 34896613399,
"index_count": 1536,
"index_data": PackedByteArray("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"),
"material": ExtResource("2_0m811"),
"name": "Plant_1",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 1024,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_uj17j")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_boa8u"]
data = PackedVector3Array(0.0424324, -0.190451, -0.102441, 0.102441, 1.84657, -0.0424324, 0.0424324, 1.84657, -0.102441, 0.0424324, -0.190451, -0.102441, 0.102441, -0.190451, -0.0424324, 0.102441, 1.84657, -0.0424324, -0.0424324, 1.84657, -0.102441, 0.03067, 1.91317, -0.0740441, -0.03067, 1.91317, -0.0740441, -0.0424324, 1.84657, -0.102441, 0.0424324, 1.84657, -0.102441, 0.03067, 1.91317, -0.0740441, -0.0424324, 1.84657, 0.102441, -0.102441, -0.190451, 0.0424324, -0.102441, 1.84657, 0.0424324, -0.0424324, 1.84657, 0.102441, -0.0424324, -0.190451, 0.102441, -0.102441, -0.190451, 0.0424324, -0.102441, 1.84657, -0.0424324, -0.03067, 1.91317, -0.0740441, -0.0740441, 1.91317, -0.03067, -0.102441, 1.84657, -0.0424324, -0.0424324, 1.84657, -0.102441, -0.03067, 1.91317, -0.0740441, 0.0424324, -0.190451, 0.102441, -0.0424324, 1.84657, 0.102441, 0.0424324, 1.84657, 0.102441, 0.0424324, -0.190451, 0.102441, -0.0424324, -0.190451, 0.102441, -0.0424324, 1.84657, 0.102441, -0.102441, 1.84657, 0.0424324, -0.0740441, 1.91317, -0.03067, -0.0740441, 1.91317, 0.03067, -0.102441, 1.84657, 0.0424324, -0.102441, 1.84657, -0.0424324, -0.0740441, 1.91317, -0.03067, -0.0424324, 1.84657, 0.102441, -0.0740441, 1.91317, 0.03067, -0.03067, 1.91317, 0.0740441, -0.0424324, 1.84657, 0.102441, -0.102441, 1.84657, 0.0424324, -0.0740441, 1.91317, 0.03067, 0.102441, -0.190451, 0.0424324, 0.0424324, 1.84657, 0.102441, 0.102441, 1.84657, 0.0424324, 0.102441, -0.190451, 0.0424324, 0.0424324, -0.190451, 0.102441, 0.0424324, 1.84657, 0.102441, 0.0424324, 1.84657, 0.102441, -0.03067, 1.91317, 0.0740441, 0.03067, 1.91317, 0.0740441, 0.0424324, 1.84657, 0.102441, -0.0424324, 1.84657, 0.102441, -0.03067, 1.91317, 0.0740441, 0.102441, -0.190451, -0.0424324, 0.102441, 1.84657, 0.0424324, 0.102441, 1.84657, -0.0424324, 0.102441, -0.190451, -0.0424324, 0.102441, -0.190451, 0.0424324, 0.102441, 1.84657, 0.0424324, 0.102441, 1.84657, 0.0424324, 0.03067, 1.91317, 0.0740441, 0.0740441, 1.91317, 0.03067, 0.102441, 1.84657, 0.0424324, 0.0424324, 1.84657, 0.102441, 0.03067, 1.91317, 0.0740441, -0.0424324, 1.84657, -0.102441, 0.0424324, -0.190451, -0.102441, 0.0424324, 1.84657, -0.102441, -0.0424324, 1.84657, -0.102441, -0.0424324, -0.190451, -0.102441, 0.0424324, -0.190451, -0.102441, 0.102441, 1.84657, -0.0424324, 0.0740441, 1.91317, 0.03067, 0.0740441, 1.91317, -0.03067, 0.102441, 1.84657, -0.0424324, 0.102441, 1.84657, 0.0424324, 0.0740441, 1.91317, 0.03067, 0, 1.94075, 0, -0.03067, 1.91317, -0.0740441, 0.03067, 1.91317, -0.0740441, 0.0740441, 1.91317, 0.03067, 0, 1.94075, 0, 0.0740441, 1.91317, -0.03067, 0.238087, 1.4924, 0.0554974, 0.220654, 1.52913, 0.0159173, 0.244574, 1.52913, 0.0398373, 0.238087, 1.4924, 0.0554974, 0.204994, 1.4924, 0.022404, 0.220654, 1.52913, 0.0159173, -0.0740441, 1.91317, -0.03067, -0.03067, 1.91317, -0.0740441, 0, 1.94075, 0, -0.0740441, 1.91317, -0.03067, 0, 1.94075, 0, -0.0740441, 1.91317, 0.03067, -0.0740441, 1.91317, 0.03067, 0, 1.94075, 0, -0.03067, 1.91317, 0.0740441, -0.03067, 1.91317, 0.0740441, 0, 1.94075, 0, 0.03067, 1.91317, 0.0740441, 0.03067, 1.91317, 0.0740441, 0, 1.94075, 0, 0.0740441, 1.91317, 0.03067, 0.0740441, 1.91317, -0.03067, 0, 1.94075, 0, 0.03067, 1.91317, -0.0740441, -0.102441, -0.190451, 0.0424324, -0.102441, 1.84657, -0.0424324, -0.102441, 1.84657, 0.0424324, -0.102441, -0.190451, 0.0424324, -0.102441, -0.190451, -0.0424324, -0.102441, 1.84657, -0.0424324, -0.102441, 1.84657, -0.0424324, -0.0424324, -0.190451, -0.102441, -0.0424324, 1.84657, -0.102441, -0.102441, 1.84657, -0.0424324, -0.102441, -0.190451, -0.0424324, -0.0424324, -0.190451, -0.102441, -0.575557, 1.40143, -0.0567811, -0.500862, 1.28944, -0.0567811, -0.498693, 1.33619, -0.0567811, -0.575557, 1.40143, -0.0567811, -0.614647, 1.37569, -0.0567811, -0.500862, 1.28944, -0.0567811, -0.636411, 1.92194, -0.0567811, -0.596097, 1.95867, -0.041121, -0.629925, 1.95867, -0.041121, -0.636411, 1.92194, -0.0567811, -0.58961, 1.92194, -0.0567811, -0.596097, 1.95867, -0.041121, -0.642288, 1.35749, 0.0231133, -0.669505, 1.46935, -0.0236878, -0.669505, 1.46935, 0.0231133, -0.642288, 1.35749, 0.0231133, -0.642288, 1.35749, -0.0236878, -0.669505, 1.46935, -0.0236878, -0.669505, 1.92194, -0.0236878, -0.629925, 1.95867, -0.041121, -0.653845, 1.95867, -0.0172011, -0.669505, 1.92194, -0.0236878, -0.636411, 1.92194, -0.0567811, -0.629925, 1.95867, -0.041121, -0.614647, 1.37569, 0.0562067, -0.669505, 1.46935, 0.0231133, -0.636411, 1.46935, 0.0562067, -0.614647, 1.37569, 0.0562067, -0.642288, 1.35749, 0.0231133, -0.669505, 1.46935, 0.0231133, -0.669505, 1.92194, 0.0231133, -0.653845, 1.95867, -0.0172011, -0.653845, 1.95867, 0.0166266, -0.669505, 1.92194, 0.0231133, -0.669505, 1.92194, -0.0236878, -0.653845, 1.95867, -0.0172011, -0.575557, 1.40143, 0.0562067, -0.636411, 1.46935, 0.0562067, -0.58961, 1.46935, 0.0562067, -0.575557, 1.40143, 0.0562067, -0.614647, 1.37569, 0.0562067, -0.636411, 1.46935, 0.0562067, -0.636411, 1.92194, 0.0562067, -0.653845, 1.95867, 0.0166266, -0.629925, 1.95867, 0.0405465, -0.636411, 1.92194, 0.0562067, -0.669505, 1.92194, 0.0231133, -0.653845, 1.95867, 0.0166266, -0.547916, 1.41962, 0.0231134, -0.58961, 1.46935, 0.0562067, -0.556517, 1.46935, 0.0231133, -0.547916, 1.41962, 0.0231134, -0.575557, 1.40143, 0.0562067, -0.58961, 1.46935, 0.0562067, -0.58961, 1.92194, 0.0562067, -0.629925, 1.95867, 0.0405465, -0.596097, 1.95867, 0.0405465, -0.58961, 1.92194, 0.0562067, -0.636411, 1.92194, 0.0562067, -0.629925, 1.95867, 0.0405465, -0.547916, 1.41962, -0.0236878, -0.556517, 1.46935, 0.0231133, -0.556517, 1.46935, -0.0236878, -0.547916, 1.41962, -0.0236878, -0.547916, 1.41962, 0.0231134, -0.556517, 1.46935, 0.0231133, -0.556517, 1.92194, 0.0231133, -0.596097, 1.95867, 0.0405465, -0.572177, 1.95867, 0.0166266, -0.556517, 1.92194, 0.0231133, -0.58961, 1.92194, 0.0562067, -0.596097, 1.95867, 0.0405465, -0.575557, 1.40143, -0.0567811, -0.556517, 1.46935, -0.0236878, -0.58961, 1.46935, -0.0567811, -0.575557, 1.40143, -0.0567811, -0.547916, 1.41962, -0.0236878, -0.556517, 1.46935, -0.0236878, -0.556517, 1.92194, -0.0236878, -0.572177, 1.95867, 0.0166266, -0.572177, 1.95867, -0.0172011, -0.556517, 1.92194, -0.0236878, -0.556517, 1.92194, 0.0231133, -0.572177, 1.95867, 0.0166266, -0.613011, 1.97388, -0.000287239, -0.629925, 1.95867, -0.041121, -0.596097, 1.95867, -0.041121, -0.572177, 1.95867, 0.0166266, -0.613011, 1.97388, -0.000287239, -0.572177, 1.95867, -0.0172011, -0.653845, 1.95867, -0.0172011, -0.629925, 1.95867, -0.041121, -0.613011, 1.97388, -0.000287239, -0.653845, 1.95867, -0.0172011, -0.613011, 1.97388, -0.000287239, -0.653845, 1.95867, 0.0166266, -0.653845, 1.95867, 0.0166266, -0.613011, 1.97388, -0.000287239, -0.629925, 1.95867, 0.0405465, -0.629925, 1.95867, 0.0405465, -0.613011, 1.97388, -0.000287239, -0.596097, 1.95867, 0.0405465, -0.596097, 1.95867, 0.0405465, -0.613011, 1.97388, -0.000287239, -0.572177, 1.95867, 0.0166266, -0.572177, 1.95867, -0.0172011, -0.613011, 1.97388, -0.000287239, -0.596097, 1.95867, -0.041121, -0.642288, 1.35749, -0.0236878, -0.636411, 1.46935, -0.0567811, -0.669505, 1.46935, -0.0236878, -0.642288, 1.35749, -0.0236878, -0.614647, 1.37569, -0.0567811, -0.636411, 1.46935, -0.0567811, -0.0302178, 1.23447, -0.0236877, -0.500862, 1.28944, -0.0567811, -0.502395, 1.25638, -0.0236878, -0.0302178, 1.23447, -0.0236877, -0.0286842, 1.26753, -0.056781, -0.500862, 1.28944, -0.0567811, -0.0265153, 1.31428, -0.0567809, -0.497159, 1.36924, -0.0236877, -0.498693, 1.33619, -0.0567811, -0.0265153, 1.31428, -0.0567809, -0.0249817, 1.34734, -0.0236877, -0.497159, 1.36924, -0.0236877, -0.0249817, 1.34734, -0.0236877, -0.497159, 1.36924, 0.0231134, -0.497159, 1.36924, -0.0236877, -0.0249817, 1.34734, -0.0236877, -0.0249817, 1.34734, 0.0231135, -0.497159, 1.36924, 0.0231134, -0.0249817, 1.34734, 0.0231135, -0.498693, 1.33619, 0.0562067, -0.497159, 1.36924, 0.0231134, -0.0249817, 1.34734, 0.0231135, -0.0265154, 1.31428, 0.0562068, -0.498693, 1.33619, 0.0562067, -0.0265154, 1.31428, 0.0562068, -0.500862, 1.28944, 0.0562067, -0.498693, 1.33619, 0.0562067, -0.0265154, 1.31428, 0.0562068, -0.0286842, 1.26753, 0.0562068, -0.500862, 1.28944, 0.0562067, -0.0286842, 1.26753, 0.0562068, -0.502395, 1.25638, 0.0231133, -0.500862, 1.28944, 0.0562067, -0.0286842, 1.26753, 0.0562068, -0.0302178, 1.23447, 0.0231135, -0.502395, 1.25638, 0.0231133, -0.0302178, 1.23447, 0.0231135, -0.502395, 1.25638, -0.0236878, -0.502395, 1.25638, 0.0231133, -0.0302178, 1.23447, 0.0231135, -0.0302178, 1.23447, -0.0236877, -0.502395, 1.25638, -0.0236878, -0.58961, 1.92194, -0.0567811, -0.636411, 1.46935, -0.0567811, -0.58961, 1.46935, -0.0567811, -0.58961, 1.92194, -0.0567811, -0.636411, 1.92194, -0.0567811, -0.636411, 1.46935, -0.0567811, -0.636411, 1.46935, -0.0567811, -0.575557, 1.40143, -0.0567811, -0.58961, 1.46935, -0.0567811, -0.636411, 1.46935, -0.0567811, -0.614647, 1.37569, -0.0567811, -0.575557, 1.40143, -0.0567811, -0.669505, 1.46935, 0.0231133, -0.636411, 1.92194, 0.0562067, -0.636411, 1.46935, 0.0562067, -0.669505, 1.46935, 0.0231133, -0.669505, 1.92194, 0.0231133, -0.636411, 1.92194, 0.0562067, -0.58961, 1.46935, 0.0562067, -0.556517, 1.92194, 0.0231133, -0.556517, 1.46935, 0.0231133, -0.58961, 1.46935, 0.0562067, -0.58961, 1.92194, 0.0562067, -0.556517, 1.92194, 0.0231133, -0.556517, 1.46935, 0.0231133, -0.556517, 1.92194, -0.0236878, -0.556517, 1.46935, -0.0236878, -0.556517, 1.46935, 0.0231133, -0.556517, 1.92194, 0.0231133, -0.556517, 1.92194, -0.0236878, -0.556517, 1.46935, -0.0236878, -0.58961, 1.92194, -0.0567811, -0.58961, 1.46935, -0.0567811, -0.556517, 1.46935, -0.0236878, -0.556517, 1.92194, -0.0236878, -0.58961, 1.92194, -0.0567811, -0.669505, 1.46935, -0.0236878, -0.669505, 1.92194, 0.0231133, -0.669505, 1.46935, 0.0231133, -0.669505, 1.46935, -0.0236878, -0.669505, 1.92194, -0.0236878, -0.669505, 1.92194, 0.0231133, -0.636411, 1.46935, 0.0562067, -0.58961, 1.92194, 0.0562067, -0.58961, 1.46935, 0.0562067, -0.636411, 1.46935, 0.0562067, -0.636411, 1.92194, 0.0562067, -0.58961, 1.92194, 0.0562067, -0.636411, 1.46935, -0.0567811, -0.669505, 1.92194, -0.0236878, -0.669505, 1.46935, -0.0236878, -0.636411, 1.46935, -0.0567811, -0.636411, 1.92194, -0.0567811, -0.669505, 1.92194, -0.0236878, -0.500862, 1.28944, 0.0562067, -0.575557, 1.40143, 0.0562067, -0.498693, 1.33619, 0.0562067, -0.500862, 1.28944, 0.0562067, -0.614647, 1.37569, 0.0562067, -0.575557, 1.40143, 0.0562067, -0.497159, 1.36924, -0.0236877, -0.575557, 1.40143, -0.0567811, -0.498693, 1.33619, -0.0567811, -0.497159, 1.36924, -0.0236877, -0.547916, 1.41962, -0.0236878, -0.575557, 1.40143, -0.0567811, -0.502395, 1.25638, 0.0231133, -0.614647, 1.37569, 0.0562067, -0.500862, 1.28944, 0.0562067, -0.502395, 1.25638, 0.0231133, -0.642288, 1.35749, 0.0231133, -0.614647, 1.37569, 0.0562067, -0.497159, 1.36924, 0.0231134, -0.547916, 1.41962, -0.0236878, -0.497159, 1.36924, -0.0236877, -0.497159, 1.36924, 0.0231134, -0.547916, 1.41962, 0.0231134, -0.547916, 1.41962, -0.0236878, -0.502395, 1.25638, -0.0236878, -0.642288, 1.35749, 0.0231133, -0.502395, 1.25638, 0.0231133, -0.502395, 1.25638, -0.0236878, -0.642288, 1.35749, -0.0236878, -0.642288, 1.35749, 0.0231133, -0.498693, 1.33619, 0.0562067, -0.547916, 1.41962, 0.0231134, -0.497159, 1.36924, 0.0231134, -0.498693, 1.33619, 0.0562067, -0.575557, 1.40143, 0.0562067, -0.547916, 1.41962, 0.0231134, -0.500862, 1.28944, -0.0567811, -0.0265153, 1.31428, -0.0567809, -0.498693, 1.33619, -0.0567811, -0.500862, 1.28944, -0.0567811, -0.0286842, 1.26753, -0.056781, -0.0265153, 1.31428, -0.0567809, -0.500862, 1.28944, -0.0567811, -0.642288, 1.35749, -0.0236878, -0.502395, 1.25638, -0.0236878, -0.500862, 1.28944, -0.0567811, -0.614647, 1.37569, -0.0567811, -0.642288, 1.35749, -0.0236878, 0.224034, 0.971889, 0.0554974, 0.149338, 0.859899, 0.0554974, 0.147169, 0.906649, 0.0554974, 0.224034, 0.971889, 0.0554974, 0.263124, 0.946154, 0.0554974, 0.149338, 0.859899, 0.0554974, 0.284888, 1.4924, 0.0554974, 0.244574, 1.52913, 0.0398373, 0.278402, 1.52913, 0.0398373, 0.284888, 1.4924, 0.0554974, 0.238087, 1.4924, 0.0554974, 0.244574, 1.52913, 0.0398373, 0.290765, 0.927957, -0.0243971, 0.317982, 1.03981, 0.022404, 0.317982, 1.03981, -0.0243971, 0.290765, 0.927957, -0.0243971, 0.290765, 0.927957, 0.022404, 0.317982, 1.03981, 0.022404, 0.317982, 1.4924, 0.022404, 0.278402, 1.52913, 0.0398373, 0.302321, 1.52913, 0.0159173, 0.317982, 1.4924, 0.022404, 0.284888, 1.4924, 0.0554974, 0.278402, 1.52913, 0.0398373, 0.263124, 0.946154, -0.0574904, 0.317982, 1.03981, -0.0243971, 0.284888, 1.03981, -0.0574904, 0.263124, 0.946154, -0.0574904, 0.290765, 0.927957, -0.0243971, 0.317982, 1.03981, -0.0243971, 0.317982, 1.4924, -0.0243971, 0.302321, 1.52913, 0.0159173, 0.302321, 1.52913, -0.0179104, 0.317982, 1.4924, -0.0243971, 0.317982, 1.4924, 0.022404, 0.302321, 1.52913, 0.0159173, 0.224034, 0.971889, -0.0574904, 0.284888, 1.03981, -0.0574904, 0.238087, 1.03981, -0.0574904, 0.224034, 0.971889, -0.0574904, 0.263124, 0.946154, -0.0574904, 0.284888, 1.03981, -0.0574904, 0.284888, 1.4924, -0.0574904, 0.302321, 1.52913, -0.0179104, 0.278401, 1.52913, -0.0418303, 0.284888, 1.4924, -0.0574904, 0.317982, 1.4924, -0.0243971, 0.302321, 1.52913, -0.0179104, 0.196393, 0.990086, -0.0243971, 0.238087, 1.03981, -0.0574904, 0.204994, 1.03981, -0.0243971, 0.196393, 0.990086, -0.0243971, 0.224034, 0.971889, -0.0574904, 0.238087, 1.03981, -0.0574904, 0.238087, 1.4924, -0.0574904, 0.278401, 1.52913, -0.0418303, 0.244574, 1.52913, -0.0418303, 0.238087, 1.4924, -0.0574904, 0.284888, 1.4924, -0.0574904, 0.278401, 1.52913, -0.0418303, 0.196393, 0.990086, 0.0224041, 0.204994, 1.03981, -0.0243971, 0.204994, 1.03981, 0.022404, 0.196393, 0.990086, 0.0224041, 0.196393, 0.990086, -0.0243971, 0.204994, 1.03981, -0.0243971, 0.204994, 1.4924, -0.0243971, 0.244574, 1.52913, -0.0418303, 0.220654, 1.52913, -0.0179103, 0.204994, 1.4924, -0.0243971, 0.238087, 1.4924, -0.0574904, 0.244574, 1.52913, -0.0418303, 0.224034, 0.971889, 0.0554974, 0.204994, 1.03981, 0.022404, 0.238087, 1.03981, 0.0554974, 0.224034, 0.971889, 0.0554974, 0.196393, 0.990086, 0.0224041, 0.204994, 1.03981, 0.022404, 0.204994, 1.4924, 0.022404, 0.220654, 1.52913, -0.0179103, 0.220654, 1.52913, 0.0159173, 0.204994, 1.4924, 0.022404, 0.204994, 1.4924, -0.0243971, 0.220654, 1.52913, -0.0179103, 0.261488, 1.54434, -0.0009965, 0.278402, 1.52913, 0.0398373, 0.244574, 1.52913, 0.0398373, 0.220654, 1.52913, -0.0179103, 0.261488, 1.54434, -0.0009965, 0.220654, 1.52913, 0.0159173, -0.58961, 1.92194, -0.0567811, -0.572177, 1.95867, -0.0172011, -0.596097, 1.95867, -0.041121, -0.58961, 1.92194, -0.0567811, -0.556517, 1.92194, -0.0236878, -0.572177, 1.95867, -0.0172011, 0.302321, 1.52913, 0.0159173, 0.278402, 1.52913, 0.0398373, 0.261488, 1.54434, -0.0009965, 0.302321, 1.52913, 0.0159173, 0.261488, 1.54434, -0.0009965, 0.302321, 1.52913, -0.0179104, 0.302321, 1.52913, -0.0179104, 0.261488, 1.54434, -0.0009965, 0.278401, 1.52913, -0.0418303, 0.278401, 1.52913, -0.0418303, 0.261488, 1.54434, -0.0009965, 0.244574, 1.52913, -0.0418303, 0.244574, 1.52913, -0.0418303, 0.261488, 1.54434, -0.0009965, 0.220654, 1.52913, -0.0179103, 0.220654, 1.52913, 0.0159173, 0.261488, 1.54434, -0.0009965, 0.244574, 1.52913, 0.0398373, 0.290765, 0.927957, 0.022404, 0.284888, 1.03981, 0.0554973, 0.317982, 1.03981, 0.022404, 0.290765, 0.927957, 0.022404, 0.263124, 0.946154, 0.0554974, 0.284888, 1.03981, 0.0554973, 0.0208803, 0.82081, 0.0224041, 0.149338, 0.859899, 0.0554974, 0.150872, 0.826841, 0.0224041, 0.0208803, 0.82081, 0.0224041, 0.0193467, 0.853868, 0.0554974, 0.149338, 0.859899, 0.0554974, 0.0171779, 0.900619, 0.0554974, 0.145636, 0.939707, 0.0224041, 0.147169, 0.906649, 0.0554974, 0.0171779, 0.900619, 0.0554974, 0.0156443, 0.933677, 0.0224041, 0.145636, 0.939707, 0.0224041, 0.0156443, 0.933677, 0.0224041, 0.145636, 0.939707, -0.024397, 0.145636, 0.939707, 0.0224041, 0.0156443, 0.933677, 0.0224041, 0.0156442, 0.933677, -0.024397, 0.145636, 0.939707, -0.024397, 0.0156442, 0.933677, -0.024397, 0.147169, 0.906649, -0.0574904, 0.145636, 0.939707, -0.024397, 0.0156442, 0.933677, -0.024397, 0.0171778, 0.900619, -0.0574903, 0.147169, 0.906649, -0.0574904, 0.0171778, 0.900619, -0.0574903, 0.149338, 0.859899, -0.0574904, 0.147169, 0.906649, -0.0574904, 0.0171778, 0.900619, -0.0574903, 0.0193467, 0.853868, -0.0574903, 0.149338, 0.859899, -0.0574904, 0.0193467, 0.853868, -0.0574903, 0.150872, 0.826841, -0.024397, 0.149338, 0.859899, -0.0574904, 0.0193467, 0.853868, -0.0574903, 0.0208803, 0.82081, -0.024397, 0.150872, 0.826841, -0.024397, 0.0208803, 0.82081, -0.024397, 0.150872, 0.826841, 0.0224041, 0.150872, 0.826841, -0.024397, 0.0208803, 0.82081, -0.024397, 0.0208803, 0.82081, 0.0224041, 0.150872, 0.826841, 0.0224041, 0.238087, 1.4924, 0.0554974, 0.284888, 1.03981, 0.0554973, 0.238087, 1.03981, 0.0554974, 0.238087, 1.4924, 0.0554974, 0.284888, 1.4924, 0.0554974, 0.284888, 1.03981, 0.0554973, 0.284888, 1.03981, 0.0554973, 0.224034, 0.971889, 0.0554974, 0.238087, 1.03981, 0.0554974, 0.284888, 1.03981, 0.0554973, 0.263124, 0.946154, 0.0554974, 0.224034, 0.971889, 0.0554974, 0.317982, 1.03981, -0.0243971, 0.284888, 1.4924, -0.0574904, 0.284888, 1.03981, -0.0574904, 0.317982, 1.03981, -0.0243971, 0.317982, 1.4924, -0.0243971, 0.284888, 1.4924, -0.0574904, 0.238087, 1.03981, -0.0574904, 0.204994, 1.4924, -0.0243971, 0.204994, 1.03981, -0.0243971, 0.238087, 1.03981, -0.0574904, 0.238087, 1.4924, -0.0574904, 0.204994, 1.4924, -0.0243971, 0.204994, 1.03981, -0.0243971, 0.204994, 1.4924, 0.022404, 0.204994, 1.03981, 0.022404, 0.204994, 1.03981, -0.0243971, 0.204994, 1.4924, -0.0243971, 0.204994, 1.4924, 0.022404, 0.204994, 1.03981, 0.022404, 0.238087, 1.4924, 0.0554974, 0.238087, 1.03981, 0.0554974, 0.204994, 1.03981, 0.022404, 0.204994, 1.4924, 0.022404, 0.238087, 1.4924, 0.0554974, 0.317982, 1.03981, 0.022404, 0.317982, 1.4924, -0.0243971, 0.317982, 1.03981, -0.0243971, 0.317982, 1.03981, 0.022404, 0.317982, 1.4924, 0.022404, 0.317982, 1.4924, -0.0243971, 0.284888, 1.03981, -0.0574904, 0.238087, 1.4924, -0.0574904, 0.238087, 1.03981, -0.0574904, 0.284888, 1.03981, -0.0574904, 0.284888, 1.4924, -0.0574904, 0.238087, 1.4924, -0.0574904, 0.284888, 1.03981, 0.0554973, 0.317982, 1.4924, 0.022404, 0.317982, 1.03981, 0.022404, 0.284888, 1.03981, 0.0554973, 0.284888, 1.4924, 0.0554974, 0.317982, 1.4924, 0.022404, 0.149338, 0.859899, -0.0574904, 0.224034, 0.971889, -0.0574904, 0.147169, 0.906649, -0.0574904, 0.149338, 0.859899, -0.0574904, 0.263124, 0.946154, -0.0574904, 0.224034, 0.971889, -0.0574904, 0.145636, 0.939707, 0.0224041, 0.224034, 0.971889, 0.0554974, 0.147169, 0.906649, 0.0554974, 0.145636, 0.939707, 0.0224041, 0.196393, 0.990086, 0.0224041, 0.224034, 0.971889, 0.0554974, 0.150872, 0.826841, -0.024397, 0.263124, 0.946154, -0.0574904, 0.149338, 0.859899, -0.0574904, 0.150872, 0.826841, -0.024397, 0.290765, 0.927957, -0.0243971, 0.263124, 0.946154, -0.0574904, 0.145636, 0.939707, -0.024397, 0.196393, 0.990086, 0.0224041, 0.145636, 0.939707, 0.0224041, 0.145636, 0.939707, -0.024397, 0.196393, 0.990086, -0.0243971, 0.196393, 0.990086, 0.0224041, 0.150872, 0.826841, 0.0224041, 0.290765, 0.927957, -0.0243971, 0.150872, 0.826841, -0.024397, 0.150872, 0.826841, 0.0224041, 0.290765, 0.927957, 0.022404, 0.290765, 0.927957, -0.0243971, 0.147169, 0.906649, -0.0574904, 0.196393, 0.990086, -0.0243971, 0.145636, 0.939707, -0.024397, 0.147169, 0.906649, -0.0574904, 0.224034, 0.971889, -0.0574904, 0.196393, 0.990086, -0.0243971, 0.149338, 0.859899, 0.0554974, 0.290765, 0.927957, 0.022404, 0.150872, 0.826841, 0.0224041, 0.149338, 0.859899, 0.0554974, 0.263124, 0.946154, 0.0554974, 0.290765, 0.927957, 0.022404, 0.149338, 0.859899, 0.0554974, 0.0171779, 0.900619, 0.0554974, 0.147169, 0.906649, 0.0554974, 0.149338, 0.859899, 0.0554974, 0.0193467, 0.853868, 0.0554974, 0.0171779, 0.900619, 0.0554974, 0.0424324, 1.84657, -0.102441, 0.0740441, 1.91317, -0.03067, 0.03067, 1.91317, -0.0740441, 0.0424324, 1.84657, -0.102441, 0.102441, 1.84657, -0.0424324, 0.0740441, 1.91317, -0.03067)

[node name="Cactus_Tall_5" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_8lqpd")
skeleton = NodePath("")

[node name="Cactus_Tall_5" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="Cactus_Tall_5"]
shape = SubResource("ConcavePolygonShape3D_boa8u")
