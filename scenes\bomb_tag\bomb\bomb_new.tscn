[gd_scene load_steps=19 format=3 uid="uid://bqxjt3clk5306"]

[ext_resource type="PackedScene" uid="uid://bbfknls1xwi2l" path="res://scenes/bomb_tag/bomb/bomb.tscn" id="1_5mp4h"]
[ext_resource type="Texture2D" uid="uid://hlmwklfyd1cl" path="res://scenes/bomb_tag/bomb/flamelet_smooth.png" id="2_cr3vk"]
[ext_resource type="Script" uid="uid://bqvd2arrwmlky" path="res://scenes/bomb_tag/bomb/flame_drag.gd" id="3_cr3vk"]

[sub_resource type="Curve" id="Curve_dfn0h"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.220833, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_schg1"]
curve = SubResource("Curve_dfn0h")

[sub_resource type="Gradient" id="Gradient_84omg"]
offsets = PackedFloat32Array(0, 0.224299, 0.420561, 0.945255)
colors = PackedColorArray(0, 0, 0, 1, 1, 0.400119, 0, 1, 1, 0.709804, 0, 1, 0.0470588, 0.0470588, 0.0470588, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_4jqei"]
gradient = SubResource("Gradient_84omg")

[sub_resource type="Curve" id="Curve_84omg"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.258333, 0.573034), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_4jqei"]
curve = SubResource("Curve_84omg")

[sub_resource type="Curve" id="Curve_schg1"]
_limits = [-200.0, 200.0, 0.0, 1.0]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_5mp4h"]
curve = SubResource("Curve_schg1")

[sub_resource type="Curve" id="Curve_yroic"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0.348315), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_ponqu"]
curve = SubResource("Curve_yroic")

[sub_resource type="Curve" id="Curve_cr3vk"]
_limits = [0.0, 3.0, 0.0, 1.0]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_dfn0h"]
curve = SubResource("Curve_cr3vk")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_80387"]
emission_shape = 1
emission_sphere_radius = 0.01
gravity = Vector3(0, 10, 0)
radial_accel_curve = SubResource("CurveTexture_5mp4h")
scale_min = 0.8
scale_curve = SubResource("CurveTexture_ponqu")
scale_over_velocity_curve = SubResource("CurveTexture_dfn0h")
color_ramp = SubResource("GradientTexture1D_4jqei")
alpha_curve = SubResource("CurveTexture_schg1")
emission_curve = SubResource("CurveTexture_4jqei")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_5i01a"]
transparency = 1
shading_mode = 0
vertex_color_use_as_albedo = true
albedo_texture = ExtResource("2_cr3vk")
billboard_mode = 1

[sub_resource type="QuadMesh" id="QuadMesh_otm5s"]
material = SubResource("StandardMaterial3D_5i01a")
size = Vector2(0.25, 0.25)

[node name="Bomb" type="Node3D"]

[node name="Node3D" parent="." instance=ExtResource("1_5mp4h")]

[node name="GPUParticles3D" type="GPUParticles3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0498759, 0.318863, 0)
amount = 6
lifetime = 0.22
preprocess = 0.03
randomness = 0.2
use_fixed_seed = true
seed = 2359240611
local_coords = true
process_material = SubResource("ParticleProcessMaterial_80387")
draw_pass_1 = SubResource("QuadMesh_otm5s")

[node name="Marker3D" type="Marker3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00365147, 0.380491, 0)
top_level = true
script = ExtResource("3_cr3vk")
max_strengh = 5.0

[node name="GPUParticlesAttractorSphere3D" type="GPUParticlesAttractorSphere3D" parent="Marker3D"]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0610861, 0, 0)
strength = 6.0
attenuation = 1e-05

[node name="CSGSphere3D" type="CSGSphere3D" parent="Marker3D/GPUParticlesAttractorSphere3D"]
visible = false
radius = 0.055114
