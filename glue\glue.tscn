[gd_scene load_steps=5 format=3 uid="uid://vosng73cptsl"]

[ext_resource type="Script" uid="uid://bgdvreoit01ah" path="res://glue/glue.gd" id="1_dy7kn"]

[sub_resource type="CylinderShape3D" id="CylinderShape3D_dy7kn"]
height = 0.5
radius = 1.9

[sub_resource type="SphereMesh" id="SphereMesh_p6l0p"]
radius = 2.0
height = 0.5
radial_segments = 6
rings = 3
is_hemisphere = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_klcdj"]
albedo_color = Color(1, 0.746755, 0.26177, 1)

[node name="Glue" type="Area3D"]
collision_mask = 3
script = ExtResource("1_dy7kn")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.286956, 0)
shape = SubResource("CylinderShape3D_dy7kn")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
mesh = SubResource("SphereMesh_p6l0p")
surface_material_override/0 = SubResource("StandardMaterial3D_klcdj")

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
[connection signal="body_exited" from="." to="." method="_on_body_exited"]
