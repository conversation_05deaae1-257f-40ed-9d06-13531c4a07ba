[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://bbwy72achauqu"
path.s3tc="res://.godot/imported/beach_umbrella_-_low_poly_1.png-b276d0b0fbcfeb701925ac2ea0ea7749.s3tc.ctex"
path.etc2="res://.godot/imported/beach_umbrella_-_low_poly_1.png-b276d0b0fbcfeb701925ac2ea0ea7749.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "a0c25bc6a4fb9a7ace8e99a7692db6d0"
}

[deps]

source_file="res://beach/beach_umbrella_-_low_poly_1.png"
dest_files=["res://.godot/imported/beach_umbrella_-_low_poly_1.png-b276d0b0fbcfeb701925ac2ea0ea7749.s3tc.ctex", "res://.godot/imported/beach_umbrella_-_low_poly_1.png-b276d0b0fbcfeb701925ac2ea0ea7749.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=1
roughness/src_normal="res://beach/beach_umbrella_-_low_poly_1.png"
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
