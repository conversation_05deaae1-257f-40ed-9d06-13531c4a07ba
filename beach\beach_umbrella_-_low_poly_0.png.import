[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://d17ghqa6acyru"
path.s3tc="res://.godot/imported/beach_umbrella_-_low_poly_0.png-25f3c63435fb629baf0e392a6c96704b.s3tc.ctex"
path.etc2="res://.godot/imported/beach_umbrella_-_low_poly_0.png-25f3c63435fb629baf0e392a6c96704b.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "67adeda06224e6bf694e0f66fe7b178d"
}

[deps]

source_file="res://beach/beach_umbrella_-_low_poly_0.png"
dest_files=["res://.godot/imported/beach_umbrella_-_low_poly_0.png-25f3c63435fb629baf0e392a6c96704b.s3tc.ctex", "res://.godot/imported/beach_umbrella_-_low_poly_0.png-25f3c63435fb629baf0e392a6c96704b.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
