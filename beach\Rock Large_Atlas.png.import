[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://b07m3c7ufjux"
path.s3tc="res://.godot/imported/Rock Large_Atlas.png-6b1e43ca4de1678c5164342f9d166f71.s3tc.ctex"
path.etc2="res://.godot/imported/Rock Large_Atlas.png-6b1e43ca4de1678c5164342f9d166f71.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "b6b1d5843eb67973aacf764f46d2f25b"
}

[deps]

source_file="res://beach/Rock Large_Atlas.png"
dest_files=["res://.godot/imported/Rock Large_Atlas.png-6b1e43ca4de1678c5164342f9d166f71.s3tc.ctex", "res://.godot/imported/Rock Large_Atlas.png-6b1e43ca4de1678c5164342f9d166f71.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
