[gd_scene load_steps=6 format=4 uid="uid://7s1esfbldf1"]

[ext_resource type="Material" uid="uid://g5x7g5rmse4o" path="res://scenes/bomb_tag/level/cactus/materials/cactus_body.material" id="1_fbnf8"]
[ext_resource type="Material" uid="uid://3oxpnywrvsyc" path="res://scenes/bomb_tag/level/cactus/materials/cactus_flowers.material" id="2_ipf46"]

[sub_resource type="ArrayMesh" id="ArrayMesh_nx8in"]
_surfaces = [{
"aabb": AABB(-0.159099, -0.19045, -0.159589, 0.320581, 0.59595, 0.320581),
"format": 34896613377,
"index_count": 216,
"index_data": PackedByteArray("DgAQABoADgAPABAAIwABAAIAIwAiAAEADQAaABsADQAOABoAJAACAAMAJAAjAAIADAAbABwADAANABsAJQADAAQAJQAkAAMACwAcAB0ACwAMABwAJgAEAAUAJgAlAAQACgAdAB4ACgALAB0AJwAFAAYAJwAmAAUACQAeAB8ACQAKAB4AKAAGAAcAKAAnAAYACAAfACAACAAJAB8AAAAIAA8AAAAHAAgABwAJAAgABwAGAAkABgAKAAkABgAFAAoABQALAAoABQAEAAsABAAMAAsABAADAAwAAwANAAwAAwACAA0AAgAOAA0AAgABAA4AAQAPAA4AAQAAAA8AFAASABMAEAAZABoAEAARABkADwAgABAADwAIACAAEQASABkAGQASABgAGAASABcAFwASABYAFgASABUAFQASABQAEQAgABMAEQAQACAAHwATACAAHwAUABMAHgAUAB8AHgAVABQAHQAVAB4AHQAWABUAHAAWAB0AHAAXABYAGwAXABwAGwAYABcAGgAYABsAGgAZABgAEgARABMAIgAAAAEAIgAhAAAABwAhACgABwAAACEA"),
"lods": [0.122537, PackedByteArray("AQARABkAEQASABkAAQATABEAEgARABMAAQAZABgAGQASABgAAwABABgAAwAYABcAGAASABcABQAUABMAFAASABMAFQASABQABQAVABQABQADABcABQAWABUABQAXABYAFgASABUAFwASABYAIwAiAAEAIgAhAAEAJAAjAAEAJAABAAMAJQADAAUAJQAkAAMAJgAlAAUAJwAmAAUAKAAnAAUABQAhACgABQABACEAAQAFABMA")],
"name": "Plant_2",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 41,
"vertex_data": PackedByteArray("+kqwwAAAAAAAALDA+koAAAAAsMAEtQAA+kqwwP//AAAEtbDA//8AAP//sMAEtQAA//+wwPpKAAAEtbDAAAAAADKx6tg4CQAAxvbq2MxOAADG9urYMrEAADKx6tjG9gAAzE7q2Mb2AAA4CerYMrEAADgJ6tjMTgAAzE7q2DgJAACtWXTteyMAAPRpLfvISgAA/3////9/AAAKli37yEoAADa1Lfv0aQAANrUt+wqWAAAKli37NrUAAPRpLfs2tQAAyEot+wqWAADISi379GkAAHsjdO2tWQAAeyN07VGmAACtWXTtg9wAAFGmdO2D3AAAg9x07VGmAACD3HTtrVkAAFGmdO17IwAApWEAAHM2AAC4NgAAYGEAALg2AAAWngAApWEAAATJAABbngAABMkAAEnJAAAWngAASckAAGBhAABbngAAczYAAA==")
}, {
"aabb": AABB(-0.262655, 2.38419e-07, -0.263421, 0.525313, 0.46254, 0.526842),
"format": 34896613377,
"index_count": 228,
"index_data": PackedByteArray("AwAAAAEAAwACAAAACQAGAAQACQAIAAYABwAIAAkABwAFAAgABAALAAoABAAGAAsADwAMAA0ADwAOAAwAFQASABAAFQAUABIAEwAUABUAEwARABQAEAAXABYAEAASABcAGwAYABkAGwAaABgAIQAeABwAIQAgAB4AHwAgACEAHwAdACAAHAAjACIAHAAeACMAJwAkACUAJwAmACQALQAqACgALQAsACoAKwAsAC0AKwApACwAKAAvAC4AKAAqAC8AMwAwADEAMwAyADAAOQA2ADQAOQA4ADYANwA4ADkANwA1ADgANAA7ADoANAA2ADsAPwA8AD0APwA+ADwARQBCAEAARQBEAEIAQwBEAEUAQwBBAEQAQABHAEYAQABCAEcASwBIAEkASwBKAEgAUQBOAEwAUQBQAE4ATwBQAFEATwBNAFAATABTAFIATABOAFMAVwBUAFUAVwBWAFQAXQBaAFgAXQBcAFoAWwBcAF0AWwBZAFwAWABfAF4AWABaAF8AZABgAGUAZABhAGAAZQBiAGYAZQBgAGIAZgBjAGcAZgBiAGMAZwBhAGQAZwBjAGEAZQBoAGQAZABoAGcAZwBoAGYAZgBoAGUA"),
"name": "Plant_1",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 105,
"vertex_data": PackedByteArray("20h+nc3+AACCXn6df80AAK5gAACVyAAAE20AAFqsAAD0Z4nIG7gAAJFMa6L79gAAX11X6wXTAACDXtyOf80AAABSdsgA6gAA92ATrvDHAABqcjfaa6AAACpq/v/htAAAfbV+nf//AADuoH6dOc4AAOCeAABDyQAAG5MAAMSsAAD3l4nIorgAAH2ya6Lg9wAADKRX6+fSAADvoNyOOc4AANasdsj86gAAmp4TrpzIAAAHjjfauKAAAA6X///atAAA/v9+nSq2AAB9zn6dFKEAAIjJAAD+ngAAMK0AAOySAADkuInIAJgAAD34a6JzsgAA6dNX60OiAAB9ztyOFKEAACLrdsg9rQAA4MgTrrieAAD4oDfa840AAHu1///9lQAA4v9+nc5KAABrzn6dcF8AAHfJAACGYQAAJq0AAFZtAADXuInIjmgAAOn3a6K6TQAA89JX621cAABrztyOb18AAAfrdsh3UwAAz8gTrs5hAADwoDfaqHIAAOu0//+haQAAOrV+nSABAADFoH6dYjIAALmeAABXNwAAA5MAAItTAADYl4nI/kcAALaxa6LACAAAx6FX6xstAADFoNyOYjIAAJesdsjdFQAAc54TrgA4AAD1jTfa7V8AALeV//+LSwAAvk5+nQAAAAAcYn6dsjEAAAxkAACzNgAAIm8AACdTAACNaonIf0cAAI5Ra6LpBwAAGV9X6zctAAAbYtyOsjEAAOVWdsjvFAAATWQTrl03AADoczfapF8AAF5r//+SSwAAegJ+nQZIAAB6M36dvF0AAGI4AADwXwAAcFQAAF5sAADZSInIVGcAACcKa6KkSwAAFS5X67JcAAB6M9yOvV0AACEXdsgrUQAACDkTrjtgAACFYDfa9HEAADRM//+yaQAAAAB+nVqzAAD2MX6dXZ8AAPc2AABjnQAAklMAAPGRAADBR4nIxJYAAA0Ia6JZsAAAWy1X64miAAD2MdyOXZ8AABIVdsjrqgAAoDcTriGdAADlXzfaPY0AALJL//8OlgAAz1ah8yOqAAAuqWL0I6oAAM9WuvTbVQAALql49dtVAABllNnmqpcAALtp2OaqlwAAu2nY5vpqAABllNnm+moAABB/j+BSgQAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_ehfvq"]
resource_name = "CanyonPlantsRocksTiles_Cactus_Small_4"
_surfaces = [{
"aabb": AABB(-0.159099, -0.19045, -0.159589, 0.320581, 0.59595, 0.320581),
"attribute_data": PackedByteArray("hTJMEKXQ8hhtPEwQOMhrI1RGTBAryaww/wBMEDxQTBCE06Q55wpMECjhVDjPFEwQ4+noLbYeTBDG6HIgnihMECjetRd73Codn+MQI2rkPCyE3lIzTdUjNEzOIi6DzRIlWdMLHmLV4iEa1yoldtiAKH/Y4Si02DYoydgfKRXZLSgq2RYpX9lrKGjZzCgc2uIkbdzSJrPc0ynD2iQswddqLHLVeSot1XsnqNFfJirSKyyj1vQvedxrLz/g7yq13xclNdtYIYUyQjZtPEI2VEZCNv8AQjY8UEI25wpCNs8UQja2HkI2nihCNm08TBBtPEwQOMhrIzjIayP/AEwQPFBMEITTpDmE06Q5zxRMEM8UTBDPFEwQ4+noLePp6C3j6egtGtcqJRza4iQc2uIkHNriJMPaJCzB12osctV5KoUyQjaFMkI2VEZCNjxQQjbnCkI25wpCNrYeQjaeKEI2"),
"format": ***********,
"index_count": 216,
"index_data": PackedByteArray("FwAZACoAFwAYABkAMwACAAQAMwAyAAIAFgAqACsAFgAXACoANQAEAAcANQAzAAQAFQArACwAFQAWACsANgAGAAkANgA0AAYAFAAsAC0AFAAVACwANwAJAAsANwA2AAkAEwAtAC4AEwAUAC0AOAALAA0AOAA3AAsAEgAuAC8AEgATAC4AOQANAA8AOQA4AA0AEQAvADAAEQASAC8AAQARABgAAQAQABEAEAASABEAEAAOABIADgATABIADgAMABMADAAUABMADAAKABQACgAVABQACgAIABUACAAWABUACAAFABYABQAXABYABQADABcAAwAYABcAAwABABgAJAAhACMAGQApACoAGQAaACkAGAAwABkAGAARADAAGgAdACkAKQAbACgAKAAcACcAJwAeACYAJgAgACUAJQAiACQAGgAwACMAGgAZADAALwAjADAALwAkACMALgAkAC8ALgAlACQALQAlAC4ALQAmACUALAAmAC0ALAAnACYAKwAnACwAKwAoACcAKgAoACsAKgApACgAHwAaACMAMgAAAAIAMgAxAAAADwAxADkADwAAADEA"),
"lods": [0.122537, PackedByteArray("PABIACkASAAdACkAPABKAEgAHwBIAEkAPAApAE4AKQAbAE4AQAA8AE4AQABOAE0ATgAcAE0ARgAkAEkAJAAhAEkAJQAiACQARQAlACQARQBBAE0ARQBMACUARQBNAEwATAAgACUATQAeAEwAUQAyADoAMgBPADoAUgBRADoAUgA6AD8AUwA+AEIAUwA0AD4ANwBUAEIAVQA3AEIAVgBVAEMARABQAFYARAA7AFAAPQBHAEsA")],
"material": ExtResource("1_fbnf8"),
"name": "Plant_2",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 87,
"vertex_data": PackedByteArray("+kqwwAAA/u/6SrDAAACW+wAAsMD6Sv/PAACwwPpKqeUAALDABLUBsAAAsMAEtRfA+kqwwP//B5D6SrDA//8KkPpKsMD//+mSBLWwwP//B5AEtbDA//+Gl///sMAEtQGw//+wwAS1X8H//7DA+kr/z///sMD6SvPmBLWwwAAA/u8EtbDAAAD0/DKx6tg4CeDtxvbq2MxOHd3G9urYMrEEvjKx6tjG9rWbzE7q2Mb2rJc4CerYMrFnuTgJ6tjMTlDZzE7q2DgJW+ytWXTteyMr3fRpLfvISrnO/3////9/K8D/f////38EwP9/////f17A/3////9/G8D/f////38cwP9/////f1zA/3////9/BMD/f////38rwAqWLfvISinPNrUt+/Rp8Mc2tS37CpYovAqWLfs2tQKy9Gkt+za1X7HISi37CpbJushKLfv0ab7GeyN07a1Z7857I3TtUaYfuK1ZdO2D3MajUaZ07YPcqaWD3HTtUabUu4PcdO2tWQ/SUaZ07XsjNd6lYQAAczYN8Lg2AABgYS/QuDYAABaeabClYQAABMlWkaVhAAAEycCRW54AAATJVJFJyQAAFp5psEnJAABgYS/QW54AAHM2DfAAALDA+kqUxwAAsMD6SgPfAACwwPpKFeMAALDA+koX8vpKsMD//9qL+kqwwP//Ppr6SrDA///nofpKsMD//2Ca//+wwAS1i6z//7DABLX4vf//sMAEtd3K//+wwAS1d77//7DABLX0zv//sMAEtY3n9Gkt+8hKRtAKli37yEpYywqWLfvISqvcCpYt+8hKWvUKli37NrU8sPRpLfs2tYGqyEot+wqWxbelYQAAczaZ5KVhAABzNsvyuDYAABaeF7SlYQAABMnEnVueAAAEyVSNW54AAATJ5JxJyQAAYGFty1ueAABzNr/mU4AC/oLsQ6UXgUT9s9ucyGSCAPzlzpDf3onD9+CGhPTfuXLyK4mmB2CPbUr+g2UCEaGPULmCFwF6uJBd/IFUAOLg/3NM5gZ4G8NeZVixU1sTtodgOdLywnjT98/A3TDABu3yoKLvxZnd8+2PNvFjh3n6woJs60iK6/MJhqbzK4aU6zSKQfregkTxXIfK8mp9H+Dpc2/bOHJM6r96b/Fzks7pEaRU6yOhkuKKszXcK7zP41Sm8NJtb/DDS2ZVz51sp+vZepF0Gf6YcNP5MWrs8kxa59LKTm3ij1x9Ku1yzhXVeWQPGX5uC/p3Df2Xh1r/ruo1uyZgRiYeTxTcsnnS+BXIocVK6yGrg3c3Dup3ew5MdL4fMrHmXRG6JFH0ru00uuyllsbpJHQX4R6Xr8coTAXjQHPM9zCOm+RbqmiGZf/npfD2VW519ZtkOPK5X8MmHWnEGzV7RQwHgG4F")
}, {
"aabb": AABB(-0.262655, 2.38419e-07, -0.263421, 0.525313, 0.46254, 0.526842),
"attribute_data": PackedByteArray("/7//v/+/P97/f/+//38/3v+PP97/jz/e/3//v/+P/7//j/+//38/3v+H/7//h/+//4c/3v+HP97/lz/e/5f/v/+//7//vz/e/3//v/9/P97/jz/e/48/3v9//7//j/+//4//v/9/P97/h/+//4f/v/+HP97/hz/e/5c/3v+X/7//v/+//78/3v9//7//fz/e/48/3v+PP97/f/+//4//v/+P/7//fz/e/4f/v/+H/7//hz/e/4c/3v+XP97/l/+//7//v/+/P97/f/+//38/3v+PP97/jz/e/3//v/+P/7//j/+//38/3v+H/7//h/+//4c/3v+HP97/lz/e/5f/v/+//7//vz/e/3//v/9/P97/jz/e/48/3v9//7//j/+//4//v/9/P97/h/+//4f/v/+HP97/hz/e/5c/3v+X/7//v/+//78/3v9//7//fz/e/48/3v+PP97/f/+//4//v/+P/7//fz/e/4f/v/+H/7//hz/e/4c/3v+XP97/l/+//7//v/+/P97/f/+//38/3v+PP97/jz/e/3//v/+P/7//j/+//38/3v+H/7//h/+//4c/3v+HP97/lz/e/5f/v/+//7//vz/e/3//v/9/P97/jz/e/48/3v9//7//j/+//4//v/9/P97/h/+//4f/v/+HP97/hz/e/5c/3v+X/78Is9SZmr3UmQizI4SavSOE77oTle+6E5XvuhOVdrUTlXa1E5V2tZOJdrWTie+6k4nvupOJM7hTjzO4U48zuFOP"),
"format": ***********,
"index_count": 228,
"index_data": PackedByteArray("AwAAAAEAAwACAAAADAAHAAQADAAKAAcACQALAA0ACQAGAAsABQAPAA4ABQAIAA8AEwAQABEAEwASABAAHQAYABUAHQAbABgAGQAaABwAGQAWABoAFAAfAB4AFAAXAB8AIwAgACEAIwAiACAALQAoACUALQArACgAKQAqACwAKQAmACoAJAAvAC4AJAAnAC8AMwAwADEAMwAyADAAPQA4ADUAPQA7ADgAOQA6ADwAOQA2ADoANAA/AD4ANAA3AD8AQwBAAEEAQwBCAEAATQBIAEUATQBLAEgASQBKAEwASQBGAEoARABPAE4ARABHAE8AUwBQAFEAUwBSAFAAXABXAFQAXABaAFcAWQBbAF0AWQBWAFsAVQBfAF4AVQBYAF8AYwBgAGEAYwBiAGAAbABnAGQAbABqAGcAaQBrAG0AaQBmAGsAZQBvAG4AZQBoAG8AcwBwAHEAcwByAHAAfAB3AHQAfAB6AHcAeQB7AH0AeQB2AHsAdQB/AH4AdQB4AH8AhgCAAIgAhgCBAIAAhwCCAIoAhwCAAIIAiQCDAIwAiQCCAIMAiwCBAIUAiwCDAIEAiACNAIQAhACOAIwAiwCPAIoAiQCOAIgA"),
"material": ExtResource("2_ipf46"),
"name": "Plant_1",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 144,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_nx8in")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_11tt5"]
data = PackedVector3Array(-0.0652035, 0.258124, -0.159589, 0.0491809, 0.362334, -0.115156, -0.0467987, 0.362334, -0.115156, -0.0652035, 0.258124, -0.159589, 0.0675856, 0.258124, -0.159589, 0.0491809, 0.362334, -0.115156, -0.0905743, -0.19045, 0.0383832, -0.159099, 0.258124, -0.0656926, -0.159099, 0.258124, 0.0670965, -0.0905743, -0.19045, 0.0383832, -0.0905743, -0.19045, -0.037643, -0.159099, 0.258124, -0.0656926, -0.159099, 0.258124, -0.0656926, -0.0467987, 0.362334, -0.115156, -0.114667, 0.362334, -0.0472878, -0.159099, 0.258124, -0.0656926, -0.0652035, 0.258124, -0.159589, -0.0467987, 0.362334, -0.115156, -0.0368158, -0.19045, 0.0921417, -0.159099, 0.258124, 0.0670965, -0.0652035, 0.258124, 0.160992, -0.0368158, -0.19045, 0.0921417, -0.0905743, -0.19045, 0.0383832, -0.159099, 0.258124, 0.0670965, -0.159099, 0.258124, 0.0670965, -0.114667, 0.362334, -0.0472878, -0.114667, 0.362334, 0.0486917, -0.159099, 0.258124, 0.0670965, -0.159099, 0.258124, -0.0656926, -0.114667, 0.362334, -0.0472878, 0.0392103, -0.19045, 0.0921417, -0.0652035, 0.258124, 0.160992, 0.0675856, 0.258124, 0.160992, 0.0392103, -0.19045, 0.0921417, -0.0368158, -0.19045, 0.0921417, -0.0652035, 0.258124, 0.160992, -0.0652035, 0.258124, 0.160992, -0.114667, 0.362334, 0.0486917, -0.0467987, 0.362334, 0.11656, -0.0652035, 0.258124, 0.160992, -0.159099, 0.258124, 0.0670965, -0.114667, 0.362334, 0.0486917, 0.0929688, -0.19045, 0.0383832, 0.0675856, 0.258124, 0.160992, 0.161482, 0.258124, 0.0670965, 0.0929688, -0.19045, 0.0383832, 0.0392103, -0.19045, 0.0921417, 0.0675856, 0.258124, 0.160992, 0.0675856, 0.258124, 0.160992, -0.0467987, 0.362334, 0.11656, 0.0491809, 0.362334, 0.11656, 0.0675856, 0.258124, 0.160992, -0.0652035, 0.258124, 0.160992, -0.0467987, 0.362334, 0.11656, 0.0929688, -0.19045, -0.037643, 0.161482, 0.258124, 0.0670965, 0.161482, 0.258124, -0.0656926, 0.0929688, -0.19045, -0.037643, 0.0929688, -0.19045, 0.0383832, 0.161482, 0.258124, 0.0670965, 0.161482, 0.258124, 0.0670965, 0.0491809, 0.362334, 0.11656, 0.117049, 0.362334, 0.0486917, 0.161482, 0.258124, 0.0670965, 0.0675856, 0.258124, 0.160992, 0.0491809, 0.362334, 0.11656, 0.0392103, -0.19045, -0.0914015, 0.161482, 0.258124, -0.0656926, 0.0675856, 0.258124, -0.159589, 0.0392103, -0.19045, -0.0914015, 0.0929688, -0.19045, -0.037643, 0.161482, 0.258124, -0.0656926, 0.161482, 0.258124, -0.0656926, 0.117049, 0.362334, 0.0486917, 0.117049, 0.362334, -0.0472878, 0.161482, 0.258124, -0.0656926, 0.161482, 0.258124, 0.0670965, 0.117049, 0.362334, 0.0486917, 0.00119108, 0.4055, 0.000701968, -0.0467987, 0.362334, -0.115156, 0.0491809, 0.362334, -0.115156, 0.117049, 0.362334, 0.0486917, 0.00119108, 0.4055, 0.000701968, 0.117049, 0.362334, -0.0472878, -0.114667, 0.362334, -0.0472878, -0.0467987, 0.362334, -0.115156, 0.00119108, 0.4055, 0.000701968, -0.114667, 0.362334, -0.0472878, 0.00119108, 0.4055, 0.000701968, -0.114667, 0.362334, 0.0486917, -0.114667, 0.362334, 0.0486917, 0.00119108, 0.4055, 0.000701968, -0.0467987, 0.362334, 0.11656, -0.0467987, 0.362334, 0.11656, 0.00119108, 0.4055, 0.000701968, 0.0491809, 0.362334, 0.11656, 0.0491809, 0.362334, 0.11656, 0.00119108, 0.4055, 0.000701968, 0.117049, 0.362334, 0.0486917, 0.117049, 0.362334, -0.0472878, 0.00119108, 0.4055, 0.000701968, 0.0491809, 0.362334, -0.115156, -0.0905743, -0.19045, -0.037643, -0.0652035, 0.258124, -0.159589, -0.159099, 0.258124, -0.0656926, -0.0905743, -0.19045, -0.037643, -0.0368158, -0.19045, -0.0914015, -0.0652035, 0.258124, -0.159589, 0.0675856, 0.258124, -0.159589, -0.0368158, -0.19045, -0.0914015, 0.0392103, -0.19045, -0.0914015, 0.0675856, 0.258124, -0.159589, -0.0652035, 0.258124, -0.159589, -0.0368158, -0.19045, -0.0914015, 0.0675856, 0.258124, -0.159589, 0.117049, 0.362334, -0.0472878, 0.0491809, 0.362334, -0.115156, 0.0675856, 0.258124, -0.159589, 0.161482, 0.258124, -0.0656926, 0.117049, 0.362334, -0.0472878)

[node name="Cactus_Small_4" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_ehfvq")
skeleton = NodePath("")

[node name="Cactus_Small_4" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="Cactus_Small_4"]
shape = SubResource("ConcavePolygonShape3D_11tt5")
