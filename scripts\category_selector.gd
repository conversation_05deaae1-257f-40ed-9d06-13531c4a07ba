extends Control

# This signal will be sent to the parent manager script.
# It will carry the name of the category (e.g., "Shirts").
signal category_selected(category_name: String)
signal item_selected(item_index, item_price)
signal clear_skin

var item_index: String
var item_price: String

@onready var price: Label = $Price
@onready var button: Button = $Button # Make sure you have a reference to the button
@onready var texture_rect: TextureRect = $TextureRect
@onready var label: Label = $Label
@onready var coin_icon: TextureRect = $coin_icon


enum SKIN_CARD{NORMAL , RARE, EPIC}
var price_table = {
	SKIN_CARD.NORMAL: 50,
	SKIN_CARD.RARE: 100,
	SKIN_CARD.EPIC: 500
}

# Which rarity this card is
var rarity: SKIN_CARD = SKIN_CARD.NORMAL

func _ready() -> void:
	# Don't overwrite price text for clear skin cards
	if label.text != "clear":
		price.text = str(price_table[rarity])
	
func extract_first_number(text: String) -> String:
	var regex = RegEx.new()
	# The pattern "\\d+" means "match one or more (+) digits (\\d)".
	regex.compile("\\d+")
	
	var result = regex.search(text)
	if result:
		return result.get_string()
	else:
		return "" # Return an empty string if no number was found.



func _on_button_pressed() -> void:
	%Click.play()
	var text_content = label.text
	var current_price = price.text
	print("ON ITEM SELECT " + str(Global.skin_parameters) )
	# Check if the label's text represents a valid integer.
	if text_content == "clear":
		emit_signal("clear_skin")

	if text_content.is_valid_int():
		# If it's a number, emit the item_selected signal.
		#Global.skin_item = int(text_content)
		emit_signal("item_selected", text_content, current_price)
		print("Selected item price: " + price.text)
	else:
		# If it's not a number (e.g., "Shirts"), emit the category_selected signal.
		emit_signal("category_selected", text_content)
		#Global.skin_category = int(extract_first_number(text_content))
		print("Selected category: " + text_content)
