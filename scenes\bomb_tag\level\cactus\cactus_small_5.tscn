[gd_scene load_steps=6 format=4 uid="uid://ddn8ddua3gdpq"]

[ext_resource type="Material" uid="uid://g5x7g5rmse4o" path="res://scenes/bomb_tag/level/cactus/materials/cactus_body.material" id="1_2n57a"]
[ext_resource type="Material" uid="uid://3oxpnywrvsyc" path="res://scenes/bomb_tag/level/cactus/materials/cactus_flowers.material" id="2_hauci"]

[sub_resource type="ArrayMesh" id="ArrayMesh_f3ll6"]
_surfaces = [{
"aabb": AABB(-0.159099, -0.19045, -0.159589, 0.320581, 0.656262, 0.320581),
"format": 34896613377,
"index_count": 216,
"index_data": PackedByteArray("DgAQABoADgAPABAAIwABAAIAIwAiAAEADQAaABsADQAOABoAJAACAAMAJAAjAAIADAAbABwADAANABsAJQADAAQAJQAkAAMACwAcAB0ACwAMABwAJgAEAAUAJgAlAAQACgAdAB4ACgALAB0AJwAFAAYAJwAmAAUACQAeAB8ACQAKAB4AKAAGAAcAKAAnAAYACAAfACAACAAJAB8AAAAIAA8AAAAHAAgABwAJAAgABwAGAAkABgAKAAkABgAFAAoABQALAAoABQAEAAsABAAMAAsABAADAAwAAwANAAwAAwACAA0AAgAOAA0AAgABAA4AAQAPAA4AAQAAAA8AFAASABMAEAAZABoAEAARABkADwAgABAADwAIACAAEQASABkAGQASABgAGAASABcAFwASABYAFgASABUAFQASABQAEQAgABMAEQAQACAAHwATACAAHwAUABMAHgAUAB8AHgAVABQAHQAVAB4AHQAWABUAHAAWAB0AHAAXABYAGwAXABwAGwAYABcAGgAYABsAGgAZABgAEgARABMAIgAAAAEAIgAhAAAABwAhACgABwAAACEA"),
"lods": [0.123836, PackedByteArray("AQARABkAEQASABkAAQATABEAAQAZABgAGQASABgAAwABABgAAwAYABcAGAASABcAEgARABMABQAUABMAFAASABMAFwASABYABQADABcABQAXABYABQAVABQABQAWABUAFQASABQAFgASABUAIwAiAAEAIgAhAAEAJAAjAAEAJAABAAMAJQADAAUAJQAkAAMAJgAlAAUAJwAmAAUAKAAnAAUABQAhACgABQABACEAAQAFABMA")],
"name": "Plant_2",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 41,
"vertex_data": PackedByteArray("+kr0vQAAAAAAAPS9+koAAAAA9L0EtQAA+kr0vf//AAAEtfS9//8AAP//9L0EtQAA///0vfpKAAAEtfS9AAAAADKxOtc4CQAAxvY618xOAADG9jrXMrEAADKxOtfG9gAAzE4618b2AAA4CTrXMrEAADgJOtfMTgAAzE461zgJAACtWafseyMAAPRp+PrISgAA/3////9/AAAKlvj6yEoAADa1+Pr0aQAANrX4+gqWAAAKlvj6NrUAAPRp+Po2tQAAyEr4+gqWAADISvj69GkAAHsjp+ytWQAAeyOn7FGmAACtWafsg9wAAFGmp+yD3AAAg9yn7FGmAACD3KfsrVkAAFGmp+x7IwAApWEAAHM2AAC4NgAAYGEAALg2AAAWngAApWEAAATJAABbngAABMkAAEnJAAAWngAASckAAGBhAABbngAAczYAAA==")
}, {
"aabb": AABB(-0.262655, 2.7388e-07, -0.263421, 0.525313, 0.490306, 0.526842),
"format": 34896613377,
"index_count": 180,
"index_data": PackedByteArray("AwAAAAEAAwACAAAACQAGAAQACQAIAAYABwAIAAkABwAFAAgADQAKAAsADQAMAAoAEwAQAA4AEwASABAAEQASABMAEQAPABIAFwAUABUAFwAWABQAHQAaABgAHQAcABoAGwAcAB0AGwAZABwAIQAeAB8AIQAgAB4AJwAkACIAJwAmACQAJQAmACcAJQAjACYAKwAoACkAKwAqACgAMQAuACwAMQAwAC4ALwAwADEALwAtADAANQAyADMANQA0ADIAOwA4ADYAOwA6ADgAOQA6ADsAOQA3ADoAPwA8AD0APwA+ADwARQBCAEAARQBEAEIAQwBEAEUAQwBBAEQASQBGAEcASQBIAEYATwBMAEoATwBOAEwATQBOAE8ATQBLAE4AVQBRAFQAVQBSAFEAVgBQAFcAVgBTAFAAVABTAFYAVABRAFMAVwBSAFUAVwBQAFIAVwBYAFYAVgBYAFQAVABYAFUAVQBYAFcA"),
"name": "Plant_1",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 89,
"vertex_data": PackedByteArray("20isqs3+AACCXqyqf80AAK5gAACVyAAAE20AAFqsAAD0Z1HZG7gAAJFMA7D79gAAX10J/wXTAACDXtCaf80AAABSPNkA6gAA92CkvPDHAAB9tayq//8AAO6grKo5zgAA4J4AAEPJAAAbkwAAxKwAAPeXUdmiuAAAfbIDsOD3AAAMpAn/59IAAO+g0Jo5zgAA1qw82fzqAACanqS8nMgAAP7/rKoqtgAAfc6sqhShAACIyQAA/p4AADCtAADskgAA5LhR2QCYAAA9+AOwc7IAAOnTCf9DogAAfc7QmhShAAAi6zzZPa0AAODIpLy4ngAA4v+sqs5KAABrzqyqcF8AAHfJAACGYQAAJq0AAFZtAADXuFHZjmgAAOn3A7C6TQAA89IJ/21cAABrztCab18AAAfrPNl3UwAAz8ikvM5hAAA6tayqIAEAAMWgrKpiMgAAuZ4AAFc3AAADkwAAi1MAANiXUdn+RwAAtrEDsMAIAADHoQn/Gy0AAMWg0JpiMgAAl6w82d0VAABznqS8ADgAAL5OrKoAAAAAHGKsqrIxAAAMZAAAszYAACJvAAAnUwAAjWpR2X9HAACOUQOw6QcAABlfCf83LQAAG2LQmrIxAADlVjzZ7xQAAE1kpLxdNwAAegKsqgZIAAB6M6yqvF0AAGI4AADwXwAAcFQAAF5sAADZSFHZVGcAACcKA7CkSwAAFS4J/7JcAAB6M9CavV0AACEXPNkrUQAACDmkvDtgAAAAAKyqWrMAAPYxrKpdnwAA9zYAAGOdAACSUwAA8ZEAAMFHUdnElgAADQgDsFmwAABbLQn/iaIAAPYx0JpdnwAAEhU82euqAACgN6S8IZ0AACpPDf9UrAAAnLL//yVNAAAqT7n/JU0AAJyyVf9UrAAA/JZY9sJpAABdaVj2wmkAAPyWWPZGkwAAXWlY9kaTAAAtgDXzhH4AAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_8rimp"]
resource_name = "CanyonPlantsRocksTiles_Cactus_Small_5"
_surfaces = [{
"aabb": AABB(-0.159099, -0.19045, -0.159589, 0.320581, 0.656262, 0.320581),
"attribute_data": PackedByteArray("hTJMEKXQ8hhtPEwQOMhrI1RGTBAryaww/wBMEDxQTBCE06Q55wpMECjhVDjPFEwQ4+noLbYeTBDG6HIgnihMECjetRd73Codn+MQI2rkPCyE3lIzTdUjNEzOIi6DzRIlWdMLHmLV4iEa1yoldtiAKH/Y4Si02DYoydgfKRXZLSgq2RYpX9lrKGjZzCgc2uIkbdzSJrPc0ynD2iQswddqLHLVeSot1XsnqNFfJirSKyyj1vQvedxrLz/g7yq13xclNdtYIYUyQjZtPEI2VEZCNv8AQjY8UEI25wpCNs8UQja2HkI2nihCNm08TBBtPEwQOMhrIzjIayM4yGsj/wBMEDxQTBCE06Q5hNOkOc8UTBDPFEwQzxRMEOPp6C3j6egt4+noLRrXKiUc2uIkHNriJBza4iRt3NImw9okLMHXaizB12osctV5KoUyQjaFMkI2VEZCNjxQQjbnCkI25wpCNrYeQjaeKEI2"),
"format": 34896613399,
"index_count": 216,
"index_data": PackedByteArray("FwAZACoAFwAYABkAMwACAAQAMwAyAAIAFgAqACsAFgAXACoANQAEAAcANQAzAAQAFQArACwAFQAWACsANgAGAAkANgA0AAYAFAAsAC0AFAAVACwANwAJAAsANwA2AAkAEwAtAC4AEwAUAC0AOAALAA0AOAA3AAsAEgAuAC8AEgATAC4AOQANAA8AOQA4AA0AEQAvADAAEQASAC8AAQARABgAAQAQABEAEAASABEAEAAOABIADgATABIADgAMABMADAAUABMADAAKABQACgAVABQACgAIABUACAAWABUACAAFABYABQAXABYABQADABcAAwAYABcAAwABABgAJAAhACMAGQApACoAGQAaACkAGAAwABkAGAARADAAGgAdACkAKQAbACgAKAAcACcAJwAeACYAJgAgACUAJQAiACQAGgAwACMAGgAZADAALwAjADAALwAkACMALgAkAC8ALgAlACQALQAlAC4ALQAmACUALAAmAC0ALAAnACYAKwAnACwAKwAoACcAKgAoACsAKgApACgAHwAaACMAMgAAAAIAMgAxAAAADwAxADkADwAAADEA"),
"lods": [0.123836, PackedByteArray("PABJACkASQAdACkAPQBLAEkAPAApAFEAKQAbAFEAQQA8AFEAQQBRAE8AUQAcAE8AHwBJAEoARwBNAEoATQAhAEoATwAeAE4ARgBCAE8ARgBQAE4ARgAlAE0ARgBOACUAJQAiAE0ATgAgACUAVAAyADoAMgBSADoAVQBUADoAVQA6AEAAVgA/AEMAVgA0AD8ANwBXAEMAWAA3AEMAWQBYAEQARQBTAFkARQA7AFMAPgBIAEwA")],
"material": ExtResource("1_2n57a"),
"name": "Plant_2",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 90,
"vertex_data": PackedByteArray("+kr0vQAA/+/6SvS9AACN+wAA9L36Sv7PAAD0vfpKHeUAAPS9BLX/rwAA9L0EtbLB+kr0vf//A5D6SvS9//8FkPpK9L3//5WTBLX0vf//A5AEtfS9///MmP//9L0Etf+v///0vQS1gML///S9+kr+z///9L36SvzlBLX0vQAA/+8EtfS9AAAB/TKxOtc4CWfvxvY618xOit3G9jrXMrFRvzKxOtfG9uybzE4618b29pY4CTrXMrGzujgJOtfMTu7ZzE461zgJyO2tWafseyNy3/Rp+PrISnfQ/3////9/K8D/f////38EwP9/////f17A/3////9/G8D/f////38cwP9/////f1zA/3////9/BMD/f////38rwAqW+PrISvDQNrX4+vRp6sg2tfj6Cpb7uwqW+Po2tXiw9Gn4+ja1sK/ISvj6CpZvushK+Pr0aaLHeyOn7K1ZM9B7I6fsUaZzuK1Zp+yD3MqhUaan7IPcO6SD3KfsUaZuvIPcp+ytWVbTUaan7HsjjuClYQAAczYL8Lg2AABgYSjQuDYAABaeWbClYQAABMklkaVhAAAEyYCRW54AAATJI5FJyQAAFp5ZsEnJAABgYSjQW54AAHM2C/AAAPS9+kqSxwAA9L36SgrfAAD0vfpKaN4AAPS9+kpS8gAA9L36Sj3y+kr0vf//0Yv6SvS9//8+mvpK9L3//y6h+kr0vf//G5j///S9BLWIrP//9L0EtfW9///0vQS14cr///S9BLUYv///9L0Ete3P///0vQS1Jej0afj6yErK0QqW+PrISoDMCpb4+shK8t4Klvj6yEpG9Da1+Pr0abbGCpb4+ja17a70afj6NrX1qvRp+Po2tRafyEr4+gqWYbelYQAAczaT5KVhAABzNsXyuDYAABaeD7SlYQAABMmznVueAAAEyT6NW54AAATJ1JxJyQAAYGFpy1ueAABzNrfmPYCK/tbqtKbNgP39hdprycGBD/2t0BnfW4fb+SWFZ/eevCzz04ayBduO2kzugsIBR6GqUQGCzQBTt8tbdoE+ALzfpXLu5HZ2SsEtY8GvgluRsZxg4tHax1LUmNEU3OXBFevFot3tt5vR8neRNvFjh3n6woJs60iK6/MJhqbzK4aU6zSKQfregkTxXIeC8Vx8dd0OchrYvXBy5+R5lPD1lKrocqfQ6eSjxOAgttrbh7+24uaqCM6absHATmXLzDtqJOpLeWJ1PP6qcUH6n2vI801cSdUlUdrjcl45KMlzXxRCelQOPH6bCk14LP2qiTL/aOb+wf/xr6RNXzElJFGh3Hp5O/lTx1fKR+sOtOl3jQ1LeM8NLHQIILquI17luHVRM7CGN3TraJgq6NlyiN+9mHnOelNh2C9tGuA+cpvycpS27+FifePcrf+Fbv8BpSD3YW8V9vllzPIZYRwlXWpAGoB7gwsIgCQF")
}, {
"aabb": AABB(-0.262655, 2.7388e-07, -0.263421, 0.525313, 0.490306, 0.526842),
"attribute_data": PackedByteArray("/7//v/+/P97/f/+//38/3v+PP97/f/+//4//v/9/P97/h/+//4f/v/+HP97/hz/e/7//v/+/P97/f/+//38/3v+PP97/f/+//4//v/9/P97/h/+//4f/v/+HP97/hz/e/7//v/+/P97/f/+//38/3v+PP97/f/+//4//v/9/P97/h/+//4f/v/+HP97/hz/e/7//v/+/P97/f/+//38/3v+PP97/f/+//4//v/9/P97/h/+//4f/v/+HP97/hz/e/7//v/+/P97/f/+//38/3v+PP97/f/+//4//v/9/P97/h/+//4f/v/+HP97/hz/e/7//v/+/P97/f/+//38/3v+PP97/f/+//4//v/9/P97/h/+//4f/v/+HP97/hz/e/7//v/+/P97/f/+//38/3v+PP97/f/+//4//v/9/P97/h/+//4f/v/+HP97/hz/e/7//v/+/P97/f/+//38/3v+PP97/f/+//4//v/9/P97/h/+//4f/v/+HP97/hz/emKCRvf+v/5+YoP+f/6+Rvbir4qi4q+Kop6TiqKek4qinpOKop6TiqLiryLW4q8i1p6TItaekyLWnpMi1L6hVrw=="),
"format": 34896613399,
"index_count": 180,
"index_data": PackedByteArray("AwAAAAEAAwACAAAACgAGAAQACgAIAAYABwAJAAsABwAFAAkADwAMAA0ADwAOAAwAFwASABAAFwAVABIAEwAUABYAEwARABQAGwAYABkAGwAaABgAIwAeABwAIwAhAB4AHwAgACIAHwAdACAAJwAkACUAJwAmACQALwAqACgALwAtACoAKwAsAC4AKwApACwAMwAwADEAMwAyADAAOwA2ADQAOwA5ADYANwA4ADoANwA1ADgAPwA8AD0APwA+ADwARgBCAEAARgBEAEIAQwBFAEcAQwBBAEUASwBIAEkASwBKAEgAUgBOAEwAUgBQAE4ATwBRAFMATwBNAFEAVwBUAFUAVwBWAFQAXgBaAFgAXgBcAFoAWwBdAF8AWwBZAF0AaQBhAGQAaQBiAGEAawBgAG0AawBjAGAAZQBjAGoAZQBhAGMAbgBiAGcAbgBgAGIAbABvAGoAagBvAGQAZABvAGgAZgBvAGwA"),
"material": ExtResource("2_hauci"),
"name": "Plant_1",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 112,
"vertex_data": PackedByteArray("20isqs3+ytOCXqyqf80/065gAACVyHDXE20AAFqshtb0Z1HZG7havJFMA7D79oPCX10J/wXTkbuDXtCaf826xABSPNkA6he7AFI82QDqC8T3YKS88Me3u/dgpLzwx/TGfbWsqv//cuPuoKyqOc4e4+CeAABDya3lG5MAAMSsHeX3l1HZorgY1n2yA7Dg96TZDKQJ/+fSstXvoNCaOc7b2tasPNn86nva1qw82fzqddWanqS8nMgY3JqepLycyMXV/v+sqiq24vV9zqyqFKHG9YjJAAD+nqX2MK0AAOySdPbkuFHZAJhY8T34A7Bzstry6dMJ/0OiOfF9ztCaFKE/8yLrPNk9rSDzIus82T2tJ/HgyKS8uJ6n8+DIpLy4nj/x4v+sqs5KEvZrzqyqcF/29XfJAACGYdH2Jq0AAFZtofbXuFHZjmgQ8un3A7C6TXry89IJ/21c7fFrztCab1/e8gfrPNl3U8DyB+s82XdT1/HPyKS8zmFG88/IpLzOYfPxOrWsqiABc+PFoKyqYjIg47meAABXN6jlA5MAAItTG+XYl1HZ/ke51raxA7DACDbZx6EJ/xstT9bFoNCaYjJs2pesPNndFQ3al6w82d0VDtZznqS8ADis23OepLwAOGLWvk6sqgAAaNQcYqyqsjHi0wxkAACzNvbXIm8AACdTFNeNalHZf0c5vo5RA7DpB0zDGV8J/zctc70bYtCasjF0xeVWPNnvFPu85VY82e8UzcRNZKS8XTeYvU1kpLxdN6XHegKsqgZIJst6M6yqvF16ymI4AADwX63PcFQAAF5sjs7ZSFHZVGfqqycKA7CkSx60FS4J/7Jcr6p6M9CavV06tyEXPNkrUeypIRc82StRSrYIOaS8O2Dpqgg5pLw7YE66AACsqlqz9cr2MayqXZ9Gyvc2AABjnYXPklMAAPGRY87BR1HZxJYLqw0IA7BZsM2zWy0J/4miyqn2MdCaXZ/xthIVPNnrqgWpEhU82euq/bWgN6S8IZ0GqqA3pLwhnQq6Kk8N/1SsdMacsv//JU32uSpPuf8lTcG5nLJV/1SsQMb8llj2wmncuvyWWPbCafa6XWlY9sJp77pdaVj2wmkGu11pWPbCaeO6XWlY9sJp4rr8llj2RpPCxfyWWPZGk8jFXWlY9kaTtsVdaVj2RpO8xV1pWPZGk6PFLYA184R+/79HHqEmsR53JpMbsic9HG8nhTUbHmMuSSDGNqAd5CtHIZI3Uh2jLPsgiTa3HZEpNCLjKCs0YinfM5klIzZqJqc1H0P+JC47QCloRDwkeTjhKkk5Yyo3RcEj6TVrLClEYSR4MlxAAzPoP9UuYEO9L6BCrE1jKpFFjjDoTl8p20LSMqxDIzKtT7woPkD/NKxOkClmzYm/3Mz8vwbRg7wg0EO93LI91nC6Yc+nsUHXKL0gzVi8zM3lsOTXx7/zyuGxENcG19TLidYfzEra38l92VrK4rwH3PjEr9adu8zcq8cT1d7GjdXQukjdPsqK09q7p9wJ4ZLYn+C92MPjdtcZ47vX5MgG4wrRF9+kx4zji9MV3tjG4ePO0mHe4Mdz4+XVIt2K8EnsTfBU7BryBOy58RTs1N9o8InmD+7e3p3wPOjF7UDev/C859vtDd+T8Mjpgu1tDmASpw5XEvQMnRJPDY8SWhxeEUkYzhBCHUMRqBYUEdcdMhEjF/8QFh1IESwVUxH/7rN18fh2ktTtP3mp9UuR9PcUkLv5SJCb70N4cu+xebbvUne27053+fgEjnv59I2/8d54z/FkeYXxAHf+//9/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_f3ll6")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_axtgq"]
data = PackedVector3Array(-0.0652035, 0.296517, -0.159589, 0.0491809, 0.416227, -0.115156, -0.0467987, 0.416227, -0.115156, -0.0652035, 0.296517, -0.159589, 0.0675856, 0.296517, -0.159589, 0.0491809, 0.416227, -0.115156, -0.0905743, -0.19045, 0.0383832, -0.159099, 0.296517, -0.0656926, -0.159099, 0.296517, 0.0670965, -0.0905743, -0.19045, 0.0383832, -0.0905743, -0.19045, -0.037643, -0.159099, 0.296517, -0.0656926, -0.159099, 0.296517, -0.0656926, -0.0467987, 0.416227, -0.115156, -0.114667, 0.416227, -0.0472878, -0.159099, 0.296517, -0.0656926, -0.0652035, 0.296517, -0.159589, -0.0467987, 0.416227, -0.115156, -0.0368158, -0.19045, 0.0921417, -0.159099, 0.296517, 0.0670965, -0.0652035, 0.296517, 0.160992, -0.0368158, -0.19045, 0.0921417, -0.0905743, -0.19045, 0.0383832, -0.159099, 0.296517, 0.0670965, -0.159099, 0.296517, 0.0670965, -0.114667, 0.416227, -0.0472878, -0.114667, 0.416227, 0.0486917, -0.159099, 0.296517, 0.0670965, -0.159099, 0.296517, -0.0656926, -0.114667, 0.416227, -0.0472878, 0.0392103, -0.19045, 0.0921417, -0.0652035, 0.296517, 0.160992, 0.0675856, 0.296517, 0.160992, 0.0392103, -0.19045, 0.0921417, -0.0368158, -0.19045, 0.0921417, -0.0652035, 0.296517, 0.160992, -0.0652035, 0.296517, 0.160992, -0.114667, 0.416227, 0.0486917, -0.0467987, 0.416227, 0.11656, -0.0652035, 0.296517, 0.160992, -0.159099, 0.296517, 0.0670965, -0.114667, 0.416227, 0.0486917, 0.0929688, -0.19045, 0.0383832, 0.0675856, 0.296517, 0.160992, 0.161482, 0.296517, 0.0670965, 0.0929688, -0.19045, 0.0383832, 0.0392103, -0.19045, 0.0921417, 0.0675856, 0.296517, 0.160992, 0.0675856, 0.296517, 0.160992, -0.0467987, 0.416227, 0.11656, 0.0491809, 0.416227, 0.11656, 0.0675856, 0.296517, 0.160992, -0.0652035, 0.296517, 0.160992, -0.0467987, 0.416227, 0.11656, 0.0929688, -0.19045, -0.037643, 0.161482, 0.296517, 0.0670965, 0.161482, 0.296517, -0.0656926, 0.0929688, -0.19045, -0.037643, 0.0929688, -0.19045, 0.0383832, 0.161482, 0.296517, 0.0670965, 0.161482, 0.296517, 0.0670965, 0.0491809, 0.416227, 0.11656, 0.117049, 0.416227, 0.0486917, 0.161482, 0.296517, 0.0670965, 0.0675856, 0.296517, 0.160992, 0.0491809, 0.416227, 0.11656, 0.0392103, -0.19045, -0.0914015, 0.161482, 0.296517, -0.0656926, 0.0675856, 0.296517, -0.159589, 0.0392103, -0.19045, -0.0914015, 0.0929688, -0.19045, -0.037643, 0.161482, 0.296517, -0.0656926, 0.161482, 0.296517, -0.0656926, 0.117049, 0.416227, 0.0486917, 0.117049, 0.416227, -0.0472878, 0.161482, 0.296517, -0.0656926, 0.161482, 0.296517, 0.0670965, 0.117049, 0.416227, 0.0486917, 0.00119108, 0.465813, 0.000701968, -0.0467987, 0.416227, -0.115156, 0.0491809, 0.416227, -0.115156, 0.117049, 0.416227, 0.0486917, 0.00119108, 0.465813, 0.000701968, 0.117049, 0.416227, -0.0472878, -0.114667, 0.416227, -0.0472878, -0.0467987, 0.416227, -0.115156, 0.00119108, 0.465813, 0.000701968, -0.114667, 0.416227, -0.0472878, 0.00119108, 0.465813, 0.000701968, -0.114667, 0.416227, 0.0486917, -0.114667, 0.416227, 0.0486917, 0.00119108, 0.465813, 0.000701968, -0.0467987, 0.416227, 0.11656, -0.0467987, 0.416227, 0.11656, 0.00119108, 0.465813, 0.000701968, 0.0491809, 0.416227, 0.11656, 0.0491809, 0.416227, 0.11656, 0.00119108, 0.465813, 0.000701968, 0.117049, 0.416227, 0.0486917, 0.117049, 0.416227, -0.0472878, 0.00119108, 0.465813, 0.000701968, 0.0491809, 0.416227, -0.115156, -0.0905743, -0.19045, -0.037643, -0.0652035, 0.296517, -0.159589, -0.159099, 0.296517, -0.0656926, -0.0905743, -0.19045, -0.037643, -0.0368158, -0.19045, -0.0914015, -0.0652035, 0.296517, -0.159589, 0.0675856, 0.296517, -0.159589, -0.0368158, -0.19045, -0.0914015, 0.0392103, -0.19045, -0.0914015, 0.0675856, 0.296517, -0.159589, -0.0652035, 0.296517, -0.159589, -0.0368158, -0.19045, -0.0914015, 0.0675856, 0.296517, -0.159589, 0.117049, 0.416227, -0.0472878, 0.0491809, 0.416227, -0.115156, 0.0675856, 0.296517, -0.159589, 0.161482, 0.296517, -0.0656926, 0.117049, 0.416227, -0.0472878)

[node name="Cactus_Small_5" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_8rimp")
skeleton = NodePath("")

[node name="Cactus_Small_5" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="Cactus_Small_5"]
shape = SubResource("ConcavePolygonShape3D_axtgq")
