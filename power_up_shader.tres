[gd_resource type="VisualShader" load_steps=14 format=3 uid="uid://cfjms7igvjapc"]

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_5ke1f"]
operator = 2

[sub_resource type="VisualShaderNodeTexture2DParameter" id="VisualShaderNodeTexture2DParameter_geb36"]
parameter_name = "aura_texture"
texture_type = 1

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_3fgow"]
source = 5
texture_type = 1

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_geb36"]
default_input_values = [0, 0.0, 1, 3.0]
operator = 2

[sub_resource type="Gradient" id="Gradient_5ke1f"]
offsets = PackedFloat32Array(0, 0.98419, 1)
colors = PackedColorArray(0, 0, 0, 1, 1, 1, 1, 1, 0.0790514, 0.0790514, 0.0790514, 1)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_geb36"]
gradient = SubResource("Gradient_5ke1f")
fill_from = Vector2(1, 0)
fill_to = Vector2(1, 1)

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_7sndf"]
expanded_output_ports = [0]
texture = SubResource("GradientTexture2D_geb36")
texture_type = 1

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_3fgow"]
expanded_output_ports = [0]
input_name = "color"

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_u3l6l"]
operator = 2

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_e0dcu"]
operator = 2

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_53rsw"]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_0xt23"]
input_name = "time"

[sub_resource type="VisualShaderNodeVec2Parameter" id="VisualShaderNodeVec2Parameter_u3l6l"]
parameter_name = "speed"

[resource]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_disabled, diffuse_lambert, specular_schlick_ggx, unshaded, shadows_disabled;

uniform vec2 speed;
uniform sampler2D aura_texture : source_color;
uniform sampler2D tex_frg_2 : source_color;



void fragment() {
// Input:3
	vec4 n_out3p0 = COLOR;
	float n_out3p4 = n_out3p0.a;


// Vector2Parameter:9
	vec2 n_out9p0 = speed;


// Input:8
	float n_out8p0 = TIME;


// FloatOp:10
	float n_out10p0 = n_out9p0.x * n_out8p0;


// UVFunc:7
	vec2 n_in7p1 = vec2(1.00000, 1.00000);
	vec2 n_out7p0 = vec2(n_out10p0) * n_in7p1 + UV;


	vec4 n_out12p0;
// Texture2D:12
	n_out12p0 = texture(aura_texture, n_out7p0);


// Texture2D:2
	vec4 n_out2p0 = texture(tex_frg_2, UV);
	float n_out2p1 = n_out2p0.r;


// FloatOp:6
	float n_out6p0 = n_out12p0.x * n_out2p1;


// FloatOp:4
	float n_out4p0 = n_out3p4 * n_out6p0;


// FloatOp:13
	float n_in13p1 = 3.00000;
	float n_out13p0 = n_out4p0 * n_in13p1;


// Output:0
	ALBEDO = vec3(n_out3p0.xyz);
	ALPHA = n_out13p0;


}
"
modes/cull = 2
flags/unshaded = true
flags/shadows_disabled = true
nodes/fragment/2/node = SubResource("VisualShaderNodeTexture_7sndf")
nodes/fragment/2/position = Vector2(-820, 720)
nodes/fragment/3/node = SubResource("VisualShaderNodeInput_3fgow")
nodes/fragment/3/position = Vector2(-400, 40)
nodes/fragment/4/node = SubResource("VisualShaderNodeFloatOp_u3l6l")
nodes/fragment/4/position = Vector2(-40, 360)
nodes/fragment/6/node = SubResource("VisualShaderNodeFloatOp_e0dcu")
nodes/fragment/6/position = Vector2(-340, 580)
nodes/fragment/7/node = SubResource("VisualShaderNodeUVFunc_53rsw")
nodes/fragment/7/position = Vector2(-1100, 380)
nodes/fragment/8/node = SubResource("VisualShaderNodeInput_0xt23")
nodes/fragment/8/position = Vector2(-1720, 300)
nodes/fragment/9/node = SubResource("VisualShaderNodeVec2Parameter_u3l6l")
nodes/fragment/9/position = Vector2(-1700, 520)
nodes/fragment/10/node = SubResource("VisualShaderNodeFloatOp_5ke1f")
nodes/fragment/10/position = Vector2(-1360, 540)
nodes/fragment/11/node = SubResource("VisualShaderNodeTexture2DParameter_geb36")
nodes/fragment/11/position = Vector2(-1600, 820)
nodes/fragment/12/node = SubResource("VisualShaderNodeTexture_3fgow")
nodes/fragment/12/position = Vector2(-780, 360)
nodes/fragment/13/node = SubResource("VisualShaderNodeFloatOp_geb36")
nodes/fragment/13/position = Vector2(160, 280)
nodes/fragment/connections = PackedInt32Array(3, 0, 0, 0, 3, 4, 4, 0, 2, 1, 6, 1, 6, 0, 4, 1, 9, 0, 10, 0, 8, 0, 10, 1, 10, 0, 7, 2, 11, 0, 12, 2, 7, 0, 12, 0, 12, 0, 6, 0, 4, 0, 13, 0, 13, 0, 0, 1)
