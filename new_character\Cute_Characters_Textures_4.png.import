[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://tio01tmpd1xi"
path.s3tc="res://.godot/imported/Cute_Characters_Textures_4.png-dcfa1303033cd1daacc45b89b047ddce.s3tc.ctex"
path.etc2="res://.godot/imported/Cute_Characters_Textures_4.png-dcfa1303033cd1daacc45b89b047ddce.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}
generator_parameters={
"md5": "b250b807dfc1a5eb051d5b351e7c1fc8"
}

[deps]

source_file="res://new_character/Cute_Characters_Textures_4.png"
dest_files=["res://.godot/imported/Cute_Characters_Textures_4.png-dcfa1303033cd1daacc45b89b047ddce.s3tc.ctex", "res://.godot/imported/Cute_Characters_Textures_4.png-dcfa1303033cd1daacc45b89b047ddce.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
