[gd_scene load_steps=6 format=4 uid="uid://ooqkqdfb1vdd"]

[ext_resource type="PackedScene" uid="uid://8iixcptellje" path="res://assets/nature/fence_corner.glb" id="1_1vij3"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_n8syx"]
resource_name = "woodDark"
albedo_color = Color(0.7265625, 0.5601253, 0.47370598, 1)
metallic = 1.0

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_pov8x"]
resource_name = "wood"
albedo_color = Color(0.7254902, 0.56078434, 0.4745098, 1)
metallic = 1.0

[sub_resource type="ArrayMesh" id="ArrayMesh_dnu2e"]
_surfaces = [{
"aabb": AABB(-0.43, 0.06210492, -0.47794998, 0.90795, 0.22081749, 0.90795),
"format": ***********,
"index_count": 48,
"index_data": PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAZABoAGgAbABgAHAAdAB4AHgAfABwA"),
"name": "woodDark",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("sfj//4QNAACx+P////8AALH4/7f//wAAsfj/t4QNAAAAAP9HAAAAAAAAAAAAAAAAevIAAAAAAAB68v9HAAAAAP//AACEDQAA//8AAP//AAD///9H//8AAP///0eEDQAA////t4QNAAD///+3//8AAP///////wAA/////4QNAAB68v//TQcAAHry/7dNBwAAAAD/t00HAAAAAP//TQcAALH4/0eEDQAAsfj/R///AACx+AAA//8AALH4AACEDQAAAAD//wAAAAAAAP+3AAAAAHry/7cAAAAAevL//wAAAAB68v9HTQcAAHryAABNBwAAAAAAAE0HAAAAAP9HTQcAAA==")
}, {
"aabb": AABB(-0.5, 0, -0.5, 1, 0.3450273, 1),
"format": ***********,
"index_count": 300,
"index_data": PackedByteArray("AAABAAIAAgADAAAAAQAAAAQAAwACAAUAAQAEAAYABwAEAAAABAAHAAUACAAHAAAABQAJAAQACQAFAAIABAAKAAYACwAJAAIACAAAAAwACwACAA0ACAAOAAcACgAOAAYACAAGAA4ADgAKAA8ACwAPAAkADwAQAA4AEAAPABEABQAQABEACwARAA8AAwAFABEAEgADABEAEwALAA0ADQASABMAEgANABQAFQATABIAEgARABUAFAAMABIAFgAVABEAEwAVABYAEQAXABYAGAAXABEAFgAXABgAEQALABgADAAUABkAGQAIAAwAFgAaABMAGAAaABYACwATABsAGwAYAAsAEwAaABwAHAAbABMAGgAdABwAGgAYAB0AHAAeABsAGwAfABgAIAAcAB0AIQAfABsAHAAiAB4AIwAdABgAHwAkABgAJAAjABgAJAAeACIAIgAjACQAJQAeACQAJgAjACIAJAAnACUAJQAnACYAIgAoACYAJgAoACUAKQAlACgAGwApACEAKQAoACAAJgAnACoAJwAhACoAKwAmACoAHQArACAAIAArACoALAAhACkALAAqACEAKQAgAC0ALQAsACkAIAAqAC4ALgAtACAAKgAsAC8ALwAsAC0AMAAuACoALQAuADAALwAxACoAKgAxADAAMAAxAC8ALQAyAC8ALwAyADAAMAAyAC0AGQAzADQAMwAZABQAFAAGADMANAAzAAYAFAABAAYANAA1ABkAGQA1ADYANgA1ADQANgAIABkABgAIADYANAA3ADYANgA3AAYABgA3ADQA"),
"name": "wood",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 56,
"vertex_data": PackedByteArray("WfooXBPuAAC48yhcE+4AALjzKFzrEQAAWfooXOsRAAC489ajE+4AAFn61qPrEQAAE+7//xPuAABZ+tajE+4AAP////8T7gAAuPPWo+sRAAC48+rRE+4AABPu///rEQAAWfoULhPuAAC48xQu6xEAAFn66tET7gAAuPPq0esRAABZ+urR6xEAAP/////rEQAAWfoULusRAAAT7gAA6xEAALjzFC4T7gAA//8AAOsRAAD//wAAAAAAAP////8AAAAAE+7//wAAAAD//wAAE+4AABPuAAAAAAAAE+7q0UYMAAAT7hQuRgwAABPuFC6lBQAAE+7Wo0YMAAAT7urRpQUAAOsRFC5GDAAA6xHq0aUFAAAT7ihcRgwAABPuKFylBQAAE+7Wo6UFAADrEdajRgwAAOsRKFylBQAA6xHWo6UFAADrEShcRgwAAOsR6tFGDAAA6xEAAAAAAADrERQupQUAAOsR//8AAAAA6xH//+sRAADrEQAA6xEAAAAA//8AAAAAAAAAAOsRAAAAAAAAAAAAAAAA///rEQAAE+4AABPuAAAT7gAA//8AAP//AAD//wAA////////AAAT7v////8AAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_vg33r"]
resource_name = "fence_corner_Mesh fence_corner"
_surfaces = [{
"aabb": AABB(-0.43, 0.06210492, -0.47794998, 0.90795, 0.22081749, 0.90795),
"attribute_data": PackedByteArray("AAAAAP//AAD//94eAADeHv//4k7//8FtAADBbQAA4k7//8FtAADBbQAA4k7//+JO///eHgAA3h4AAAAA//8AAP//AAD//94eAADeHgAAAAAAAOJO///iTv//wW0AAMFt//8AAP//3h4AAN4eAAAAAP//4k7//8FtAADBbQAA4k4="),
"format": ***********,
"index_count": 48,
"index_data": PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAZABoAGgAbABgAHAAdAB4AHgAfABwA"),
"material": SubResource("StandardMaterial3D_n8syx"),
"name": "woodDark",
"primitive": 3,
"uv_scale": Vector4(33.85827, 20.277355, 0, 0),
"vertex_count": 32,
"vertex_data": PackedByteArray("sfj//4QN/7+x+P//////v7H4/7f///+/sfj/t4QN/78AAP9HAAD//wAAAAAAAP//evIAAAAA//968v9HAAD/////AACEDf+///8AAP///7////9H////v////0eEDf+/////t4QN/7////+3////v/////////+//////4QN/7968v//TQcAgHry/7dNBwCAAAD/t00HAIAAAP//TQcAgLH4/0eEDf+/sfj/R////7+x+AAA////v7H4AACEDf+/AAD//wAA//8AAP+3AAD//3ry/7cAAP//evL//wAA//968v9HTQcAgHryAABNBwCAAAAAAE0HAIAAAP9HTQcAgP9/////f////3////9/////f////3////9/////f////38AAP9/AAD/fwAA/38AAP9/AAD/fwAA/38AAP9/AAD/f////3////9/////f////3////9/////f////3////9/////f////3////9/////f////3////9/////f///")
}, {
"aabb": AABB(-0.5, 0, -0.5, 1, 0.3450273, 1),
"attribute_data": PackedByteArray("Wfrx7rjz8e64820dWfptHesRvxET7r8RE+4OGOsRDhgAAPHu6xHx7usR//8AAP//6xEhMusRL4YAAC+GAAAhMv//ITL//y+GE+4vhhPuITJZ+u1nuPPtZ7jzY1BZ+mNQuPNCQf//L4ZZ+g53WfpCQbjzDnf//yEyE+4vhhPuITLrES+GAAAvhkYMDnelBQ536xEhMgAAITKlBe1nRgztZ0YMY1ClBWNQRgxCQaUFQkHrEb8RE+6/ERPuDhjrEQ4YE+4hMv//ITL//y+GE+4vhhPuYAz//2AM//9tHRPubR3//y+GE+4vhhPuITL//yEyAAAhMusRITLrES+GAAAvhqUFbR1GDG0dRgzx7qUF8e7rES+GAAAvhgAAITLrESEyWfrx7rjz8e64820dWfptHf//bR0T7m0dE+5gDP//YAz//yEy//8vhhPuL4YT7iEy6xEhMusRL4ZGDEJBAAAhMkYMDncAAC+GpQVCQUYMY1ClBWNQpQUOd0YM7WelBe1n6xEhMusRL4YAAC+GAAAhMgAAYAzrEWAM6xFtHQAAbR2lBW0dRgxtHUYM8e6lBfHuE+4OGOsRDhjrEb8RE+6/ERPuDhjrEQ4Y6xG/ERPuvxH//y+GE+4vhln6Dne48w53//8hMhPuITK48+1nWfrtZ1n6Y1C482NQWfpCQbjzQkHrEW0dAABtHQAAYAzrEWAM/////xPu//8T7vHu///x7g=="),
"format": ***********,
"index_count": 300,
"index_data": PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAWABUAFAAXABkAGgAUABkAFwAbABkAHAAaABkAGAAVABwAHQAZABsAHQAbABgAHAAZAB4AHwAdABgAGAAcAB8AHgAfABwAIAAhACIAIwAiACEAIgAkACAAIQAlACMAJQAmACMAIgAnACQAJwAmACgAJwAoACQAKQAoACYAJQApACYAKAAqACQAJQArACkAKgArACQAJQAkACsALAAtAC4ALgAvACwAMAAxADIAMgAzADAANAA1ADYANgA3ADQAOAA5ADoAOgA7ADgAPAA9AD4APgA/ADwAQABBAEIAQgBDAEAARABFAEYARgBHAEQASABJAEoASgBLAEgATABNAE4ATgBPAEwAUABRAFIAUgBTAFAAVABVAFYAVgBXAFQAWABWAFUAVQBZAFgAVgBaAFcAWABbAFYAWgBcAFcAWQBdAFgAWQBXAF0AWABeAFsAXABbAF4AXwBdAFcAXgBfAFwAXABfAFcAYABhAGIAYgBjAGAAZABlAGYAZgBnAGQAaABpAGoAagBrAGgAbABtAG4AbgBvAGwAcABxAHIAcgBzAHAAdAB1AHYAdwB2AHUAdgB4AHQAdQB5AHcAeQB6AHcAdgB7AHgAewB6AHwAewB8AHgAfQB8AHoAeQB9AHoAfAB+AHgAeQB/AH0AfgB/AHgAeQB4AH8AgACBAIIAggCDAIAAhACFAIYAhgCHAIQA"),
"material": SubResource("StandardMaterial3D_pov8x"),
"name": "wood",
"primitive": 3,
"uv_scale": Vector4(39.37008, 41.37008, 0, 0),
"vertex_count": 136,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_dnu2e")

[node name="fence_corner2" instance=ExtResource("1_1vij3")]

[node name="fence_corner" parent="tmpParent" index="0"]
mesh = SubResource("ArrayMesh_vg33r")
