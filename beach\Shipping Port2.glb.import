[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://o21ukdfa1uj"
path="res://.godot/imported/Shipping Port2.glb-d95e66bf3571003b407731d44f3bde52.scn"

[deps]

source_file="res://beach/Shipping Port2.glb"
dest_files=["res://.godot/imported/Shipping Port2.glb-d95e66bf3571003b407731d44f3bde52.scn"]

[params]

nodes/root_type="StaticBody3D"
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
nodes/import_as_skeleton_bones=false
nodes/use_node_type_suffixes=true
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
animation/import_rest_as_RESET=false
import_script/path=""
_subresources={}
gltf/naming_version=1
gltf/embedded_image_handling=1
