extends Control

### SERVER VARS ###
const HOST: String = "**************"
const PORT: int = 5353
const Client = preload("res://scripts/networking/client_tcp.gd")
var _client: Client = Client.new()
const RECONNECT_TIMEOUT: float = 3.0

# MENU SIGNALS
signal enter_menu

# PROFILE SIGNALS
signal update_profile
signal show_change_name_panel
signal show_change_bio_panel
signal show_change_pic_panel
signal buyskin(status:int)

#RANKING SIGNALS
signal gbucoin_received(data: Dictionary)
signal gbuwcup_received(data: Dictionary)
signal gbuscup_received(data: Dictionary)
signal gbucup_received(data: Dictionary)

# FRIENDS SIGNALS
signal frget_received(data:Dictionary)
signal frreqget_received(data:Dictionary)
signal fraccept_received(data:Dictionary)
signal frsearch_received(data: Dictionary)
signal frsendreq_received(data: Dictionary)

signal frchat_received(data:Dictionary)
signal gudph_received(data:Dictionary)

# CHATS SIGNALS
signal chatroommsg_recieved(data:Dictionary)
var last_chatroom_data: Dictionary = {}

# SHOP SIGNALS
signal gshopd_recieved(data: Dictionary)
var shop_data: Dictionary = {}

# GAME SEARCH SIGNAL
signal gnsearch_recieved(data:Dictionary)
signal gamestart_recieved(data:Dictionary)

#GAME OVER SIGNAL
signal gameover(data:Dictionary)

signal client_connected
signal permission_granted

var is_permission_granted: bool = false


var plugin
var database: SQLite

func _ready() -> void:
	if Engine.has_singleton("GodotFCM"):
		print("PLUGIN FOUND!")
		plugin = Engine.get_singleton("GodotFCM")
		plugin.fcmsignal.connect(_on_signal_recieved)
		plugin.permissionsignal.connect(_on_permissionsignal_recieved)
		#plugin.getFcmToken()
	else:
		printerr("No plugin Found")
	#pass
	_client.connect("connected", Callable(self, "_handle_client_connected"))
	_client.connect("disconnected", Callable(self, "_handle_client_disconnected"))
	_client.connect("error", Callable(self, "_handle_client_error"))
	_client.connect("data", Callable(self, "_handle_client_data"))

	#Comment 2 lines below for testing
	#if not Global.server:
		#add_client()
	

func add_client():
	add_child(_client)
	_client.connect_to_host(HOST, PORT)

func _on_signal_recieved(data):
	print(data)
	Global.fcmid = data
	#send_fcmid()
	
	
func _on_permissionsignal_recieved(data):
	print("Permission signal received:", data)
	if data == "Allowed":
		is_permission_granted = true
		#permission_granted.emit()


func _handle_client_connected() -> void:
	print("Client connected to server.")
	client_connected.emit()
	send_id()
	
func _handle_client_data(param: Dictionary) -> void:
	match param["event"]:
		"cid":
			print(param)
			Global.global_player_id = param["id"]
			Global.global_player_name = param["name"]
			Global.global_player_ax = param["ax"]
			send_fcmid()
			# --- NEW: Skin Parsing Logic ---
			var skin_string = param["skin"]
			var parsed_skins: Array = []

			# Loop through the string, stepping 2 characters at a time.
			for i in range(0, skin_string.length(), 2):
				# Get a 2-character chunk (e.g., "01", "05")
				var chunk = skin_string.substr(i, 2)
				# Convert the chunk to an integer and add it to our new array.
				parsed_skins.append(chunk.to_int())

			# Store the final parsed array in the Global singleton.
			Global.skin_parameters = parsed_skins
			print("Parsed Skin Parameters: ", Global.skin_parameters) # For debugging
			# -----------------------------
			
			enter_menu.emit()
		"gud":
			print(param)
			Global.player_coin = str(param["coin"]).pad_decimals(0)
			Global.player_cup = str(param["cup"]).pad_decimals(0)
			var skin_string = param["skin"]
			var parsed_skins: Array = []

			# Loop through the string, stepping 2 characters at a time.
			for i in range(0, skin_string.length(), 2):
				# Get a 2-character chunk (e.g., "01", "05")
				var chunk = skin_string.substr(i, 2)
				# Convert the chunk to an integer and add it to our new array.
				parsed_skins.append(chunk.to_int())

			# Store the final parsed array in the Global singleton.
			Global.skin_parameters = parsed_skins
		"gudp":
			print(param)
			Global.global_player_name = param["name"]
			Global.player_win = str(param["win"]).pad_decimals(0)
			Global.player_age = str(param["age"]).pad_decimals(0)
			Global.player_kol = str(param["kol"]).pad_decimals(0)
			Global.player_bio = param["bio"]
			Global.player_skin = str(param["skin"]).pad_decimals(0)
			Global.player_coin = str(param["coin"]).pad_decimals(0)
			# Use call_deferred to ensure scene change happens on the main thread after current frame
			#call_deferred("_change_to_profile_scene")
			update_profile.emit()
			
		#PROFILE REQUESTS
		"changenamereq":
			print(param)
			Global.can_change_name = true
			Global.change_name_cost = param["coin"]
			show_change_name_panel.emit()
		"changebioreq":
			print(param)
			Global.can_change_bio = true
			Global.change_bio_cost = param["coin"]
			show_change_bio_panel.emit()
		"changepicreq":
			print(param)
			Global.can_change_pic = true
			Global.change_pic_cost = param["coin"]
			show_change_pic_panel.emit()
			
		#PROFILE REQUESTS
		"gbucoin":
			print(param)
			gbucoin_received.emit(param)
		"gbuwcup":
			print(param)
			gbuwcup_received.emit(param)
		"gbuscup":
			print(param)
			gbuscup_received.emit(param)
		"gbucup":
			print(param)
			gbucup_received.emit(param)
			
		"frget":
			print(param)
			frget_received.emit(param)
		
		"frreqget":
			print(param)
			frreqget_received.emit(param)
			
		"fraccept":
			print(param)
			fraccept_received.emit(param)
			
		"frsearch":
			print(param)
			frsearch_received.emit(param)
			
		"frsendreq":
			print(param)
			frsendreq_received.emit(param)
			
		"chatroomlogin":
			print(param)
			last_chatroom_data = param
			call_deferred("_change_to_chat_scene")
			
		"chatroommsg":
			print(param)
			chatroommsg_recieved.emit(param)
			
		"frdelete":
			print(param)
			frget_received.emit(param)
			
		"frchat":
			print(param)

			var database = SQLite.new()
			database.path = "user://chat.db"
			database.open_db()
			
			var is_me = str(Global.global_player_id) == str(param["id"])
			var db_data = {
				"name": param["name"],
				"message": param["msg"],
				"hid": param["hid"],
				"ax": param["ax"],
				"read": str(get_tree().current_scene.name) == "ChatPrivate",
				"date": param["time"],
				"me": is_me
			}
			database.insert_row("chats", db_data)
			#print("💾 New message saved to DB:", db_data)
			#ChatPrivate:<Node2D#110092093259>
			print("CURRENT SCENE IS : " + str(get_tree().current_scene.name))
			frchat_received.emit(param)

			
		"gudph":
			print(param)
			Global.global_player_name = param["name"]
			Global.player_win = str(param["win"]).pad_decimals(0)
			Global.player_age = str(param["age"]).pad_decimals(0)
			Global.player_kol = str(param["kol"]).pad_decimals(0)
			Global.player_bio = param["bio"]
			#Global.player_skin = str(param["skin"]).pad_decimals(0)
			var skin_string = param["skin"]
			var parsed_skins: Array = []

			# Loop through the string, stepping 2 characters at a time.
			for i in range(0, skin_string.length(), 2):
				# Get a 2-character chunk (e.g., "01", "05")
				var chunk = skin_string.substr(i, 2)
				# Convert the chunk to an integer and add it to our new array.
				parsed_skins.append(chunk.to_int())

			# Store the final parsed array in the Global singleton.
			Global.h_skin_parameters = parsed_skins
			print("Parsed Skin Parameters: ", Global.h_skin_parameters) # For debugging
			
			Global.player_coin = str(param["coin"]).pad_decimals(0)
			if param["online"] == 1.0:
				Global.friend_last_seen = "Online"
			else:
				Global.friend_last_seen = "last seen " + param["last_seen"]
			# Use call_deferred to ensure scene change happens safely
			call_deferred("_change_to_friend_profile_scene", param)
			gudph_received.emit(param)
			
		"buyskin":
			print(param)
			var status = param["status"]
			buyskin.emit(status)
			
		"gshopd":
			print(param)
			shop_data = param
			call_deferred("_change_to_shop_scene")
			#gshopd_recieved.emit()
			
		"gnsearch":
			print(param)
			gnsearch_recieved.emit(param)
			
		"gamestart":
			print(param)
			Global.port = param["port"]
			Global.game_id = param["game_id"]
			gamestart_recieved.emit(param)
			
		"gamefinish":
			print(param)
			call_deferred("_change_to_game_over_scene")
			
		"leavegame":
			print(param)
			
			
func send_skin_data(skin_param:String, coin:int):
	var df = {"id" : Global.global_player_id, "andid": Global.andid , "skin": skin_param, "coin": coin}
	_client.send_server("buyskin",df)

func send_leavegame(loser_id:int):
	var df = {"id" : loser_id, "andid": Global.andid}
	_client.send_server("leavegame", df)

func send_gamefinish(winner_id:int):
	var df = {"id" : Global.global_player_id, "andid": Global.andid , "game_id": Global.game_id, "winner_id": winner_id}
	_client.send_server("gamefinish", df)

func send_id():
	Global.andid = OS.get_unique_id()
	var df = {"andid": Global.andid , "fcmid": Global.fcmid }
	_client.send_server("cid", df)
	
func send_fcmid():
	var df = {"id" : Global.global_player_id, "andid": Global.andid , "fcmid": Global.fcmid }
	print("FCM SENT")
	_client.send_server("sfcmid", df)
	

# PROFILE REQUESTS #
func send_request(req:String):
	_client.send_server(req,{"id" : Global.global_player_id , "andid":Global.andid})
	
func change_name_request(user_name: String):
	_client.send_server("changename",{"id" : Global.global_player_id , "andid":Global.andid , "name": user_name })
	
func change_bio_request(bio: String):
	_client.send_server("changebio",{"id" : Global.global_player_id , "andid":Global.andid , "bio": bio })

func send_pic(img: PackedByteArray):
	_client.send_server("uploadpic",{"id" : Global.global_player_id , "andid":Global.andid , "pic": img })
	
func send_pic_name(pic: String):
	_client.send_server("changepic",{"id" : Global.global_player_id , "andid":Global.andid , "picname": pic })
	
# FRIENDS REQUESTS #
func send_fraceept(hid:String , status:int):
	var df = {"id": Global.global_player_id,"hid" : hid , "status": status}
	_client.send_server("fraccept", df)
	
func send_frsearch(player_name: String):
	var df = {"id": Global.global_player_id, "name" : player_name}
	_client.send_server("frsearch", df)
	
func send_frsendreq(hid: String):
	var df = {"id": Global.global_player_id, "hid" : hid}
	_client.send_server("frsendreq", df)
	
func send_frdelete(hid: String):
	var df = {"id": Global.global_player_id, "hid" : hid}
	_client.send_server("frdelete", df)
	
func send_message(message:String):
	var df = {"id": Global.global_player_id, "hid" : Global.hid , "msg":message}
	_client.send_server("frchat", df)
	
func send_gudph(hid:int):
	var df = {"id": Global.global_player_id, "hid" : hid}
	_client.send_server("gudph", df)

# Deferred function to safely change to profile scene
func _change_to_profile_scene():
	# Ensure we're on the main thread and the scene tree is ready
	if not get_tree():
		push_error("Scene tree not available for profile scene change")
		return

	# Wait one frame to ensure current processing is complete
	await get_tree().process_frame
	get_tree().change_scene_to_file("res://scenes/UI/Profile/Profile.tscn")

# Deferred function to safely change to friend profile scene
func _change_to_friend_profile_scene(_param: Dictionary):
	# Ensure we're on the main thread and the scene tree is ready
	if not get_tree():
		push_error("Scene tree not available for friend profile scene change")
		return

	# Wait one frame to ensure current processing is complete
	await get_tree().process_frame
	get_tree().change_scene_to_file("res://scenes/UI/Profile/friend_profile.tscn")

# Deferred function to safely change to chat scene
func _change_to_chat_scene():
	# Ensure we're on the main thread and the scene tree is ready
	if not get_tree():
		push_error("Scene tree not available for chat scene change")
		return

	# Wait one frame to ensure current processing is complete
	await get_tree().process_frame
	get_tree().change_scene_to_file("res://scenes/UI/Chat/chat.tscn")

# Deferred function to safely change to game over scene
func _change_to_game_over_scene():
	# Ensure we're on the main thread and the scene tree is ready
	if not get_tree():
		push_error("Scene tree not available for game over scene change")
		return

	# Wait one frame to ensure current processing is complete
	await get_tree().process_frame
	get_tree().change_scene_to_file("res://game_over.tscn")

# Deferred function to safely change to shop scene
func _change_to_shop_scene():
	# Ensure we're on the main thread and the scene tree is ready
	if not get_tree():
		push_error("Scene tree not available for shop scene change")
		return

	# Wait one frame to ensure current processing is complete
	await get_tree().process_frame
	get_tree().change_scene_to_file("res://scenes/UI/Shop/shop.tscn")



# CHATS REQUESTS #
func send_chatroomlogin(roomid: int):
	var df = {"id": Global.global_player_id, "roomid" : roomid}
	_client.send_server("chatroomlogin", df)
	
# GAME NEW REQUEST #
func send_gnsearch(game:int):
	var df = {"id": Global.global_player_id, "game" : 1}
	_client.send_server("gnsearch", df)



func _handle_client_disconnected() -> void:
	print("Client disconnected from server.")
	_connect_after_timeout(RECONNECT_TIMEOUT) # Try to reconnect after 3 seconds

func _handle_client_error() -> void:
	print("Client error.")
	_connect_after_timeout(RECONNECT_TIMEOUT) # Try to reconnect after 3 seconds

func _connect_after_timeout(timeout: float) -> void:
	await get_tree().create_timer(timeout).timeout
	
	# Disconnect before reconnecting
	_client.disconnect_from_host()
	
	# Check if the client is already connected or connecting
	if _client._status != _client._stream.STATUS_CONNECTED and _client._status != _client._stream.STATUS_CONNECTING:
		_client.connect_to_host(HOST, PORT)
	else:
		print("Client is already connected or connecting. Skipping reconnection.")
