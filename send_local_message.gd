extends <PERSON><PERSON><PERSON><PERSON><PERSON>

@onready var panel: Panel = $Panel
@onready var textfield: LineEdit = $Panel/textfield

signal send_message(message:String)

var reference_resolution: Vector2 = Vector2(1920, 864)
var min_scale: float = 0.3

func _ready() -> void:
	get_viewport().connect("size_changed", Callable(self, "_on_viewport_resized"))
	_on_viewport_resized()
	_animate_panel()

func _animate_panel() -> void:
	panel.modulate.a = 0.0
	panel.position.y = get_viewport().size.y
	var target_position = Vector2(panel.position.x, panel.position.y * 0.05)

	var tween = create_tween()
	tween.tween_property(panel, "position", target_position, 0.25).set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_IN_OUT)
	tween.parallel().tween_property(panel, "modulate:a", 1.0, 0.3).set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_IN_OUT)




# Called when the viewport size changes
func _on_viewport_resized() -> void:
	# Get the current viewport size
	var viewport_size = get_viewport().get_visible_rect().size
	var scale_x = viewport_size.x / reference_resolution.x
	var scale_y = viewport_size.y / reference_resolution.y
	var scale_factor = min(scale_x, scale_y)
	scale_factor = max(scale_factor, min_scale)
	scale = Vector2(scale_factor, scale_factor)


func _on_close_pressed() -> void:
	queue_free()


func _on_send_pressed() -> void:
	var message := textfield.text
	emit_signal("send_message",message)
	textfield.clear()
