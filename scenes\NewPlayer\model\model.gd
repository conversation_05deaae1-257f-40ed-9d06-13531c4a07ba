extends CharacterBody3D


@onready var _skeleton: Node3D = %Skeleton_01
@onready var animation_player: AnimationPlayer = %AnimationPlayer

# Variables for input tracking
@export var rotation_sensitivity: float = 0.01
var previous_touch_position: Vector2
var is_dragging: bool = false
var rotation_velocity: float = 0.0  
var drag_decay_rate: float = 0.8  
var lod_bias_for_character := 10.0
@onready var animation_tree: AnimationTree = $AnimationTree
@onready var state_machine : AnimationNodeStateMachinePlayback = animation_tree.get("parameters/StateMachine/playback")

func _ready() -> void:
	var profile_node = get_parent().get_parent()
	_apply_lod_bias_recursively(self)
	if is_instance_valid(profile_node) and profile_node.has_signal("set_model_skin"):
		profile_node.set_model_skin.connect(_on_set_model_skin)
		
func play_victory_anim():
	state_machine.travel("Victory")
	
func play_lose_anim():
	state_machine.travel("Lose")
	
func go_idle():
	state_machine.travel("idle")




func _apply_lod_bias_recursively(node: Node):
	# Check if the current node is a mesh.
	if node is MeshInstance3D:
		# If it is, set its LOD bias.
		node.lod_bias = lod_bias_for_character
		#print("Applied LOD bias to: ", node.name)
	# Go through all children of the current node and run this function on them.
	for child in node.get_children():
		_apply_lod_bias_recursively(child)

# This function is now complete. It's called when the signal is emitted.
func _on_set_model_skin() ->void:
	%VFX_PowerUp.play_anim()


func _input(event: InputEvent) -> void:
	# Handle touch input (for mobile)
	if event is InputEventScreenTouch:
		if event.pressed:
			# Check if the touch is roughly on the NPC model
			if is_touch_on_model(event.position):
				is_dragging = true
				previous_touch_position = event.position
		else:
			# Stop dragging when the touch is released
			is_dragging = false

	# Handle touch drag (for mobile)
	if event is InputEventScreenDrag and is_dragging:
		# Calculate the difference in position
		var delta = event.position - previous_touch_position
		previous_touch_position = event.position

		# Update rotation velocity based on the drag delta
		rotation_velocity = delta.x * rotation_sensitivity

func _physics_process(delta: float) -> void:
	if is_dragging:
		# Rotate the NPC based on the rotation velocity
		rotate_y(rotation_velocity)
	else:
		# Gradually slow down the rotation when not dragging
		rotation_velocity *= drag_decay_rate
		rotate_y(rotation_velocity)

		# Stop rotation if the velocity is very small
		if abs(rotation_velocity) < 0.001:
			rotation_velocity = 0.0

# Function to check if the touch is roughly on the NPC model
func is_touch_on_model(touch_position: Vector2) -> bool:
	# Get the NPC's position on the screen
	var camera = get_viewport().get_camera_3d()
	if camera:
		var npc_screen_position = camera.unproject_position(global_transform.origin)
		# Define a rough area around the NPC for touch detection
		var touch_radius: float = 400.0
		return touch_position.distance_to(npc_screen_position) < touch_radius
	return false
