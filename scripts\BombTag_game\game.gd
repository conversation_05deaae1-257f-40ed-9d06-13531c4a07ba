# Game.gd
# PURPOSE: To manage all the rules, timers, and state of the bomb tag game.

extends Node

# --- Node References ---
@onready var player_count: Label3D = %PlayerCount
@onready var bomb_timer_label: Label = %BombTimerLabel
@onready var players: Node3D = %Players
@onready var explosion_effect: Node3D = %Explosion
@onready var lobby_timer_label: Label = %LobbyTimerLabel
@onready var powerup_spwanpoints: Node3D = %Powerup_Spwanpoints


# --- Game State ---
var current_bomb_holder: Player = null
var explosion_countdown := 0
var bomb_explosion_timer: Timer
const GAME_START_PLAYERS := 3
var is_bomb_game_running := false
var lobby_timer : Timer
var hot_potato_timer : Timer
var lobby_countdown := 0
var hot_potato_countdown := 120  
var debug_timer_counter := 0  
@onready var players_left_container: GridContainer = %Players_left_container


signal start_bomb_game
var player_left_card = preload("res://scenes/Utils/players_left.tscn")
var POWER_UP_SPEED = preload("res://power_up_speed.tscn")
var POWER_UP_TIME = preload("res://power_up_time.tscn")
var p : Array = []
@onready var hot_potato_timer_label: Label = %hot_potato_timer_label
var win_sound = preload("res://assets/SFX/WIN.wav")
var lose_sound = preload("res://assets/SFX/LOSE.wav")
var winner_id := 0

# Dictionary to store mapping of multiplayer_id -> tcp_id
var player_tcp_ids: Dictionary = {}

func _ready():
	#Tcpserver.connect("gameover", Callable(self, "_on_game_over"))
	#pass
	#TEST
	start_bomb_game.connect(_on_start_bomb_game)
	if Global.server:
		MultiplayerController.start_server()
	else:
		MultiplayerController.start_client()
	
	# Server is responsible for creating and managing game timers
	if multiplayer.is_server():
		bomb_explosion_timer = Timer.new()
		bomb_explosion_timer.timeout.connect(_on_bomb_timer_timeout)
		bomb_explosion_timer.wait_time = 1.0
		add_child(bomb_explosion_timer)
		

		lobby_timer = Timer.new()
		lobby_timer.timeout.connect(_on_lobby_timer_timeout)
		lobby_timer.wait_time = 1.0
		add_child(lobby_timer)

		hot_potato_timer = Timer.new()
		hot_potato_timer.timeout.connect(_on_hot_potato_timer_timeout)
		hot_potato_timer.wait_time = 1.0
		add_child(hot_potato_timer)

func _on_hot_potato_timer_timeout():
	if not multiplayer.is_server(): return
	
	hot_potato_countdown -= 1
	debug_timer_counter += 1
	
	# Update the timer display for all clients
	update_hot_potato_display.rpc(hot_potato_countdown)
	
	# Debug output every 10 seconds
	if debug_timer_counter >= 10:
		print("Hot Potato Timer: %d seconds remaining" % hot_potato_countdown)
		debug_timer_counter = 0
	
	if hot_potato_countdown <= 0:
		end_hot_potato_game()

func end_hot_potato_game():
	if not multiplayer.is_server(): return
	
	print("HOT POTATO GAME ENDED!")
	is_bomb_game_running = false
	hot_potato_timer.stop()
	bomb_explosion_timer.stop()
	
	# Collect all players and their explosion counts
	var player_results = []
	var min_explosions = 99
	var winner_name = ""
	
	for player in players.get_children():
		var explosions = player.explosion_count
		player_results.append({"name": player.name, "explosions": explosions})

		if explosions < min_explosions:
			min_explosions = explosions
			winner_name = player.name
			# Get TCP ID from the mapping, fallback to multiplayer ID if not found
			var multiplayer_id = player.get_multiplayer_authority()
			winner_id = player_tcp_ids.get(multiplayer_id, multiplayer_id)
	
	# Print final results
	print("=== FINAL RESULTS ===")
	for result in player_results:
		print("Player %s: %d explosions" % [result.name, result.explosions])
	print("WINNER: %s with %d explosions!" % [winner_name, min_explosions])
	print("WINNER ID IS : " + str(winner_id))
	print("====================")
	
	# Sync results to all clients
	display_game_results.rpc(player_results, winner_name,winner_id)
	#Global.winner_id = winner_id
	#Tcpserver.send_gamefinish(winner_id)

	# Close the dedicated server after a short delay
	print("Shutting down dedicated server...")
	await get_tree().create_timer(5.0).timeout

	# Clean up server resources before shutdown
	if OS.has_feature("dedicated_server"):
		print("Cleaning up server resources...")

		# Stop all timers
		if hot_potato_timer:
			hot_potato_timer.stop()
			hot_potato_timer.queue_free()
		if bomb_explosion_timer:
			bomb_explosion_timer.stop()
			bomb_explosion_timer.queue_free()

		# Clear game state
		is_bomb_game_running = false
		current_bomb_holder = null
		player_tcp_ids.clear()

		# Remove all player nodes
		if players:
			for player in players.get_children():
				player.queue_free()

		# Disconnect all peers and clean up multiplayer
		if multiplayer.multiplayer_peer:
			multiplayer.multiplayer_peer.close()
			multiplayer.multiplayer_peer = null

		# Force garbage collection
		await get_tree().process_frame

		print("Server cleanup complete. Shutting down...")
		get_tree().quit()

@rpc("any_peer", "call_local")
func display_game_results(results: Array, winner: String, winner_tcp_id: int):
	print("Game Over! Winner: %s (TCP ID: %d)" % [winner, winner_tcp_id])

	# Set the global winner ID for the game_over screen
	Global.winner_id = winner_tcp_id

	## Only clients should send the game finish message, not the dedicated server
	#if not OS.has_feature("dedicated_server") and not Global.server:
		#Tcpserver.send_gamefinish(winner_tcp_id)
		##print("WINNER ID ON CLIENT IS : " + str(winner_tcp_id))

	lobby_timer_label.text = "Winner: " + winner
	lobby_timer_label.show()

	# Check if the current player running this code is the winner by comparing TCP IDs
	if Global.global_player_id == winner_tcp_id:
		%game_over.stream = win_sound
	else:
		%game_over.stream = lose_sound

	# Now play whichever sound was just assigned to the stream
	%game_over.play()

	# Clean up multiplayer session after a short delay
	await get_tree().create_timer(5.0).timeout
	# Only clients should send the game finish message, not the dedicated server
	if not OS.has_feature("dedicated_server") and not Global.server:
		Tcpserver.send_gamefinish(winner_tcp_id)
		#print("WINNER ID ON CLIENT IS : " + str(winner_tcp_id))
	cleanup_multiplayer_session()

# Clean up multiplayer session before scene change
func cleanup_multiplayer_session():
	print("Cleaning up multiplayer session...")

	# Stop all timers to prevent further RPC calls
	if hot_potato_timer:
		hot_potato_timer.stop()
	if bomb_explosion_timer:
		bomb_explosion_timer.stop()

	# Clear the game state
	is_bomb_game_running = false
	current_bomb_holder = null

	# Disconnect from multiplayer if we're a client (check if peer exists first)
	if not OS.has_feature("dedicated_server") and multiplayer.multiplayer_peer != null:
		print("Client disconnecting from multiplayer...")
		multiplayer.multiplayer_peer = null

	print("Multiplayer cleanup complete")

@rpc("any_peer","call_local")
func peer_connected(id: int):
	print("Game Logic: Acknowledging new player: " + str(id))
	set_peer_skin(id)
	sync_world_objects(id)

	# Wait a bit for the player to be fully spawned and initialized
	await get_tree().create_timer(0.5).timeout

	# Request TCP ID from the newly connected player
	if multiplayer.is_server():
		request_tcp_id_from_player.rpc_id(id, id)

	add_players_left()
	if not is_bomb_game_running:
		reset_lobby_timer()

# This is called by the controller when a player leaves.
@rpc("any_peer","call_local")
func peer_disconnected(id: int):
	print("Game Logic: Acknowledging player left: " + str(id))
	var player_node = players.get_node_or_null(str(id))
	if player_node:
		player_node.queue_free() # Make sure the player is removed from the scene

	# Remove TCP ID mapping for disconnected player
	if player_tcp_ids.has(id):
		player_tcp_ids.erase(id)
		print("Removed TCP ID mapping for disconnected player: " + str(id))

	if Global.player_count > 0:
		Global.player_count -= 1
		player_count.text = "players : " + str(Global.player_count)
	
	await get_tree().process_frame
	add_players_left()
	
	# Optional: Add logic to end the game if not enough players are left
	if multiplayer.is_server() and is_bomb_game_running and players.get_child_count() <= 1:
		print("GAME OVER: Not enough players to continue.")
		is_bomb_game_running = false
		bomb_explosion_timer.stop()
		# You could reset the game or return to a lobby here.

func add_players_left() -> void:
	for card in players_left_container.get_children():
		card.queue_free()
	var player_nodes = get_node("%Players").get_children()

	for player in player_nodes:
		var card = player_left_card.instantiate()
		players_left_container.add_child(card)

		# Get TCP ID from the mapping, fallback to multiplayer ID if not found
		var multiplayer_id = player.get_multiplayer_authority()
		var tcp_id = player_tcp_ids.get(multiplayer_id, multiplayer_id)

		card.user_name.text = str(tcp_id)

		#card.player_id = Global.global_player_id
		#card.profile.texture = Global.player_profile.texture

		#var image_path = "res://assets/profiles/" + profile_picture_name
		#if profile_picture_name != "0" and ResourceLoader.exists(image_path):
			#card.profile.texture = load(image_path)
		#else:
			#card.profile.texture = load("res://assets/images/profile_placeHolder.png")

# Function to store TCP ID mapping
func store_player_tcp_id(multiplayer_id: int, tcp_id: int):
	# Only update if the mapping is new or different
	if not player_tcp_ids.has(multiplayer_id) or player_tcp_ids[multiplayer_id] != tcp_id:
		player_tcp_ids[multiplayer_id] = tcp_id
		print("Stored TCP ID mapping: multiplayer_id %d -> tcp_id %d" % [multiplayer_id, tcp_id])
		# Refresh the players left display when a new mapping is added
		await get_tree().process_frame  # Wait a frame to ensure all players are spawned
		add_players_left()

# Request TCP ID from a specific player
@rpc("any_peer", "call_local")
func request_tcp_id_from_player(target_multiplayer_id: int):
	# This runs on the target player's client
	if multiplayer.get_unique_id() == target_multiplayer_id:
		# Send back our TCP ID to the server
		send_tcp_id_to_server.rpc_id(1, target_multiplayer_id, Global.global_player_id)

# Receive TCP ID from a player and broadcast it to all clients
@rpc("any_peer", "call_local")
func send_tcp_id_to_server(multiplayer_id: int, tcp_id: int):
	# This runs on the server, then broadcast to all clients
	if multiplayer.is_server():
		broadcast_tcp_id.rpc(multiplayer_id, tcp_id)

# Broadcast TCP ID mapping to all clients
@rpc("any_peer", "call_local")
func broadcast_tcp_id(multiplayer_id: int, tcp_id: int):
	store_player_tcp_id(multiplayer_id, tcp_id)

func set_peer_skin(id : int):
		# This part only runs on the server to sync late-joiners.
	if multiplayer.is_server():
		# Iterate through all players already in the game.
		for player in players.get_children():
			# Get the existing player's unique ID.
			var existing_player_id = player.get_multiplayer_authority()
			# We don't need to tell the new player about themself.
			# They will handle their own skin setup in their _ready() function.
			if existing_player_id == id:
				continue
			# For every OTHER player, send their skin data via an RPC,
			# but target it ONLY to the newly connected player (id).
			player.apply_skin_on_all_clients.rpc_id(id, player.skin_parameters)
			print("Server syncing existing player %s for new player %s" % [existing_player_id, id])


func sync_world_objects(id):
	Global.player_count += 1
	player_count.text = "players : " + str(Global.player_count)
	#if multiplayer.is_server():
		#var wheel = $wheel
		#var time = wheel.get_node("AnimationPlayer").current_animation_position
		#wheel.sync_anim_time.rpc_id(id, time)
		
# Change this function to accept the position from the server's RPC
# In game.gd

# Change this function to accept the position AND the index from the server's RPC
@rpc("any_peer","call_local")
func spawn_power_up(spawn_position: Vector3, power_up_index: int):
	var power_up_options = [POWER_UP_SPEED, POWER_UP_TIME]
	var chosen_power_up = power_up_options[power_up_index]
	var power_up = chosen_power_up.instantiate()
	power_up.position = spawn_position
	add_child(power_up)
	print("Power-up spawned at synced position: ", power_up.position)

#region Lobby Timer Functions
@rpc("any_peer", "call_local")
func _set_lobby_timer_all_clients(countdown: int):
	lobby_timer_label.text = str(countdown)
	lobby_timer_label.show()
	if lobby_timer_label.text == "-1": lobby_timer_label.hide()

func reset_lobby_timer():
	if not multiplayer.is_server(): return
	if Global.player_count == int(Global.number_of_players):
		lobby_countdown = 5 # Start game soon!
	else:
		lobby_countdown = 10 # Waiting for more players
	rpc("_set_lobby_timer_all_clients", lobby_countdown)
	lobby_timer_label.show()
	lobby_timer.start()
	
func _on_lobby_timer_timeout():
	if not multiplayer.is_server(): return
	lobby_countdown -= 1
	if lobby_countdown <= 3 and lobby_countdown > 0:
		pass
		%Countdown.play()
	if lobby_countdown == 0:
		pass
		%CountdownEnd.play()
	rpc("_set_lobby_timer_all_clients", lobby_countdown)
	if lobby_countdown < 0:
		if Global.player_count >= int(Global.number_of_players):
			is_bomb_game_running = true
			start_bomb_game.emit()
			lobby_timer.stop()
		else:
			# Not enough players, reset the waiting timer
			lobby_timer.stop()
			reset_lobby_timer()
#endregion

#region BOMB GAME FUNCTIONS
@rpc("any_peer", "reliable")
func reassign_bomb(dead_player_id: int):
	if not multiplayer.is_server(): return
	await get_tree().process_frame
	var sp = get_node_or_null(powerup_spwanpoints.get_path())
	if sp and not sp.get_children().is_empty():
		var points = sp.get_children()
		var chosen_position = points.pick_random().global_position
		var power_up_index = randi_range(0, 1) 
		spawn_power_up.rpc(chosen_position, power_up_index)

	var dead_player = get_node_or_null(str(players.get_path()) + "/" + str(dead_player_id))
	if is_instance_valid(dead_player):
		dead_player.set_bomb_status.rpc(false)

	var living_players = []
	for p in players.get_children():
		if p.get_multiplayer_authority() != dead_player_id:
			living_players.append(p)

	if living_players.size() > 0:
		var new_bomb_holder = living_players.pick_random()
		current_bomb_holder = new_bomb_holder
		new_bomb_holder.set_bomb_status.rpc(true)
		await get_tree().create_timer(2.0).timeout
		start_bomb_timer()
	else:
		print("Game over or no players left.")
		current_bomb_holder = null

@rpc("any_peer", "call_remote", "reliable")
func request_bomb_transfer(target_player_id: int):
	if not multiplayer.is_server(): return
	var transferer_id = multiplayer.get_remote_sender_id()
	var transferer_node = players.get_node_or_null(str(transferer_id))
	var target_node = players.get_node_or_null(str(target_player_id))

	if not (is_instance_valid(transferer_node) and is_instance_valid(target_node)): return
	if transferer_node != current_bomb_holder: return

	print("Transferring bomb from %s to %s" % [transferer_node.name, target_node.name])
	current_bomb_holder = target_node
	transferer_node.set_bomb_status.rpc(false)
	target_node.set_bomb_status.rpc(true)
	print("TRANSFERRER ID IS : " +  str(transferer_node.player_id))
	print("TARGET ID IS : " +  str(target_node.player_id))
	transferer_node.toggle_bomb_area.rpc(false)
	target_node.toggle_bomb_area.rpc(false)
	await get_tree().create_timer(1.0).timeout
	transferer_node.toggle_bomb_area.rpc(true)
	target_node.toggle_bomb_area.rpc(true)

func start_bomb_timer():
	if not multiplayer.is_server(): return
	explosion_countdown = randi_range(6, 10)
	rpc("_set_bomb_timer_all_clients", explosion_countdown)
	bomb_explosion_timer.start()
	

# This allows any client to call the function, but it will only run on the server.
@rpc("any_peer", "call_remote", "reliable")
func increase_bomb_timer():
	# A security check to ensure this code only ever runs on the server instance.
	if not multiplayer.is_server(): return

	explosion_countdown += 10
	# Since this now runs on the server, it will correctly sync the new time to all clients.
	rpc("_set_bomb_timer_all_clients", explosion_countdown)

@rpc("any_peer", "call_local")
func _set_bomb_timer_all_clients(countdown: int):
	bomb_timer_label.text = str(countdown)
	
	
## Add this new function to game.gd
## This RPC will be called by the server and execute on all clients (and the server itself).
#@rpc("any_peer", "call_local")
#func update_player_card_eliminated(eliminated_player_name: String):
	## This code now runs on every player's machine
	#var player_cards = get_node("%Players_left_container").get_children()
	#for card in player_cards:
		## Check if this card belongs to the player who was eliminated
		#if card.user_name.text == eliminated_player_name:
			## Call the local function to update the visuals
			#card.remove_player()
			## We found the card, so we can stop looping
			#break

func _on_bomb_timer_timeout():
	if not multiplayer.is_server(): return
	explosion_countdown -= 1
	rpc("_set_bomb_timer_all_clients", explosion_countdown)
	if explosion_countdown <= 0:
		bomb_explosion_timer.stop()
		if is_instance_valid(current_bomb_holder):
			var holder_id = current_bomb_holder.get_multiplayer_authority()
			current_bomb_holder._on_bomb_exploded.rpc_id(holder_id)
			print("BOMB EXPLODED ON : " + current_bomb_holder.name)
			#var eliminated_name = str(current_bomb_holder.name)
			#update_player_card_eliminated.rpc(eliminated_name)



func _on_start_bomb_game():
	if not multiplayer.is_server(): return
	await get_tree().process_frame
	
	# Start the hot potato timer
	hot_potato_countdown = 15  # Reset to 5 minutes
	debug_timer_counter = 0
	
	# Initialize the timer display
	update_hot_potato_display.rpc(hot_potato_countdown)
	
	hot_potato_timer.start()
	print("Hot Potato Timer started: %s seconds" %hot_potato_countdown)
	
	var player_nodes = players.get_children()
	if player_nodes.is_empty(): return
	var chosen_player = player_nodes.pick_random()
	current_bomb_holder = chosen_player
	chosen_player.set_bomb_status.rpc(true)
	start_bomb_timer()

@rpc("any_peer", "call_local", "reliable")
func play_explosion_at(world_position: Vector3):
	explosion_effect.global_position = world_position + Vector3(0,1,0)
	explosion_effect.explode()

@rpc("any_peer", "call_local")
func update_hot_potato_display(seconds: int):
	var minutes = seconds / 60
	var remaining_seconds = seconds % 60
	hot_potato_timer_label.text = "%02d:%02d" % [minutes, remaining_seconds]
#endregion


func _on_area_3d_body_entered(body: Node3D) -> void:
	if body is Player:
		#body.player_enter_water()
		body.water.play()
		await get_tree().create_timer(0.1).timeout
		
		if body.has_bomb:
			if not multiplayer.is_server(): return
			explosion_countdown = 0
			rpc("_set_bomb_timer_all_clients", explosion_countdown)
			bomb_explosion_timer.stop()
			if is_instance_valid(current_bomb_holder):
				var holder_id = current_bomb_holder.get_multiplayer_authority()
				current_bomb_holder._on_bomb_exploded.rpc_id(holder_id)
				print("BOMB EXPLODED ON : " + current_bomb_holder.name)
				#var eliminated_name = str(current_bomb_holder.name)
				#update_player_card_eliminated.rpc(eliminated_name)
		else:
			body.respawn_at_start()
