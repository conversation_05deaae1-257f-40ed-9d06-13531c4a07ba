[gd_scene load_steps=121 format=4 uid="uid://cxc3n0k7aovh8"]

[ext_resource type="Script" uid="uid://djcl3bc1laf6s" path="res://scenes/NewPlayer/model/model.gd" id="1_g5bav"]
[ext_resource type="Script" uid="uid://cg4m828emfqp4" path="res://scenes/NewPlayer/character_skin.gd" id="2_ch4jb"]
[ext_resource type="Texture2D" uid="uid://tio01tmpd1xi" path="res://new_character/Cute_Characters_Textures_4.png" id="4_6kp8l"]
[ext_resource type="ArrayMesh" uid="uid://cam1gbmkc255q" path="res://scenes/NewPlayer/resources/hair/1.tres" id="4_g5bav"]
[ext_resource type="ArrayMesh" uid="uid://7lew1u1yh47t" path="res://scenes/NewPlayer/resources/shirts/1.tres" id="5_nkjm3"]
[ext_resource type="ArrayMesh" uid="uid://bqmvr8dromhfo" path="res://scenes/NewPlayer/resources/pants/1.tres" id="6_pxof6"]
[ext_resource type="Animation" uid="uid://utllxlltfay2" path="res://new_character/A-Pose.res" id="10_s5rxm"]
[ext_resource type="Animation" uid="uid://bmq0bes6thgkp" path="res://new_character/Attack_Kick.res" id="11_8udw1"]
[ext_resource type="Animation" uid="uid://bxeigcbxrd7br" path="res://new_character/Attack_Punch.res" id="12_hl27u"]
[ext_resource type="Animation" uid="uid://b8svpinflebvr" path="res://new_character/Block_With_Hands.res" id="13_frjm0"]
[ext_resource type="Animation" uid="uid://ctb7afq5k4u10" path="res://new_character/Climb_Ladder.res" id="14_yvxww"]
[ext_resource type="Animation" uid="uid://bs76ukaqllltx" path="res://new_character/Crouch_Walk _Forward.res" id="15_24ehl"]
[ext_resource type="Animation" uid="uid://bg52p704i7s8k" path="res://new_character/Crouch_Walk_Backward.res" id="16_kayyy"]
[ext_resource type="Animation" uid="uid://fgwi7vx1v3d4" path="res://new_character/Crouch_Walk_Left.res" id="17_wl8gc"]
[ext_resource type="Animation" uid="uid://2c10vjpt5vw7" path="res://new_character/Crouch_Walk_Right.res" id="18_ny0gp"]
[ext_resource type="Animation" uid="uid://ygfomad5ksul" path="res://new_character/Death_Backward.res" id="19_2mj4c"]
[ext_resource type="Animation" uid="uid://bk6mm0pcllgwa" path="res://new_character/Death_Forward.res" id="20_o1aa7"]
[ext_resource type="Animation" uid="uid://c4xlwlft3b1vs" path="res://new_character/Dodge_Roll.res" id="21_0nk2r"]
[ext_resource type="Animation" uid="uid://dwdv2cf1fvjvt" path="res://new_character/Dodge_Sidestep.res" id="22_imtsj"]
[ext_resource type="Animation" uid="uid://e1b1qpceeg4" path="res://new_character/Fall.res" id="23_gpoug"]
[ext_resource type="Animation" uid="uid://gj3k2yvma4tb" path="res://new_character/Hit_Reaction_Heavy.res" id="24_n2uid"]
[ext_resource type="Animation" uid="uid://x7qyvi34a158" path="res://new_character/Hit_Reaction_Light.res" id="25_w2sfv"]
[ext_resource type="Animation" uid="uid://cudg1uwc5ufri" path="res://new_character/Idle_Look_Around.res" id="26_jlwsj"]
[ext_resource type="Animation" uid="uid://dwgs0jfryki2i" path="res://new_character/Idle_Relaxed.res" id="27_vj7jt"]
[ext_resource type="Animation" uid="uid://dsx5h3tmhb1yl" path="res://new_character/Jump.res" id="28_c34mi"]
[ext_resource type="Animation" uid="uid://dajwrb0myxv4c" path="res://new_character/Jump_End.res" id="29_xdknx"]
[ext_resource type="Animation" uid="uid://7rwxqgl4nw7e" path="res://new_character/Jump_Start.res" id="30_iftjx"]
[ext_resource type="Animation" uid="uid://c30elvkijl07n" path="res://new_character/Run_Backward.res" id="31_36i3e"]
[ext_resource type="Animation" uid="uid://dg0so7o74a73k" path="res://new_character/Run_Forward.res" id="32_golr0"]
[ext_resource type="Animation" uid="uid://ckyv7mieqq5wi" path="res://new_character/Run_Left.res" id="33_qeqh6"]
[ext_resource type="Animation" uid="uid://dlcin1l3jaggy" path="res://new_character/Run_Right.res" id="34_6ompd"]
[ext_resource type="Animation" uid="uid://dnkuddtkcoc4g" path="res://new_character/Walk_Backward.res" id="35_vx3qb"]
[ext_resource type="Animation" uid="uid://crkswt0hytc4w" path="res://new_character/Walk_Forward.res" id="36_sq613"]
[ext_resource type="Animation" uid="uid://cqrqt8pvw23eo" path="res://new_character/Walk_Left.res" id="37_miudd"]
[ext_resource type="Script" uid="uid://doq3qjy351hur" path="res://vfx_power_up.gd" id="37_nkjm3"]
[ext_resource type="Animation" uid="uid://f1bhqrfv8qy4" path="res://scenes/NewPlayer/animations/Victory.res" id="37_pxof6"]
[ext_resource type="Animation" uid="uid://crfe2cxbiy8a" path="res://new_character/Walk_Right.res" id="38_4jv0p"]
[ext_resource type="Animation" uid="uid://bisle17p7ddxj" path="res://scenes/NewPlayer/animations/Lose.res" id="38_oytuk"]
[ext_resource type="Shader" uid="uid://cfjms7igvjapc" path="res://power_up_shader.tres" id="38_pxof6"]
[ext_resource type="Texture2D" uid="uid://v8r288glr1u6" path="res://POWERUP_TEXTURES/Textures/T_Aurax12.jpg" id="39_0nmdm"]
[ext_resource type="Animation" uid="uid://v6pxx2co1tj" path="res://new_character/Skeleton_01_mixamo_com_Layer0.res" id="39_2gn6e"]
[ext_resource type="Animation" uid="uid://t4l1ysp3m5n7" path="res://scenes/NewPlayer/animations/Lose2.res" id="39_ri00f"]
[ext_resource type="ArrayMesh" uid="uid://e371smbj4und" path="res://cylinder_long.obj" id="40_oytuk"]
[ext_resource type="Texture2D" uid="uid://dfdoxu0b1fdx1" path="res://POWERUP_TEXTURES/Textures/T_disort_test2.PNG" id="41_ri00f"]
[ext_resource type="Shader" uid="uid://dgwac6mam38tg" path="res://POWERUP_TEXTURES/s_simple1.tres" id="42_s5rxm"]
[ext_resource type="Texture2D" uid="uid://djtd4uc4abq6f" path="res://POWERUP_TEXTURES/Textures/T_VFX_Glo31.png" id="43_8udw1"]

[sub_resource type="Skin" id="Skin_2k135"]
resource_name = "Skin"
bind_count = 44
bind/0/name = &"Root"
bind/0/bone = -1
bind/0/pose = Transform3D(1, 8.74228e-08, 8.74228e-08, -8.74228e-08, 1, 2.38419e-07, -8.74227e-08, -1.62921e-07, 1, 0, 0, 0)
bind/1/name = &"Hips"
bind/1/bone = -1
bind/1/pose = Transform3D(1, -3.09801e-07, -1.87695e-06, -6.83609e-08, 0.980161, -0.198203, 1.90111e-06, 0.198203, 0.980161, 1.24075e-07, -0.30774, -0.06191)
bind/2/name = &"LeftUpLeg"
bind/2/bone = -1
bind/2/pose = Transform3D(-0.00782302, 0.167173, 0.985896, 4.61004e-08, -0.985927, 0.167178, 0.999969, 0.00130789, 0.00771291, -0.050597, 0.335074, -0.0699237)
bind/3/name = &"LeftLeg"
bind/3/bone = -1
bind/3/pose = Transform3D(0.0088033, -0.272034, 0.962247, -0.00948426, -0.962264, -0.271952, 0.999916, -0.00673212, -0.0110512, 0.0328069, 0.190194, -0.0680165)
bind/4/name = &"LeftFoot"
bind/4/bone = -1
bind/4/pose = Transform3D(-1, 5.48155e-06, 9.94759e-07, 3.56411e-08, -0.161424, 0.986885, 5.5746e-06, 0.986885, 0.161424, 0.0679638, 0.0303778, -0.0284937)
bind/5/name = &"LeftToeBase"
bind/5/bone = -1
bind/5/pose = Transform3D(1, 2.15671e-06, 5.15571e-07, -2.47531e-07, -0.149938, 0.988695, 2.20456e-06, -0.988695, -0.149938, -0.0679641, -0.049905, 0.0279147)
bind/6/name = &"Spine"
bind/6/bone = -1
bind/6/pose = Transform3D(1, 8.20861e-08, -1.50937e-07, -8.5444e-08, 0.999749, -0.0223832, 1.49062e-07, 0.0223832, 0.999749, 2.17208e-09, -0.406909, -0.00911027)
bind/7/name = &"Spine1"
bind/7/bone = -1
bind/7/pose = Transform3D(1, 8.74227e-08, 8.74225e-08, -8.02673e-08, 0.996895, -0.0787431, -9.40349e-08, 0.0787431, 0.996895, 3.90798e-14, -0.491518, -0.0368954)
bind/8/name = &"Neck"
bind/8/bone = -1
bind/8/pose = Transform3D(1, -4.75749e-08, 5.44752e-07, -1.08596e-07, 0.959087, 0.28311, -5.35933e-07, -0.28311, 0.959087, 7.97016e-08, -0.541607, 0.167146)
bind/9/name = &"Head"
bind/9/bone = -1
bind/9/pose = Transform3D(1, 8.68965e-08, 3.25841e-07, -8.76155e-08, 0.999997, 0.0022074, -3.25648e-07, -0.00220731, 0.999998, -1.9294e-09, -0.622479, -0.00809243)
bind/10/name = &"LeftShoulder"
bind/10/bone = -1
bind/10/pose = Transform3D(0.154985, -0.0162676, 0.987783, 0.976142, -0.151396, -0.155652, 0.152078, 0.988339, -0.0075846, 0.00271129, 0.0728375, -0.542446)
bind/11/name = &"LeftArm"
bind/11/bone = -1
bind/11/pose = Transform3D(0.148851, -0.0564558, 0.987247, 0.880969, -0.4459, -0.158326, 0.449151, 0.893301, -0.0166368, 0.0247572, 0.158227, -0.518332)
bind/12/name = &"LeftForeArm"
bind/12/bone = -1
bind/12/pose = Transform3D(-0.592081, -0.805862, -0.00519985, 0.795252, -0.585306, 0.158087, -0.13044, 0.0894653, 0.987412, 0.506968, 0.110984, 0.0156244)
bind/13/name = &"LeftHand"
bind/13/bone = -1
bind/13/pose = Transform3D(-0.590803, -0.806816, -2.16067e-07, 0.803435, -0.588327, 0.0914593, -0.0737911, 0.0540343, 0.995809, 0.507025, -0.00695802, 0.0127108)
bind/14/name = &"LeftHandIndex1"
bind/14/bone = -1
bind/14/pose = Transform3D(-0.00542012, 0.00542039, -0.999971, 0.63165, -0.775217, -0.0076259, -0.775235, -0.631672, 0.00077798, 0.0250735, 0.040415, 0.524214)
bind/15/name = &"LeftHandIndex2"
bind/15/bone = -1
bind/15/pose = Transform3D(0.03511, -0.037178, -0.998692, 0.466316, -0.883245, 0.049274, -0.883922, -0.467436, -0.013674, 0.0236327, 0.117721, 0.51116)
bind/16/name = &"LeftHandMiddle1"
bind/16/bone = -1
bind/16/pose = Transform3D(-0.00585239, 0.00120206, -0.999982, 0.646658, -0.762766, -0.00470155, -0.762758, -0.646674, 0.00368668, 0.00187246, 0.0324674, 0.523446)
bind/17/name = &"LeftHandMiddle2"
bind/17/bone = -1
bind/17/pose = Transform3D(-0.00870351, 0.00374581, -0.999955, 0.58175, -0.813328, -0.00811028, -0.813322, -0.581794, 0.00489968, 0.00211099, 0.053249, 0.520831)
bind/18/name = &"LeftHandPinky1"
bind/18/bone = -1
bind/18/pose = Transform3D(-0.0786782, 0.0827304, -0.993462, 0.643071, -0.757276, -0.113991, -0.761755, -0.647834, 0.00637951, -0.0430797, 0.031603, 0.518756)
bind/19/name = &"LeftHandPinky2"
bind/19/bone = -1
bind/19/pose = Transform3D(-0.0477945, 0.0602707, -0.997037, 0.592175, -0.802134, -0.0768757, -0.804391, -0.594094, 0.00264677, -0.0471262, 0.0526599, 0.515965)
bind/20/name = &"LeftHandProp"
bind/20/bone = -1
bind/20/pose = Transform3D(0.0146597, -0.01466, -0.999785, 0.749719, -0.661433, 0.0206916, -0.661594, -0.749861, 0.00129447, -0.00967823, -0.0325254, 0.488159)
bind/21/name = &"LeftHandRing1"
bind/21/bone = -1
bind/21/pose = Transform3D(-0.0334299, 0.0362908, -0.998782, 0.530428, -0.846342, -0.0485057, -0.847072, -0.531403, 0.00904346, -0.0227658, 0.106347, 0.514531)
bind/22/name = &"LeftHandRing2"
bind/22/bone = -1
bind/22/pose = Transform3D(-0.0292329, 0.0258762, -0.999238, 0.56938, -0.8212, -0.0379232, -0.821555, -0.570054, 0.00927269, -0.0207978, 0.0622496, 0.518007)
bind/23/name = &"LeftHandThumb1"
bind/23/bone = -1
bind/23/pose = Transform3D(0.849456, 0.405672, -0.337424, 0.526431, -0.695177, 0.48949, -0.035997, -0.593431, -0.80408, -0.418472, 0.0767613, 0.25556)
bind/24/name = &"LeftHandThumb2"
bind/24/bone = -1
bind/24/pose = Transform3D(0.733185, 0.473096, -0.488488, 0.639472, -0.724021, 0.258592, -0.231337, -0.50197, -0.833373, -0.394265, 0.0159969, 0.29309)
bind/25/name = &"RightShoulder"
bind/25/bone = -1
bind/25/pose = Transform3D(0.154985, 0.0162673, -0.987783, -0.976141, -0.151396, -0.155652, -0.152078, 0.988339, -0.00758476, -0.00271116, 0.0728373, -0.542446)
bind/26/name = &"RightArm"
bind/26/bone = -1
bind/26/pose = Transform3D(0.148851, 0.056456, -0.987247, -0.880969, -0.4459, -0.158326, -0.449152, 0.8933, -0.0166366, -0.0247573, 0.158227, -0.518332)
bind/27/name = &"RightForeArm"
bind/27/bone = -1
bind/27/pose = Transform3D(-0.592081, 0.805861, 0.00519994, -0.795252, -0.585306, 0.158087, 0.13044, 0.0894653, 0.987411, -0.506968, 0.110984, 0.0156244)
bind/28/name = &"RightHand"
bind/28/bone = -1
bind/28/pose = Transform3D(-0.590803, 0.806816, 2.38418e-07, -0.803434, -0.588327, 0.0914592, 0.0737909, 0.0540343, 0.995809, -0.507025, -0.00695802, 0.0127108)
bind/29/name = &"RightHandIndex1"
bind/29/bone = -1
bind/29/pose = Transform3D(-0.00542055, -0.00542033, 0.999971, -0.63165, -0.775217, -0.00762611, 0.775235, -0.631672, 0.000778234, -0.0250736, 0.0404151, 0.524214)
bind/30/name = &"RightHandIndex2"
bind/30/bone = -1
bind/30/pose = Transform3D(0.03511, 0.037178, 0.998692, -0.466316, -0.883245, 0.049274, 0.883921, -0.467436, -0.0136742, -0.0236326, 0.117721, 0.51116)
bind/31/name = &"RightHandMiddle1"
bind/31/bone = -1
bind/31/pose = Transform3D(-0.00585272, -0.00120191, 0.999982, -0.646658, -0.762766, -0.00470168, 0.762758, -0.646674, 0.00368691, -0.00187258, 0.0324674, 0.523446)
bind/32/name = &"RightHandMiddle2"
bind/32/bone = -1
bind/32/pose = Transform3D(-0.00870333, -0.00374606, 0.999955, -0.581749, -0.813328, -0.00811041, 0.813322, -0.581793, 0.00489929, -0.00211077, 0.053249, 0.520831)
bind/33/name = &"RightHandPinky1"
bind/33/bone = -1
bind/33/pose = Transform3D(-0.078678, -0.0827302, 0.993461, -0.643071, -0.757276, -0.11399, 0.761755, -0.647834, 0.00637946, 0.0430797, 0.0316031, 0.518756)
bind/34/name = &"RightHandPinky2"
bind/34/bone = -1
bind/34/pose = Transform3D(-0.0477943, -0.0602708, 0.997037, -0.592175, -0.802134, -0.0768757, 0.804391, -0.594094, 0.0026465, 0.0471264, 0.0526601, 0.515965)
bind/35/name = &"RightHandProp"
bind/35/bone = -1
bind/35/pose = Transform3D(0.0146596, 0.01466, 0.999785, -0.749719, -0.661433, 0.0206915, 0.661594, -0.749861, 0.00129446, 0.00967823, -0.0325254, 0.488159)
bind/36/name = &"RightHandRing1"
bind/36/bone = -1
bind/36/pose = Transform3D(-0.03343, -0.0362908, 0.998782, -0.530427, -0.846342, -0.0485058, 0.847071, -0.531402, 0.00904347, 0.0227658, 0.106347, 0.514531)
bind/37/name = &"RightHandRing2"
bind/37/bone = -1
bind/37/pose = Transform3D(-0.0292331, -0.0258763, 0.999238, -0.569379, -0.821199, -0.0379234, 0.821555, -0.570054, 0.00927263, 0.0207979, 0.0622497, 0.518007)
bind/38/name = &"RightHandThumb1"
bind/38/bone = -1
bind/38/pose = Transform3D(0.849456, -0.405671, 0.337424, -0.52643, -0.695177, 0.48949, 0.0359973, -0.593431, -0.80408, 0.418472, 0.0767614, 0.25556)
bind/39/name = &"RightHandThumb2"
bind/39/bone = -1
bind/39/pose = Transform3D(0.733185, -0.473095, 0.488488, -0.639471, -0.724021, 0.258592, 0.231337, -0.501969, -0.833372, 0.394265, 0.015997, 0.29309)
bind/40/name = &"RightUpLeg"
bind/40/bone = -1
bind/40/pose = Transform3D(-0.00746176, -0.167173, -0.985899, 2.12691e-07, -0.985927, 0.167178, -0.999972, 0.00124723, 0.00735678, 0.0506222, 0.335074, -0.0699054)
bind/41/name = &"RightLeg"
bind/41/bone = -1
bind/41/pose = Transform3D(0.0088034, 0.272034, -0.962247, 0.00948438, -0.962264, -0.271952, -0.999916, -0.00673222, -0.0110513, -0.0328069, 0.190194, -0.0680165)
bind/42/name = &"RightFoot"
bind/42/bone = -1
bind/42/pose = Transform3D(-1, -7.96235e-06, -1.49208e-06, -1.75075e-07, -0.161424, 0.986885, -8.09726e-06, 0.986885, 0.161424, -0.0679638, 0.0303778, -0.0284938)
bind/43/name = &"RightToeBase"
bind/43/bone = -1
bind/43/pose = Transform3D(1, -2.54398e-06, -1.973e-07, -1.74233e-07, -0.149938, 0.988695, -2.54618e-06, -0.988695, -0.149938, 0.0679641, -0.0499051, 0.0279147)

[sub_resource type="Skin" id="Skin_gsnyu"]
resource_name = "Skin"
bind_count = 44
bind/0/name = &"Root"
bind/0/bone = -1
bind/0/pose = Transform3D(1, 8.74228e-08, 8.74228e-08, -8.74228e-08, 1, 2.38419e-07, -8.74227e-08, -1.62921e-07, 1, 0, 0, 0)
bind/1/name = &"Hips"
bind/1/bone = -1
bind/1/pose = Transform3D(1, -3.09801e-07, -1.87695e-06, -6.83609e-08, 0.980161, -0.198203, 1.90111e-06, 0.198203, 0.980161, 1.24075e-07, -0.30774, -0.06191)
bind/2/name = &"LeftUpLeg"
bind/2/bone = -1
bind/2/pose = Transform3D(-0.00782302, 0.167173, 0.985896, 4.61004e-08, -0.985927, 0.167178, 0.999969, 0.00130789, 0.00771291, -0.050597, 0.335074, -0.0699237)
bind/3/name = &"LeftLeg"
bind/3/bone = -1
bind/3/pose = Transform3D(0.0088033, -0.272034, 0.962247, -0.00948426, -0.962264, -0.271952, 0.999916, -0.00673212, -0.0110512, 0.0328069, 0.190194, -0.0680165)
bind/4/name = &"LeftFoot"
bind/4/bone = -1
bind/4/pose = Transform3D(-1, 5.48155e-06, 9.94759e-07, 3.56411e-08, -0.161424, 0.986885, 5.5746e-06, 0.986885, 0.161424, 0.0679638, 0.0303778, -0.0284937)
bind/5/name = &"LeftToeBase"
bind/5/bone = -1
bind/5/pose = Transform3D(1, 2.15671e-06, 5.15571e-07, -2.47531e-07, -0.149938, 0.988695, 2.20456e-06, -0.988695, -0.149938, -0.0679641, -0.049905, 0.0279147)
bind/6/name = &"Spine"
bind/6/bone = -1
bind/6/pose = Transform3D(1, 8.20861e-08, -1.50937e-07, -8.5444e-08, 0.999749, -0.0223832, 1.49062e-07, 0.0223832, 0.999749, 2.17208e-09, -0.406909, -0.00911027)
bind/7/name = &"Spine1"
bind/7/bone = -1
bind/7/pose = Transform3D(1, 8.74227e-08, 8.74225e-08, -8.02673e-08, 0.996895, -0.0787431, -9.40349e-08, 0.0787431, 0.996895, 3.90798e-14, -0.491518, -0.0368954)
bind/8/name = &"Neck"
bind/8/bone = -1
bind/8/pose = Transform3D(1, -4.75749e-08, 5.44752e-07, -1.08596e-07, 0.959087, 0.28311, -5.35933e-07, -0.28311, 0.959087, 7.97016e-08, -0.541607, 0.167146)
bind/9/name = &"Head"
bind/9/bone = -1
bind/9/pose = Transform3D(1, 8.68965e-08, 3.25841e-07, -8.76155e-08, 0.999997, 0.0022074, -3.25648e-07, -0.00220731, 0.999998, -1.9294e-09, -0.622479, -0.00809243)
bind/10/name = &"LeftShoulder"
bind/10/bone = -1
bind/10/pose = Transform3D(0.154985, -0.0162676, 0.987783, 0.976142, -0.151396, -0.155652, 0.152078, 0.988339, -0.0075846, 0.00271129, 0.0728375, -0.542446)
bind/11/name = &"LeftArm"
bind/11/bone = -1
bind/11/pose = Transform3D(0.148851, -0.0564558, 0.987247, 0.880969, -0.4459, -0.158326, 0.449151, 0.893301, -0.0166368, 0.0247572, 0.158227, -0.518332)
bind/12/name = &"LeftForeArm"
bind/12/bone = -1
bind/12/pose = Transform3D(-0.592081, -0.805862, -0.00519985, 0.795252, -0.585306, 0.158087, -0.13044, 0.0894653, 0.987412, 0.506968, 0.110984, 0.0156244)
bind/13/name = &"LeftHand"
bind/13/bone = -1
bind/13/pose = Transform3D(-0.590803, -0.806816, -2.16067e-07, 0.803435, -0.588327, 0.0914593, -0.0737911, 0.0540343, 0.995809, 0.507025, -0.00695802, 0.0127108)
bind/14/name = &"LeftHandIndex1"
bind/14/bone = -1
bind/14/pose = Transform3D(-0.00542012, 0.00542039, -0.999971, 0.63165, -0.775217, -0.0076259, -0.775235, -0.631672, 0.00077798, 0.0250735, 0.040415, 0.524214)
bind/15/name = &"LeftHandIndex2"
bind/15/bone = -1
bind/15/pose = Transform3D(0.03511, -0.037178, -0.998692, 0.466316, -0.883245, 0.049274, -0.883922, -0.467436, -0.013674, 0.0236327, 0.117721, 0.51116)
bind/16/name = &"LeftHandMiddle1"
bind/16/bone = -1
bind/16/pose = Transform3D(-0.00585239, 0.00120206, -0.999982, 0.646658, -0.762766, -0.00470155, -0.762758, -0.646674, 0.00368668, 0.00187246, 0.0324674, 0.523446)
bind/17/name = &"LeftHandMiddle2"
bind/17/bone = -1
bind/17/pose = Transform3D(-0.00870351, 0.00374581, -0.999955, 0.58175, -0.813328, -0.00811028, -0.813322, -0.581794, 0.00489968, 0.00211099, 0.053249, 0.520831)
bind/18/name = &"LeftHandPinky1"
bind/18/bone = -1
bind/18/pose = Transform3D(-0.0786782, 0.0827304, -0.993462, 0.643071, -0.757276, -0.113991, -0.761755, -0.647834, 0.00637951, -0.0430797, 0.031603, 0.518756)
bind/19/name = &"LeftHandPinky2"
bind/19/bone = -1
bind/19/pose = Transform3D(-0.0477945, 0.0602707, -0.997037, 0.592175, -0.802134, -0.0768757, -0.804391, -0.594094, 0.00264677, -0.0471262, 0.0526599, 0.515965)
bind/20/name = &"LeftHandProp"
bind/20/bone = -1
bind/20/pose = Transform3D(0.0146597, -0.01466, -0.999785, 0.749719, -0.661433, 0.0206916, -0.661594, -0.749861, 0.00129447, -0.00967823, -0.0325254, 0.488159)
bind/21/name = &"LeftHandRing1"
bind/21/bone = -1
bind/21/pose = Transform3D(-0.0334299, 0.0362908, -0.998782, 0.530428, -0.846342, -0.0485057, -0.847072, -0.531403, 0.00904346, -0.0227658, 0.106347, 0.514531)
bind/22/name = &"LeftHandRing2"
bind/22/bone = -1
bind/22/pose = Transform3D(-0.0292329, 0.0258762, -0.999238, 0.56938, -0.8212, -0.0379232, -0.821555, -0.570054, 0.00927269, -0.0207978, 0.0622496, 0.518007)
bind/23/name = &"LeftHandThumb1"
bind/23/bone = -1
bind/23/pose = Transform3D(0.849456, 0.405672, -0.337424, 0.526431, -0.695177, 0.48949, -0.035997, -0.593431, -0.80408, -0.418472, 0.0767613, 0.25556)
bind/24/name = &"LeftHandThumb2"
bind/24/bone = -1
bind/24/pose = Transform3D(0.733185, 0.473096, -0.488488, 0.639472, -0.724021, 0.258592, -0.231337, -0.50197, -0.833373, -0.394265, 0.0159969, 0.29309)
bind/25/name = &"RightShoulder"
bind/25/bone = -1
bind/25/pose = Transform3D(0.154985, 0.0162673, -0.987783, -0.976141, -0.151396, -0.155652, -0.152078, 0.988339, -0.00758476, -0.00271116, 0.0728373, -0.542446)
bind/26/name = &"RightArm"
bind/26/bone = -1
bind/26/pose = Transform3D(0.148851, 0.056456, -0.987247, -0.880969, -0.4459, -0.158326, -0.449152, 0.8933, -0.0166366, -0.0247573, 0.158227, -0.518332)
bind/27/name = &"RightForeArm"
bind/27/bone = -1
bind/27/pose = Transform3D(-0.592081, 0.805861, 0.00519994, -0.795252, -0.585306, 0.158087, 0.13044, 0.0894653, 0.987411, -0.506968, 0.110984, 0.0156244)
bind/28/name = &"RightHand"
bind/28/bone = -1
bind/28/pose = Transform3D(-0.590803, 0.806816, 2.38418e-07, -0.803434, -0.588327, 0.0914592, 0.0737909, 0.0540343, 0.995809, -0.507025, -0.00695802, 0.0127108)
bind/29/name = &"RightHandIndex1"
bind/29/bone = -1
bind/29/pose = Transform3D(-0.00542055, -0.00542033, 0.999971, -0.63165, -0.775217, -0.00762611, 0.775235, -0.631672, 0.000778234, -0.0250736, 0.0404151, 0.524214)
bind/30/name = &"RightHandIndex2"
bind/30/bone = -1
bind/30/pose = Transform3D(0.03511, 0.037178, 0.998692, -0.466316, -0.883245, 0.049274, 0.883921, -0.467436, -0.0136742, -0.0236326, 0.117721, 0.51116)
bind/31/name = &"RightHandMiddle1"
bind/31/bone = -1
bind/31/pose = Transform3D(-0.00585272, -0.00120191, 0.999982, -0.646658, -0.762766, -0.00470168, 0.762758, -0.646674, 0.00368691, -0.00187258, 0.0324674, 0.523446)
bind/32/name = &"RightHandMiddle2"
bind/32/bone = -1
bind/32/pose = Transform3D(-0.00870333, -0.00374606, 0.999955, -0.581749, -0.813328, -0.00811041, 0.813322, -0.581793, 0.00489929, -0.00211077, 0.053249, 0.520831)
bind/33/name = &"RightHandPinky1"
bind/33/bone = -1
bind/33/pose = Transform3D(-0.078678, -0.0827302, 0.993461, -0.643071, -0.757276, -0.11399, 0.761755, -0.647834, 0.00637946, 0.0430797, 0.0316031, 0.518756)
bind/34/name = &"RightHandPinky2"
bind/34/bone = -1
bind/34/pose = Transform3D(-0.0477943, -0.0602708, 0.997037, -0.592175, -0.802134, -0.0768757, 0.804391, -0.594094, 0.0026465, 0.0471264, 0.0526601, 0.515965)
bind/35/name = &"RightHandProp"
bind/35/bone = -1
bind/35/pose = Transform3D(0.0146596, 0.01466, 0.999785, -0.749719, -0.661433, 0.0206915, 0.661594, -0.749861, 0.00129446, 0.00967823, -0.0325254, 0.488159)
bind/36/name = &"RightHandRing1"
bind/36/bone = -1
bind/36/pose = Transform3D(-0.03343, -0.0362908, 0.998782, -0.530427, -0.846342, -0.0485058, 0.847071, -0.531402, 0.00904347, 0.0227658, 0.106347, 0.514531)
bind/37/name = &"RightHandRing2"
bind/37/bone = -1
bind/37/pose = Transform3D(-0.0292331, -0.0258763, 0.999238, -0.569379, -0.821199, -0.0379234, 0.821555, -0.570054, 0.00927263, 0.0207979, 0.0622497, 0.518007)
bind/38/name = &"RightHandThumb1"
bind/38/bone = -1
bind/38/pose = Transform3D(0.849456, -0.405671, 0.337424, -0.52643, -0.695177, 0.48949, 0.0359973, -0.593431, -0.80408, 0.418472, 0.0767614, 0.25556)
bind/39/name = &"RightHandThumb2"
bind/39/bone = -1
bind/39/pose = Transform3D(0.733185, -0.473095, 0.488488, -0.639471, -0.724021, 0.258592, 0.231337, -0.501969, -0.833372, 0.394265, 0.015997, 0.29309)
bind/40/name = &"RightUpLeg"
bind/40/bone = -1
bind/40/pose = Transform3D(-0.00746176, -0.167173, -0.985899, 2.12691e-07, -0.985927, 0.167178, -0.999972, 0.00124723, 0.00735678, 0.0506222, 0.335074, -0.0699054)
bind/41/name = &"RightLeg"
bind/41/bone = -1
bind/41/pose = Transform3D(0.0088034, 0.272034, -0.962247, 0.00948438, -0.962264, -0.271952, -0.999916, -0.00673222, -0.0110513, -0.0328069, 0.190194, -0.0680165)
bind/42/name = &"RightFoot"
bind/42/bone = -1
bind/42/pose = Transform3D(-1, -7.96235e-06, -1.49208e-06, -1.75075e-07, -0.161424, 0.986885, -8.09726e-06, 0.986885, 0.161424, -0.0679638, 0.0303778, -0.0284938)
bind/43/name = &"RightToeBase"
bind/43/bone = -1
bind/43/pose = Transform3D(1, -2.54398e-06, -1.973e-07, -1.74233e-07, -0.149938, 0.988695, -2.54618e-06, -0.988695, -0.149938, 0.0679641, -0.0499051, 0.0279147)

[sub_resource type="Skin" id="Skin_kfipw"]
resource_name = "Skin"
bind_count = 44
bind/0/name = &"Root"
bind/0/bone = -1
bind/0/pose = Transform3D(1, 8.74228e-08, 8.74228e-08, -8.74228e-08, 1, 2.38419e-07, -8.74227e-08, -1.62921e-07, 1, 0, 0, 0)
bind/1/name = &"Hips"
bind/1/bone = -1
bind/1/pose = Transform3D(1, -3.09801e-07, -1.87695e-06, -6.83609e-08, 0.980161, -0.198203, 1.90111e-06, 0.198203, 0.980161, 1.24075e-07, -0.30774, -0.06191)
bind/2/name = &"LeftUpLeg"
bind/2/bone = -1
bind/2/pose = Transform3D(-0.00782302, 0.167173, 0.985896, 4.61004e-08, -0.985927, 0.167178, 0.999969, 0.00130789, 0.00771291, -0.050597, 0.335074, -0.0699237)
bind/3/name = &"LeftLeg"
bind/3/bone = -1
bind/3/pose = Transform3D(0.0088033, -0.272034, 0.962247, -0.00948426, -0.962264, -0.271952, 0.999916, -0.00673212, -0.0110512, 0.0328069, 0.190194, -0.0680165)
bind/4/name = &"LeftFoot"
bind/4/bone = -1
bind/4/pose = Transform3D(-1, 5.48155e-06, 9.94759e-07, 3.56411e-08, -0.161424, 0.986885, 5.5746e-06, 0.986885, 0.161424, 0.0679638, 0.0303778, -0.0284937)
bind/5/name = &"LeftToeBase"
bind/5/bone = -1
bind/5/pose = Transform3D(1, 2.15671e-06, 5.15571e-07, -2.47531e-07, -0.149938, 0.988695, 2.20456e-06, -0.988695, -0.149938, -0.0679641, -0.049905, 0.0279147)
bind/6/name = &"Spine"
bind/6/bone = -1
bind/6/pose = Transform3D(1, 8.20861e-08, -1.50937e-07, -8.5444e-08, 0.999749, -0.0223832, 1.49062e-07, 0.0223832, 0.999749, 2.17208e-09, -0.406909, -0.00911027)
bind/7/name = &"Spine1"
bind/7/bone = -1
bind/7/pose = Transform3D(1, 8.74227e-08, 8.74225e-08, -8.02673e-08, 0.996895, -0.0787431, -9.40349e-08, 0.0787431, 0.996895, 3.90798e-14, -0.491518, -0.0368954)
bind/8/name = &"Neck"
bind/8/bone = -1
bind/8/pose = Transform3D(1, -4.75749e-08, 5.44752e-07, -1.08596e-07, 0.959087, 0.28311, -5.35933e-07, -0.28311, 0.959087, 7.97016e-08, -0.541607, 0.167146)
bind/9/name = &"Head"
bind/9/bone = -1
bind/9/pose = Transform3D(1, 8.68965e-08, 3.25841e-07, -8.76155e-08, 0.999997, 0.0022074, -3.25648e-07, -0.00220731, 0.999998, -1.9294e-09, -0.622479, -0.00809243)
bind/10/name = &"LeftShoulder"
bind/10/bone = -1
bind/10/pose = Transform3D(0.154985, -0.0162676, 0.987783, 0.976142, -0.151396, -0.155652, 0.152078, 0.988339, -0.0075846, 0.00271129, 0.0728375, -0.542446)
bind/11/name = &"LeftArm"
bind/11/bone = -1
bind/11/pose = Transform3D(0.148851, -0.0564558, 0.987247, 0.880969, -0.4459, -0.158326, 0.449151, 0.893301, -0.0166368, 0.0247572, 0.158227, -0.518332)
bind/12/name = &"LeftForeArm"
bind/12/bone = -1
bind/12/pose = Transform3D(-0.592081, -0.805862, -0.00519985, 0.795252, -0.585306, 0.158087, -0.13044, 0.0894653, 0.987412, 0.506968, 0.110984, 0.0156244)
bind/13/name = &"LeftHand"
bind/13/bone = -1
bind/13/pose = Transform3D(-0.590803, -0.806816, -2.16067e-07, 0.803435, -0.588327, 0.0914593, -0.0737911, 0.0540343, 0.995809, 0.507025, -0.00695802, 0.0127108)
bind/14/name = &"LeftHandIndex1"
bind/14/bone = -1
bind/14/pose = Transform3D(-0.00542012, 0.00542039, -0.999971, 0.63165, -0.775217, -0.0076259, -0.775235, -0.631672, 0.00077798, 0.0250735, 0.040415, 0.524214)
bind/15/name = &"LeftHandIndex2"
bind/15/bone = -1
bind/15/pose = Transform3D(0.03511, -0.037178, -0.998692, 0.466316, -0.883245, 0.049274, -0.883922, -0.467436, -0.013674, 0.0236327, 0.117721, 0.51116)
bind/16/name = &"LeftHandMiddle1"
bind/16/bone = -1
bind/16/pose = Transform3D(-0.00585239, 0.00120206, -0.999982, 0.646658, -0.762766, -0.00470155, -0.762758, -0.646674, 0.00368668, 0.00187246, 0.0324674, 0.523446)
bind/17/name = &"LeftHandMiddle2"
bind/17/bone = -1
bind/17/pose = Transform3D(-0.00870351, 0.00374581, -0.999955, 0.58175, -0.813328, -0.00811028, -0.813322, -0.581794, 0.00489968, 0.00211099, 0.053249, 0.520831)
bind/18/name = &"LeftHandPinky1"
bind/18/bone = -1
bind/18/pose = Transform3D(-0.0786782, 0.0827304, -0.993462, 0.643071, -0.757276, -0.113991, -0.761755, -0.647834, 0.00637951, -0.0430797, 0.031603, 0.518756)
bind/19/name = &"LeftHandPinky2"
bind/19/bone = -1
bind/19/pose = Transform3D(-0.0477945, 0.0602707, -0.997037, 0.592175, -0.802134, -0.0768757, -0.804391, -0.594094, 0.00264677, -0.0471262, 0.0526599, 0.515965)
bind/20/name = &"LeftHandProp"
bind/20/bone = -1
bind/20/pose = Transform3D(0.0146597, -0.01466, -0.999785, 0.749719, -0.661433, 0.0206916, -0.661594, -0.749861, 0.00129447, -0.00967823, -0.0325254, 0.488159)
bind/21/name = &"LeftHandRing1"
bind/21/bone = -1
bind/21/pose = Transform3D(-0.0334299, 0.0362908, -0.998782, 0.530428, -0.846342, -0.0485057, -0.847072, -0.531403, 0.00904346, -0.0227658, 0.106347, 0.514531)
bind/22/name = &"LeftHandRing2"
bind/22/bone = -1
bind/22/pose = Transform3D(-0.0292329, 0.0258762, -0.999238, 0.56938, -0.8212, -0.0379232, -0.821555, -0.570054, 0.00927269, -0.0207978, 0.0622496, 0.518007)
bind/23/name = &"LeftHandThumb1"
bind/23/bone = -1
bind/23/pose = Transform3D(0.849456, 0.405672, -0.337424, 0.526431, -0.695177, 0.48949, -0.035997, -0.593431, -0.80408, -0.418472, 0.0767613, 0.25556)
bind/24/name = &"LeftHandThumb2"
bind/24/bone = -1
bind/24/pose = Transform3D(0.733185, 0.473096, -0.488488, 0.639472, -0.724021, 0.258592, -0.231337, -0.50197, -0.833373, -0.394265, 0.0159969, 0.29309)
bind/25/name = &"RightShoulder"
bind/25/bone = -1
bind/25/pose = Transform3D(0.154985, 0.0162673, -0.987783, -0.976141, -0.151396, -0.155652, -0.152078, 0.988339, -0.00758476, -0.00271116, 0.0728373, -0.542446)
bind/26/name = &"RightArm"
bind/26/bone = -1
bind/26/pose = Transform3D(0.148851, 0.056456, -0.987247, -0.880969, -0.4459, -0.158326, -0.449152, 0.8933, -0.0166366, -0.0247573, 0.158227, -0.518332)
bind/27/name = &"RightForeArm"
bind/27/bone = -1
bind/27/pose = Transform3D(-0.592081, 0.805861, 0.00519994, -0.795252, -0.585306, 0.158087, 0.13044, 0.0894653, 0.987411, -0.506968, 0.110984, 0.0156244)
bind/28/name = &"RightHand"
bind/28/bone = -1
bind/28/pose = Transform3D(-0.590803, 0.806816, 2.38418e-07, -0.803434, -0.588327, 0.0914592, 0.0737909, 0.0540343, 0.995809, -0.507025, -0.00695802, 0.0127108)
bind/29/name = &"RightHandIndex1"
bind/29/bone = -1
bind/29/pose = Transform3D(-0.00542055, -0.00542033, 0.999971, -0.63165, -0.775217, -0.00762611, 0.775235, -0.631672, 0.000778234, -0.0250736, 0.0404151, 0.524214)
bind/30/name = &"RightHandIndex2"
bind/30/bone = -1
bind/30/pose = Transform3D(0.03511, 0.037178, 0.998692, -0.466316, -0.883245, 0.049274, 0.883921, -0.467436, -0.0136742, -0.0236326, 0.117721, 0.51116)
bind/31/name = &"RightHandMiddle1"
bind/31/bone = -1
bind/31/pose = Transform3D(-0.00585272, -0.00120191, 0.999982, -0.646658, -0.762766, -0.00470168, 0.762758, -0.646674, 0.00368691, -0.00187258, 0.0324674, 0.523446)
bind/32/name = &"RightHandMiddle2"
bind/32/bone = -1
bind/32/pose = Transform3D(-0.00870333, -0.00374606, 0.999955, -0.581749, -0.813328, -0.00811041, 0.813322, -0.581793, 0.00489929, -0.00211077, 0.053249, 0.520831)
bind/33/name = &"RightHandPinky1"
bind/33/bone = -1
bind/33/pose = Transform3D(-0.078678, -0.0827302, 0.993461, -0.643071, -0.757276, -0.11399, 0.761755, -0.647834, 0.00637946, 0.0430797, 0.0316031, 0.518756)
bind/34/name = &"RightHandPinky2"
bind/34/bone = -1
bind/34/pose = Transform3D(-0.0477943, -0.0602708, 0.997037, -0.592175, -0.802134, -0.0768757, 0.804391, -0.594094, 0.0026465, 0.0471264, 0.0526601, 0.515965)
bind/35/name = &"RightHandProp"
bind/35/bone = -1
bind/35/pose = Transform3D(0.0146596, 0.01466, 0.999785, -0.749719, -0.661433, 0.0206915, 0.661594, -0.749861, 0.00129446, 0.00967823, -0.0325254, 0.488159)
bind/36/name = &"RightHandRing1"
bind/36/bone = -1
bind/36/pose = Transform3D(-0.03343, -0.0362908, 0.998782, -0.530427, -0.846342, -0.0485058, 0.847071, -0.531402, 0.00904347, 0.0227658, 0.106347, 0.514531)
bind/37/name = &"RightHandRing2"
bind/37/bone = -1
bind/37/pose = Transform3D(-0.0292331, -0.0258763, 0.999238, -0.569379, -0.821199, -0.0379234, 0.821555, -0.570054, 0.00927263, 0.0207979, 0.0622497, 0.518007)
bind/38/name = &"RightHandThumb1"
bind/38/bone = -1
bind/38/pose = Transform3D(0.849456, -0.405671, 0.337424, -0.52643, -0.695177, 0.48949, 0.0359973, -0.593431, -0.80408, 0.418472, 0.0767614, 0.25556)
bind/39/name = &"RightHandThumb2"
bind/39/bone = -1
bind/39/pose = Transform3D(0.733185, -0.473095, 0.488488, -0.639471, -0.724021, 0.258592, 0.231337, -0.501969, -0.833372, 0.394265, 0.015997, 0.29309)
bind/40/name = &"RightUpLeg"
bind/40/bone = -1
bind/40/pose = Transform3D(-0.00746176, -0.167173, -0.985899, 2.12691e-07, -0.985927, 0.167178, -0.999972, 0.00124723, 0.00735678, 0.0506222, 0.335074, -0.0699054)
bind/41/name = &"RightLeg"
bind/41/bone = -1
bind/41/pose = Transform3D(0.0088034, 0.272034, -0.962247, 0.00948438, -0.962264, -0.271952, -0.999916, -0.00673222, -0.0110513, -0.0328069, 0.190194, -0.0680165)
bind/42/name = &"RightFoot"
bind/42/bone = -1
bind/42/pose = Transform3D(-1, -7.96235e-06, -1.49208e-06, -1.75075e-07, -0.161424, 0.986885, -8.09726e-06, 0.986885, 0.161424, -0.0679638, 0.0303778, -0.0284938)
bind/43/name = &"RightToeBase"
bind/43/bone = -1
bind/43/pose = Transform3D(1, -2.54398e-06, -1.973e-07, -1.74233e-07, -0.149938, 0.988695, -2.54618e-06, -0.988695, -0.149938, 0.0679641, -0.0499051, 0.0279147)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_qsq81"]
resource_name = "Color"
cull_mode = 2
albedo_color = Color(0.867188, 0.867188, 0.867188, 1)
albedo_texture = ExtResource("4_6kp8l")
roughness = 0.5

[sub_resource type="ArrayMesh" id="ArrayMesh_fyusp"]
resource_name = "Cute_Characters_Mesh_001"
_surfaces = [{
"aabb": AABB(-0.103009, 0.708793, 0.197722, 0.206017, 0.190548, 0.0523151),
"attribute_data": PackedByteArray("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"),
"bone_aabbs": [AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(-0.103009, 0.708793, 0.197722, 0.206017, 0.190548, 0.0523151)],
"format": 34359745559,
"index_count": 1008,
"index_data": PackedByteArray("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"),
"lods": [0.00720031, PackedByteArray("YAABAAIAYAAFAAEAYQBgAAIAYQACAAMAYQADAAQABgBhAAQAYABiAAUAYgAIAAUAYgAHAAgAYwAGAAcAYwAHAGIABgBjAGEAYABhAAAAYQBjAAAAAABiAGAAZABjAGIAAABjAGQAAABkAGIAZQAKAAsAZQALAA4AZgAKAGUAZgAMAAoAZgANAAwADwANAGYAZQAJAGYADwBmAGcAZgAJAGcAZwAQAA8ACQBlAGgACQBoAGcAZQAOAGgAZwBpABAAaABpAGcAaQBoAA4AaQARABAAaQAOABEAEgBqAGsAIwBrAGoAbABrACMAbQAjAGoAawBsAG4AbgASAGsAIwBvAGwAIwBtAG8AbABvACUAbwAnACUAbAAlACIAbwApACcAbwBtACkAIABsACIAbgBsACAAbgAgACEAbQAoACkAbgAhABQAbQAmACgAbQAkACYAbQAfACQAbgAUAB4AbgAeAB0AHQAcAG4AbgAcABsAbgAbABIAEgAbABoAEgAaABkAbQBwAB8AcAAVAB8AbQBqAHAAcAATABUAEgAZAHEAcQBqABIAcQBwAGoAcQATAHAAcQAZABgAcQAWABMAcQAYABcAcQAXABYAKgArACwAKwAtAC4ALwAqADAALQAxADIAMQAvADMAcgA2ADQANAA8AHIAcgBzADYAcwA1ADYAdAByADwAcwA5ADUAdAA8AD0AcwB1ADkAdQA4ADkAdAA9AD4AdQA/ADgAdgB0AD4AdgA+AEIAdgBCAEEAdQB3AD8AdwB2AEEAdwBAAD8AdwBBAEAAdwB4AHYAdQB5AHcAeAB3AHkAeQB1AHoAcwB6AHUAeQB6ADcAeAB5ADcAegBzAHsAegB7ADcAcgB7AHMAeAA3ADsAewA6ADcAOwBEAHgAewByAHwAewB8ADoAdAB8AHIAfABDADoAfQB4AEQAfAB0AH0AfQBEAEMAfAB9AEMAdgB9AHQAfQB2AHgARQBGAEcARwBIAEkASgBLAEUATABNAEoASQBOAEwAfgBQAH8AfwBQAFEAfgBPAFAAfwBRAFwATwB+AFMAfwBcAIAAgABcAFsAgABbAF4AgQBTAH4AgQBUAFMAgQBVAFQAgABeAIIAggBeAF8AggBfAF0AgwBVAIEAggBdAIMAgwBWAFUAgwBdAFYAggCDAIQAgACCAIUAhACFAIIAhQCGAIAAfwCAAIYAhABaAIUAWQCFAFoAhQBZAIYAWgCEAFgAUgCGAFkAhgCHAH8AhgBSAIcAfgB/AIcAiABYAIQAiACEAIMAWACIAFcAgwCBAIgAhwCJAH4AgQB+AIkAhwBSAIkAiQCIAIEAiQBSAFcAiQBXAIgA"), 0.0161107, PackedByteArray("AAABAAIAAAACAAMAAAADAAQAAAAFAAEABgAAAAQAAAAGAAUABgAHAAUABQAHAAgACQAKAAsACQAMAAoACQANAAwACQALAA4ADwANAAkADwAJAA4ADgAQAA8ADgARABAAEgATABQAFAATABUAEgAWABMAEgAXABYAEgAYABcAEgAZABgAEgAaABkAEgAbABoAHAAbABIAFAAcABIAFAAdABwAFAAeAB0AHwAUABUAFAAgACEAFAAiACAAIwAUAB8AFAAjACIAIwAfACQAIgAjACUAIwAkACYAIwAnACUAIwAmACgAIwApACcAIwAoACkAKgArACwAKwAtAC4ALwAqADAALQAxADIAMQAvADMANAA1ADYANQA0ADcAOAA1ADcANQA4ADkANAA6ADcAOwA4ADcAOgA0ADwAOgA8AD0AOgA9AD4AOAA7AD8AOwBAAD8AOwBBAEAAQgA6AD4AOwBCAEEAOgBCAEMAQgA7AEQAQgBEAEMARQBGAEcARwBIAEkASgBLAEUATABNAEoASQBOAEwATwBQAFEAUQBSAE8AUgBTAE8AUgBUAFMAUgBVAFQAVgBVAFIAUgBXAFYAWABWAFcAUgBRAFkAVgBYAFoAWwBZAFEAUQBcAFsAWQBbAFoAWgBdAFYAWwBeAFoAWgBfAF0AWgBeAF8A")],
"material": SubResource("StandardMaterial3D_qsq81"),
"name": "Color",
"primitive": 3,
"skin_data": PackedByteArray("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"),
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 250,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_uj2ed"]
resource_name = "Cute_Characters_body_1_140"
_surfaces = [{
"aabb": AABB(-0.414016, 0.00356821, -0.2531, 0.828032, 1.15243, 0.496542),
"attribute_data": PackedByteArray("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"),
"bone_aabbs": [AABB(0, 0, 0, -1, -1, -1), AABB(-0.114422, 0.233144, -0.0945056, 0.228845, 0.272083, 0.176225), AABB(-0.043311, 0.144268, -0.0892898, 0.157733, 0.255509, 0.1665), AABB(0.0202369, 0.00356821, -0.0524939, 0.097011, 0.266265, 0.11991), AABB(0.0270145, 0.00356821, -0.0457595, 0.0902334, 0.0894947, 0.154839), AABB(0.0270145, 0.00356821, 0.0122665, 0.0902334, 0.0790809, 0.105497), AABB(-0.139624, 0.308649, -0.0945056, 0.279248, 0.254549, 0.180078), AABB(-0.0810799, 0.416584, -0.0945056, 0.16217, 0.191389, 0.178523), AABB(-0.0793676, 0.550519, -0.0754359, 0.158735, 0.0607961, 0.133998), AABB(-0.285194, 0.595952, -0.2531, 0.570388, 0.560044, 0.496542), AABB(-0.0739926, 0.395841, -0.0945056, 0.233052, 0.212296, 0.180078), AABB(0.0294663, 0.380204, -0.0894775, 0.239779, 0.212411, 0.171216), AABB(0.158019, 0.367884, -0.0483463, 0.203933, 0.154846, 0.0868923), AABB(0.264202, 0.346707, -0.0519648, 0.116339, 0.0938643, 0.0939318), AABB(0.338161, 0.327271, -0.00290689, 0.071494, 0.0786773, 0.0596792), AABB(0.372154, 0.323349, 0.0102688, 0.0375012, 0.0329265, 0.0253258), AABB(0.349427, 0.331443, -0.0306795, 0.0580213, 0.0809655, 0.0673393), AABB(0.373172, 0.320682, -0.0113403, 0.0408443, 0.0344249, 0.0216091), AABB(0.35228, 0.335185, -0.0543153, 0.0450277, 0.0642901, 0.0411), AABB(0.369195, 0.33366, -0.0543153, 0.0281123, 0.0387312, 0.0247653), AABB(0, 0, 0, -1, -1, -1), AABB(0.348795, 0.329462, -0.0495905, 0.0587157, 0.0827101, 0.0503116), AABB(0.378722, 0.325296, -0.0331545, 0.0287892, 0.0319929, 0.0191994), AABB(0.269588, 0.329665, -0.00447848, 0.109237, 0.0956604, 0.0663849), AABB(0.318272, 0.321723, 0.0185363, 0.0534521, 0.0557499, 0.0445938), AABB(-0.159059, 0.395841, -0.0932413, 0.233052, 0.212296, 0.178814), AABB(-0.269246, 0.380204, -0.0894775, 0.239779, 0.212411, 0.171216), AABB(-0.361952, 0.367884, -0.0483463, 0.203933, 0.154846, 0.0868923), AABB(-0.380541, 0.346707, -0.0519648, 0.116339, 0.0938643, 0.0939318), AABB(-0.409655, 0.327271, -0.00290689, 0.071494, 0.0786773, 0.0596792), AABB(-0.409655, 0.323349, 0.0102688, 0.0375012, 0.0329265, 0.0253258), AABB(-0.407449, 0.331443, -0.0306795, 0.0580213, 0.0809655, 0.0673393), AABB(-0.414016, 0.320682, -0.0113403, 0.0408443, 0.0344249, 0.0216091), AABB(-0.397308, 0.335185, -0.0543153, 0.0450277, 0.0642901, 0.0411), AABB(-0.397308, 0.33366, -0.0543153, 0.0281123, 0.0387312, 0.0247653), AABB(0, 0, 0, -1, -1, -1), AABB(-0.407511, 0.329462, -0.0495905, 0.0587157, 0.0827101, 0.0503116), AABB(-0.407511, 0.325296, -0.0331545, 0.0287892, 0.0319929, 0.0191994), AABB(-0.378825, 0.329665, -0.00447848, 0.109237, 0.0956604, 0.0663849), AABB(-0.371724, 0.321723, 0.0185363, 0.0534521, 0.0557499, 0.0445938), AABB(-0.114422, 0.144268, -0.0892898, 0.157733, 0.255509, 0.1665), AABB(-0.117248, 0.00356821, -0.0524939, 0.097011, 0.266265, 0.11991), AABB(-0.117248, 0.00356821, -0.0457595, 0.0902334, 0.0894947, 0.154839), AABB(-0.117248, 0.00356821, 0.0122665, 0.0902334, 0.0790809, 0.105497)],
"format": 34359745591,
"index_count": 10212,
"index_data": PackedByteArray("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"),
"lods": [0.00617539, PackedByteArray("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"), 0.0163155, PackedByteArray("LAARAVAAEQESAVAALACZABEBEgERAU8AmQBPABEBmQAsAFoAnACZAFoAWgAsABMBnAAUAZkAFAFPAJkAnABaABUBWgATARUBFAGcAC8AFAEWAU8ALwAWARQBFwFPABYBLwCeABYBFgGeABcBnAAYAS8AFwEtAE8AEgFPAC0AnACdABgBnAAVAZ0AGAGfAC8AnwAZAS8AGQGeAC8AGAFZAJ8AGgEZAZ8AGwFZABgBGwEYAZ0AWQAcAZ8AWQAbAR0BWQAdARwBoACeABkBGgGgABkBoAAeAZ4AoQAaAZ8AnwAcAaEAHwGgABoBHgFRAJ4AngBRABcBUwAeAaAAUwCgAB8BwwAeAVMAxABTAB8BxADDAFMAHgEgAVEAwwAgAR4BEAAXAVEAUQAgASEBUQAhARAAEAAiARcBIQEjARAAIwEiARAALQAXASIBIQEkASMBIAElASEBJAEhASUBAwAlASABAwAgAcMAJAElAb8AJgElAQMAJgG/ACUBwwAnAQMAAwAoASYBAwAnASgBVQAnAcMAJwHCACgBVQDCACcBvgAmASgBPgC/ACYBvgA+ACYBwgBDACgBKAFDAL4AxABVAMMAVQDhAMIAxADiAFUAxABCAOIAVQDiACkBVQApAeEA4wBCAMQA4gBCAFYA4gBWACkBVgBCAOMA4wDEAB8BHADhACkBVgAcACkB4wAfARIAHwEaARIAKgFWAOMA5gAqAeMAEgDmAOMA5QBWACoBVwASABoBoQBXABoBVwArARIALAFXAKEALAErAVcALQESACsBLQHmABIALAEtASsB5QAuAVYAVgAuARwAHAAuAeAAHADgAOEA5AAuAeUA5ADgAC4B4QDgAC8B4ADkAC8B4QAvAcIAwgAvAUMALwHkADABLwEwAUMA5QAxAeQAMAHkADEBMQHlACoB5gAxASoBMgExAeYAMAExATIBMgHmAC0BMAEyAUEAQQAyAS0BMAFBAEMAQQAtAb0ALAG9AC0BQwBBADMBMwFBAL0AQwAzAb4AMwE+AL4AvQBUADMBMwFUAD4ALAFUAL0AVAAsATQBLAGhADQBHAE0AaEAVAA0AbsAHAE1ATQBNQG7ADQBHAE2ATUBHQE2ARwBNgE3ATUBNQE3AbsANgEdAQIAAgA3ATYBOAFUALsAVAA4AT4AOQE+ADgBOQG/AD4AOQEkAb8AOQE4AT0AOAG7AD0AJAE5AToBOQE9ADoBJAE6ASMBuwA7AT0AOwG7ADcBPQC8ADoBOwG8AD0AOgE8ASMBOgG8ADwBIwE8ASIBPAHAACIBvADAADwBLQAiAcAAOwFSALwAUgDAALwALQDAAMEAUgDBAMAALQDBAD0BLQA9ARIBOwFYAFIAOwE3AVgAtwDBAFIAPgE9AcEAtwA+AcEALgC3AFIALgBSAFgAPwE+AbcAQAE9AT4BPwFAAT4BQAEXAD0BFwASAT0BQQFAAT8BFwBAAUEBCgA/AbcALgAKALcAQQE/AUIBQgE/AQoAFwCVABIBQQFDARcAlQAXAEMBUAASAZUAQgHFAEEBQQHFAEMBQgEwAMUAQgEKAEQBQgFEATAARAEKAHoAegAKAC4ARQFDAcUAMABFAcUAdQAwAEQBRAF6AHUAyABFATAALgC0AHoARgHIADAARgEwAHUAyABHAUUBeQDIAEYBRwHIAHkAGQBFAUcBRQEZAEMBGQBIAUMBlQBDAUgBGQDGAEgBSQGVAEgBxgBJAUgBSQFQAJUAGQBHAUoBxgAZAEoBSgFHAccAxwBHAXkAeADGAEoBeABKAccAxgB2AEkBlgB2AMYAlgDGAHgAdgCUAEkBlABQAEkBdgBLAZQAlgBLAXYAlACTAFAAkwAsAFAASwFMAZQATAGTAJQAlwAsAJMATAGXAJMAEwEsAJcASwEAAEwBywBLAZYAywAAAEsBywCWADEAlgDOADEAlgB4AM4AGwDLADEAzgAbADEAeABNAc4AzgBNARsAeADQAE0BeADHANAAdwBNAdAAGwBNAXcA0ADHAE4BxwB5AE4BdwDQAM8A0ABOAc8AGwB3AE8BdwDPAE8BzwBOAc0AzQBPAc8ATgF5AFABTgFQAc0AzAAbAE8BzQDMAE8BzABRARsAUQHLABsAzADNAFIBUgFRAcwAUAFSAc0AygDLAFEBygBRAVIBAADLAMoAUAFTAVIBUwHKAFIBVAEAAMoAUwFUAcoAUAEaAFMBUAF5ABoAGgBUAVMBeQBGARoAVAFVAQAARgHJABoAGgDJAFQByQBGAXUAVgFUAckAVgFVAVQBVgHJAHUAdAAAAFUBVgF0AFUBdABMAQAAmgBMAXQAVgGaAHQAmgCXAEwBdQBXAVYBVgFXAZoAdQB6AFcBlwCaAFgBWAGaAFcBWQFXAXoAWAFXAVkBWQF6ALQAXQCXAFgBEwGXAF0AWQFaAVgBWgFZAbQAXQBYAZsAmwBYAVoBWwETAV0AXQCbAFsBWwEVARMBBABaAbQAWgEEAJsAXAFbAZsAXQGbAAQAXAGbAF0BAQAVAVsBWwFcAQEAXgEVAQEABAC0ADIAXAAVAV4BnQAVAVwAXwFeAQEAXAFgAQEAXwEBAGABXAFfAGABXwBcAV0BXwFgAaYAXwCmAGABYQFeAV8BXwBdAWIBYwFeAWEBYwFcAF4BMwBfAGIBZAFcAGMBXgBkAWMBXgBjAWEBZAFlAVwAZAFeAKQApABlAWQBZQFmAVwAXABmAZ0AZQFnAWYBpABnAWUBZgEFAJ0ABQAbAZ0AaAEFAGYBZwFoAWYBGwEFAGkBaQEdARsBaQEFAGoBaAFqAQUAaQG5AB0BuQACAB0BaQFqAVsAWwC5AGkBaAGjAGoBWwBqAaMAawECALkAawFsAQIAAgBsATcBbAFYADcBPABsAWsBbAFtAVgAbAE8AG0BbgFrAbkAPABrAW4BWwBvAbkAbgG5AG8BPwBvAVsAuABuAW8BuABvAT8APABuAXABcAFuAbgAPAC1AG0BPABwAWIAPABiALUAuADsAHABcAFxAWIAcAHsAHEBuAByAewAcgG4AD8A7ABFAHEBYgBxAXMBcQFFAHMBcgF0AewA7AB0AUUAcgG6AHQBcgE/AHUBugByAXUBdAF2AUUAdQE/AHcBugDrAHQBdAHrAHYBugB1AUYARgB1AXcBeAF3AT8AeAE/AFsAWwCjAHgBeAF5AXcBowB5AXgBRgB3AXoBeQF6AXcBRgDqALoAegHqAEYAugAVAOsA6gAVALoAowB7AXkBeQF8AXoBewF8AXkBfQF7AaMAfQGjAGgBewEeAHwBfgF9AWgBfQH7AHsBfgH7AH0B+wAeAHsBfgFoAWcBfAF/AXoBegF/AeoAHgCAAXwBfAGAAX8B+wCBAR4AfgFkAPsAZACBAfsAggFkAH4BggF+AWcBZADtAIEBggFnAWMAZwGkAGMAggGDAWQAgwHtAGQAYwBzAYIBgwGCAXMBpACEAWMAYwCEAXMBhAFiAHMBgwFzAUUAhAG1AGIARQBlAIMBgwFlAO0AhAGkAB0AHQC1AIQBpACFAR0ApABeAIUBtQAdAIYBhQGGAR0AXgClAIUBhgGFAaUAXgBhAaUAhgG2ALUAtgBtAbUAtgBYAG0BhgFhALYAhgGlAGEAtgCHAVgALgBYAIcBLgCHAbQAMgC0AIcBMgCHAbYAGAC2AGEAMgC2ABgApQARAGEAGABhABEAGACIATIAiAEEADIAiAFdAQQAewBdAYgBGAB7AIgBewBiAV0BGAARAHwAGAB8AHsAswBiAXsAfACzAHsAswAzAGIBEQCrAHwAswB8AIkBfACrAIkBswCJAYoBigEzALMAigGJAYsBiQGrAIsBigGMATMAqQCMAYoBigGLAakAjAGNATMAjgGrABEApQCOAREANQCOAaUAYQE1AKUAjgGPAasANQCPAY4BkAE1AGEBNQCQAY8BkAFhAZEBkQFhAV8BkQFfAZIBkgFfAaYAkAGRAacAkQGSAagAkQGoAKcAkgGmAJMBkgGTAagAkAGnAJQBlAGPAZABlAGnAKoAlAGVAY8BqwCPAZUBpwCoAJYBpwCWAaoAqAAgAJYBqACTASAAIAAjAJYBkwGXASAAIwCGAJYBhgCqAJYBIwAgAJgBIACXAZgBkwGZAZcBpgCZAZMBlwGsAJgBpgCaAZkBXwCaAaYAmgFfADMAmgEzAI0BmgGNAZkBmQGbAZcBmQGNAZsBlwGbAawANACbAY0BNACNAYwBqQA0AIwBmwE0AIMANACpAIMAmwGDAKwAgwCpADYArACDAJwBqQCxADYAqQCLAbEAiwETALEAiwGrABMAqwCVARMAsQATACEAlQEhABMAsAA2ALEAIQCwALEANgCuAIMAnAGDAK4AsACdATYAnQGeATYArgA2AJ4BnwGeAZ0BnwGdAbAArgCeAaABrgCgAZwBnwGvAJ4BngGvAKABoQGfAbAArwCfAaEBnAGgAaIBogGgAa8AnAGiAYEArwAiAKIBrwChASIAgQCiASIArACcAYEAowEiAKEBgQAiAKMBrQCsAIEAowGtAIEArACtAJgBmAGtAKQBowGkAa0AmAGkASMAowGhAaUBpQGhAbAAowGlAaYBpwGlAbAApwGmAaUBIQCnAbAAowGoAaQBowGmAagBqAEkAKQBIwCkASQApgGAAKgBqAGAACQApgGnAbIAsgCnASEAsgDfAKYBpgHfAIAAsgB+AN8AfgCyACEA3wB/AIAAqQHfAH4AqQF/AN8AfwCqAYAA3gB/AKkBfwDeAKoBgACqAd0AqgHeAN0A3QAkAIAA3gCpARQAqQF+ABQAFADdAN4AqwEUAH4AfgAhAKsB3QAUAKwBqwGsARQAIQA7AKsBOwCsAasBrQHdAKwBOwCtAawBrQGuAd0AJADdAK4BrQELAK4BOwALAK0BrgELAK8BrgGvASQAIQCwATsAIQCVAbABOwCxAQsA2wCvAQsA2wALALEBOwDVALEBOwCwAdUAsQF9ANsAsQHVAH0A2wDcAK8B3ADbAH0ArwHcACQAfQDVANoA2gDcAH0A2gDVALABsgEkANwA2gCyAdwA2AAkALIB2QCyAdoA2ACyAdkA2gCwAbMB2QDaALMB2ADZALQB1ACzAbAB2QCzATkA2QA5ALQB1AA5ALMBsAGVAdQAtAHXANgAOQDXALQBtQEkANgA1wC1AdgAIwAkALUBOQA6ANcAtgE6ADkA1wA6ALcBOgC2AbcB1wCEALUBhADXALcBtwG2AdYAtwHWAIQAOQDWALYBhAAMALUBIwC1AQwAuAGEANYAhAC4AQwAOQC5AdYAuQG4AdYAuQE5ANQAugG4AbkBDAC4AboBugG5AdQADAC7ASMADAC6AbsBugGFALsBugHUAIUAhwAjALsBhQCHALsBhwC8ASMAIwC8AYYAvQG8AYcAOACHAIUAvQGHADgA0QC8Ab0B0QCGALwBOADRAL0BvgGGANEAvgHRADgAhgC+AaoAOACFAL8BhQDUAL8BOADSAL4BvgHSAKoA0gA4AL8BvwHUANMA0gC/AdMAqgDSANMA1ACVAdMAlAGqANMAlQGUAdMAwAEVAGgAwQFoABUAFQDqAMEB5wAVAMAB5wDrABUA6ADAAWgA6ADnAMABwQHpAGgA6QDBAeoA6ABoAMIBaADpAMIBwgHDAegAwwHnAOgAxAHCAekA6QDqAGcA6QBnAMQB6gB/AWcAwgHEAUoAwgFKAMMBZwBKAMQB8gBnAH8BfwGAAfIAZwDFAUoAxQHDAUoAxQFnAPIAgAHGAfIAxQHyAMYBgAEfAMYBHwCAAR4AHgCBAR8AxQHHAcMBxwHnAMMBxQHGAcgBxQHJAccBxQHIAckByAHGAUkAHwBJAMYBygHnAMcBygHHAckByQHIAWkAyAFJAGkAyQHuAMoBZgDnAMoB7gBmAMoBZgDrAOcA7gDJAWoAyQFpAGoA7gBqAGsAawBmAO4AdgHrAGYAywFrAGoAagBpAEsAywFqAEsASwBpAMwB7wDLAUsASwDMAe8AaQBsAMwBzAFsAO8A7wAOAMsBywEOAGsADgDvAGwAzQFrAA4AawDNAWYAzQEOAM4BbADOAQ4AZgDNAUgASADNAc4BzwHOAWwASADOAc8BbABpAEkAzwFsANABSQDQAWwAzwHQAfEASADPAfEA0AHRAfEA0AFJANEBSADxANIB9ADxANEB9ADSAfEA9ADRAfMA0QFJAPMA0gH0AG4A8wBuAPQASADSAfAA0gFuAPAAbwBuAPMAbgBvAPAASADwANMBbwDTAfAAZgBIANMBZgDTAXYBbwDzANQB1AHzAEkAbwDVAdMB9QBvANQB9QDVAW8A9wDUAUkA9wD1ANQB9wDWAfUA1wH3AEkA1gH3APgA+AD3ANcBHwDXAUkA9QDWAQYA9QAGANUBBgDWAfgA9gDTAdUB9gDVAQYA0wH2AHYB+ADXAXMABgD4ANgB2QEGANgB+ADaAdgB2AHaAdkBcwDaAfgA2gFyANkB2gFzAHIABgDZAXIAcwDXAQ0AHwANANcB2wFyAHMAcwANANsBBgByANwB3AFyANsB3AH2AAYA3QHcAdsBDQDdAdsB3QH2ANwBDQDeAd0BDQAfAN4B3QFwAPYA3QHeAXAAcQDeAR8AcADeAXEAcADfAfYA3wFlAPYA9gBlAHYBRQB2AWUA+QBlAN8B4AHfAXAA+QDfAeAB4AFwAHEA7QBlAPkARwD5AOABcQBHAOAB4QHtAPkARwDhAfkA4gFHAHEAbQDtAOEBbQCBAe0AHwCBAW0A4QFHAPoARwDiAfoA4QH6AG0A4gFxAOMBcQAfAOMB+gDiAeMBHwBtAOMB+gDjAW0A5AEmAOUBJgDmAeUB5QHmAecB6AHmASYA5QHnASUA5gHpAecB6QHmAegB6QHqAecBJQDnAeoB/wDpAegB6wHoASYA/wD9AOkB/QDqAekBigD/AOgBigDoAesB7AH/AIoAJgDtAesB5AHtASYAigDrAe4B7gHsAYoA7AEnAP8AJwD9AP8A7gEBAewB7wEnAOwBAQHvAewB/QAnAPAB8AEnAO8B/QDwAfEB7wEAAfABAAHxAfAB8gH9APEB6gH9APIBAAHzAfEB8wH+APEB8gHxAf4AKADzAQAB8wEoAP4A6gHyAfQB9AHyAf4AEAHqAfQB/gBMAPQBEAH0AUwA9QEoAAABJQDqARAB/gD2AUwA/gAoAAIBAgH2Af4AKAAHAAIBBwAoAPUBAgGRAPYBAgEHAPcB9wGRAAIB9wEHAPgBBwD1AfgB9wH5AZEACQH2AZEA+QEJAZEA9gH6AUwACQH6AfYBTAD6ARAB+AGQAPcBkAD5AfcBCQH5AfsB+wH5AZAACQErAPoBCgEJAfsBCgErAAkB+gErAA8B+gEPARABKwAKAQwBkgAQAQ8BkgAlABABDwErAPwB/AErAAwB/QGSAA8B/QEPAfwBkgD+ASUA/gGSAP0B/gGIACUAJQCIAOUB5QGIAOQBTgD9AfwBTgD8AQwBDgH+Af0B/QFOAA4BiAD+Af8BiAD/AeQB/gEOAf8BTgAMAQACDgFOAAACBgHkAf8B5AEGAe0BAQL/AQ4BBgH/AQECAQIOAYsAiwAOAQACDQEGAQECAQKLAA0BBgECAu0B6wHtAQICDQEHAQYBBgEHAQICiwADAg0BAwIHAQ0BAAIEAosAiwAFAgMCiwAEAgUCjQAEAgACjQAFAgQCAwIGAgcBBQIGAgMCBwEHAgICBwEGAgcC6wECAggCAgIHAggC6wEIAu4BTQAGAgUCTQAFAo0ACQIGAk0ACAIKAu4B7gEKAgEBCAILAgoCCAIHAgsCDAIHAgYCCwIHAgwCDAIGAgkCKQAKAgsCCwIMAikADQIBAQoCDQIKAikADgIBAQ0CDgLvAQEBDwINAikADwIOAg0CKQAMAo4AjgAMAgkCKQAQAg8CKQCOABACjwDvAQ4CDwKPAA4CAAHvAY8A9QEAAY8AAwGPAA8CAwH1AY8AEAIDAQ8CKgD1AQMBEAIqAAMBEAKOACoAKgCOAAkCKgD4AfUB+AEqABECKgAJAhEC+AERApAAEgIRAgkCkAARAhICEgIJAk0AEgL7AZAAEgJNAI0AEwL7ARICEwIKAfsBEgKNAA8AEwISAg8ADwCNABQCDwAUAhMCFAIKARMCjQAAAhQCFAIMAQoBAAIMARQC"), 0.0276327, PackedByteArray("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"), 0.0489848, PackedByteArray("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"), 0.086354, PackedByteArray("LAAtABcALQAuABcALwAtACwALQAQAC4ALwAQAC0AMAAXAC4ALwAsAAUAAAAsABcAMAAZABcAAAAXABkAGgAZADAAAAAaADAAMQAZABoAMQAAABkAMQAaABsAGgAAABsAAAAxABsAAAAwADIAMAAuADIABAAsAAAAMgAEAAAABAABACwALAABAAUAMwAEADIAAQAEADMAAQAzADQAMgARADMAMwARADQAMgA1ABEANQAFAAEANAARACEAEQA1ACEAAQA0AAwADAA1AAEADAA0ADYANAAhADYANgAiAAwAIQAiADYADAAiACQAIQAkACIANQAMADcANwAhADUADAA4ADcAOAA5ADcADAA5ADgAOQAhADcADAA6ADkAOgAMACQAOQA6ACQAOwAhADkAOQAkADsAIQAUACQAJAAUADsAFAAhADsANQAyAB0AHQAFADUAHQAyADwAMgAuADwAPQA8AC4ALgAQAD0APQACADwAPgA9ABAAPAACAD8APAA/AB0ABQA/AAIAAgA9ABIAEgA9AD4AAgAvAAUAAgASAC8AEgAQAC8AHQBAAAUAPgAQAAMAEgADABAAEgA+AEEAQgADABIAQwA+AAMAQgBDAAMAQQA+AEMAEgAcAEIAQQAcABIAQgAcAEMAQwAcAEEAHQBEAEAARAAdAEUAPwBFAB0AQABEAA0ADQAFAEAABQBGAD8ABQANAEYAPwBGAEUARABFAEcADQBEAEcARwBFAAYADQBHAAYARgBIAEUASAAGAEUABgBJAA0ABgBIAEkADQBJAEoADQBKAEYARgBKABUARgAVAEgASAAVAEoASABKAEsASwBKAEkASQBIAA4ASABLAA4ADgBLAEkAJQAmAAgAJQAIAEwAJwAIACYAKABMAAgAKAAIACcAKwBMACgATAArACUAKgArACgAKQAoACcAKgAoACkAJgApACcATQArACoAKQBNACoAJQArAE4ATQBOACsAJgAlAE4ATQApAAkAJgAJACkATgBNAAkATgAJACYA"), 0.106883, PackedByteArray("AQAQABcAGAAXABAAAAABABcAGAABAAAAGQAXABgAAAAXABkAGAAAABkAGQAAABoAAAAZABoAAAAaABsAGgAAABsAAgAYABAAAwACABAAEgADABAAAgADABIAEgAcAAMAAwAcABIAEgAQAAUAAgASAAUABQAQAAEAHQAYAAIAHQAFAAEAAQAYAB0ABQAeAAIAHQAeAAUAAgAVAB0AHgAdABUAAgAeABUAHgAfABUAHgAVAB8AHwAOABUADgAGABUAHwAVAAYABgAOAB8AAQAYABEAGAABABEAAQARACAAEQABACEAIAAhAAEAIAARACEAIAAhACIAIAAiACMAIwAiACQAIwAkACAAIQAkACIAJAAhACAAIQAUACQAFAAhACQAJQAmAAgAJwAIACYAJQAIACgAKAAIACcAJgApACcAKQAoACcAKgAoACkAJgAJACkAJgAlAAkAKgApAA8ADwApAAkAKgArACgADwArACoAKAArACUACQArAA8ACQAlACsA"), 0.180242, PackedByteArray("AQAQAAoAAAABAAoACgABAAAAAgAKABAAAQAKABEAEQAKAAIAAQARAAIAAwACABAAEgADABAAAgADABIAEgAQAAUAAgASAAUABQAQAAEAAgAFAAEAEQABABMAAQARABMAAQATAAwAAQAMABMADAATABQADAAUABMAAgANAAUABQANAAIAAgANABUADQACABUADQAOABUAFQAOAA0ADwAIAAcABwAIABYABwAWAA8AFgAIAAkACQAIAA8ADwAWAAkA"), 0.204014, PackedByteArray("AAABAAoACgABAAAAAQALAAwAAQAMAAsAAQAKAAIAAgAKAAEAAwAKAAIAAwACAAoADQACAA4AAgANAA4ABwAIAAkACQAIAA8ADwAIAAcABwAJAA8A"), 0.271862, PackedByteArray("AwAEAAUAAwAFAAQABQAGAAQABAAGAAUABwAIAAkACQAIAAcA"), 0.30423, PackedByteArray("AAABAAIAAgABAAAA")],
"material": SubResource("StandardMaterial3D_qsq81"),
"name": "Color",
"primitive": 3,
"skin_data": PackedByteArray("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"),
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 1710,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_hnoft"]
resource_name = "Cute_Characters_body_1_002"
_surfaces = [{
"aabb": AABB(-0.327679, 0.743199, -0.05566, 0.655358, 0.129495, 0.0759882),
"attribute_data": PackedByteArray("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"),
"bone_aabbs": [AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(-0.327679, 0.743199, -0.05566, 0.655358, 0.129495, 0.0759882)],
"format": 34359745559,
"index_count": 1416,
"index_data": PackedByteArray("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"),
"lods": [0.0068228, PackedByteArray("HAAoAEAAKABBAEAAHABAAEIAHABDACgARAAcAEIAHABEAEUAHABFAEMAQgAsAEQARAAsAEUAQgBAACkAQgAdACwAKQAdAEIARQAsAC4AHQBGACwALABGAC4ARQAuAEcAQwBFAEcASABGAB0AKQBIAB0ASQBGAEgARgBJAC4ABQBJAEgAAgAFAEgAAgBIACkAAgApAAQABAApAAMAKQABAAMASgBJAAUAAABKAAUADQBKAAAADABKAA0AKQBLAAEAQABLACkAQQBLAEAASwBMAAEAQQBMAEsAAQBMAAYATQBKAAwATQAMAAsASgAtAEkALgBJAC0ATQAyAEoAMgAtAEoATQALADMAMwALAAoAHwAyAE0AHwBNADMAMQAtADIAMQAuAC0ACgBOADMAHwAzAE4ACgAJAE4AHwBPADIAMQAyAE8AHgAuADEACQBQAE4ACABQAAkABwBQAAgABgBQAAcABgBMAFAAUQAuAB4ARwAuAFEAUQBDAEcATAAqAFAATABBACoAKgBSAFAAUgBOAFAAQQBTACoAUwBSACoAUwBBACgAUgBUAE4AHwBOAFQAUwBVAFIAVQBUAFIAUwAoACsAKwBVAFMAKwAoAEMAVQBWAFQAVgAfAFQAKwBXAFUAVwBWAFUAKwBDAFcAVgBYAB8AVgBXAFgAVwBDAC8ALwBYAFcAUQAvAEMAWABZAB8AWQBPAB8AWQAxAE8AWABaAFkAWgAxAFkALwBaAFgALwBRAFoAWgAeADEAUQAeAFoAIgBbADQANABbAFwAIgBdAFsAIgA0AF4AXwBdACIAIgBgAF8AIgBeAGAAXQBfADcAXwBgADcAXQA1AFsAXQA3ACMANQBdACMAYAA5ADcAIwA3AGEANwA5AGEAYABiADkAXgBiAGAAYwAjAGEANQAjAGMAZABjAGEAYQA5AGQAFABjAGQAEwBjABQAEwA1AGMAEwAPADUADwARADUANQARABAAZQAUAGQADgAUAGUAGwAOAGUAGgAbAGUANQAQAGYAWwA1AGYAXABbAGYAZgAQAGcAXABmAGcAEAASAGcAaAAaAGUAaAAZABoAZQBkAD0AOQA9AGQAaABlAD4APgBlAD0AaAA/ABkAPwAYABkAJQBoAD4AJQA/AGgAGAA/AGkAJQBpAD8AGABpABcAPAA+AD0APAA9ADkAJQA+AGoAPABqAD4AFwBpAGsAFgAXAGsAFQAWAGsAEgAVAGsAEgBrAGcAbABqADwAbAAlAGoAJAA8ADkAZwBrADYAZwA2AFwANgBrAG0AbQBrAGkAXAA2AG4AbgA2AG0AbgA0AFwAbQBpAG8AJQBvAGkAbgBtAHAAcABtAG8AbgA4ADQAOABuAHAAOABeADQAcQBvACUAcABvAHEAOABwAHIAcgBwAHEAOAByAF4AcQAlAHMAcQBzAHIAcwAlAGwAcgA6AF4AOgByAHMAcwBsAHQAOgBzAHQAdABsADwAdAA8ACQAOgB0AHUAdQBeADoAdQB0ACQAdQBiAF4AdQAkADkAYgB1ADkA"), 0.00927273, PackedByteArray("KAABACkAKQABAAMABAApAAMAAgApAAQAAgAdACkAAgAFAB0AKQAdABwAHAAoACkAKgABACgABgABACoABgAqAAcABwAqAAgACAAqAAkAKwAqACgAHAAgACgAKwAoACAAKwAhACoAKgAhAAkAKwAgACEAHAAsACAAHAAdACwALQAsAB0ABQAtAB0AIAAsAC4ALAAtAC4AIAAvACEAIAAuAC8ALwAuAB4ALwAwACEALwAeADAAHgAuADEAMQAuAC0AMAAeADEAMgAtAAUAMQAtADIAAAAyAAUADQAyAAAADAAyAA0AMAAxAB8AMAAfACEAHwAxADIAHwAJACEAMwAyAAwAHwAyADMAHwAzAAkAMwAMAAsACgAJADMAMwALAAoANAA1ABAANQARABAADwARADUAEwAPADUAEwA1ACMAEwAjABQAIgA1ADQANQAiACMANgA0ABAAEgA2ABAAEgAVADYAFQAWADYAFgAXADYAIgA3ACMAIgA0ACYAIgAmADcAOAA0ADYAOAAmADQAOAA2ACcAOAAnACYANgAXACcANwA5ACMAJgA5ADcAJgAnADoAJgA6ADkAOgAkADkAOgAnADsAOgA7ACQAJQAnABcAOwAnACUAOwA8ACQAJAA8ADkAOwAlADwAPQAjADkAPAA9ADkAFAAjAD0APAA+AD0APgAUAD0AJQA+ADwADgAUAD4AGwAOAD4AGgAbAD4AJQA/AD4APwAaAD4AJQAXAD8APwAZABoAGAA/ABcAPwAYABkA"), 0.021118, PackedByteArray("HAABAB0AHQABAAMABAAdAAMAAgAdAAQAAgAFAB0ABQAeAB0AAAAeAAUADQAeAAAADAAeAA0AHwAeAAwAHwAMAAsAIAAdAB4AHAAdACAAIAAeAB8AHwALAAoACgAJAB8AIAAfACEAIQAcACAAHwAJACEAIQABABwACAAhAAkABgABACEABwAhAAgABgAhAAcAIgAjABAAIwARABAADwARACMAEwAPACMAEwAjABQAFAAjACQADgAUACQAGwAOACQAGgAbACQAJQAaACQAJQAZABoAJgAkACMAIgAmACMAJgAlACQAJQAYABkAGAAlABcAJgAnACUAJwAmACIAJQAnABcAJwAiABAAFgAXACcAEgAnABAAFQAWACcAEgAVACcA"), 0.0601588, PackedByteArray("AAABAAIAAgABAAMABAACAAMABQAAAAIABgABAAAABgAAAAcABwAAAAgACAAAAAkACgAJAAAAAAALAAoAAAAMAAsADAAAAA0ADgAPABAADwARABAAEgAOABAADwAOABMAFAATAA4AEgAVAA4AFQAWAA4AFgAXAA4AGAAOABcADgAYABkADgAZABoAGgAbAA4A")],
"material": SubResource("StandardMaterial3D_qsq81"),
"name": "Color",
"primitive": 3,
"skin_data": PackedByteArray("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"),
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 252,
"vertex_data": PackedByteArray("/paKvmE3Tz8976E8QMCLvmBVWz81StK8Tp+NvjrVWj+d2qE8yP+KvjfvWz/ODdu71NaLvhi7Wz83DiQ8dxSMvg57VT8/76E80nqKvinNVT+r4ey8ajaJvjxuTz8IZfW85FeGvtYoST+JAeq8++qDvh6qQz/WGtC88+iCvhEaQz97udm7WtKDvioiQz/5fiM8nhWGvpq1Qz/1t6E8LgaIvgoJST8776E8/paKPmE3Tz8976E81NaLPhi7Wz83DiQ8QMCLPmBVWz81StK8yP+KPjfvWz/ODdu70nqKPinNVT+r4ey8Tp+NPjrVWj+d2qE8dxSMPg57VT8/76E8ajaJPjxuTz8IZfW85FeGPtYoST+JAeq8++qDPh6qQz/WGtC88+iCPhEaQz97udm7WtKDPioiQz/5fiM8nhWGPpq1Qz/1t6E8LgaIPgoJST8776E8G/6gvmoiXD9PJRg8RQOZvtcMXD9D0qU8bzucviCMRD/vKqU8etWNvkVCPj/Xtwk83nynvnyBUj/8zDE8BbGTvku/RD8ahzW9G/6gPmoiXD9PJRg8RQOZPtcMXD9D0qU8bzucPiCMRD/vKqU8etWNPkVCPj/Xtwk83nynPnyBUj/8zDE8BbGTPku/RD8ahzW9YUSZvsGwWz++bRW9ml2QvngoXz95+SI8ebSSvnMBTz+2+2O9TOCevgtCTj9o2DS9pMmevmsOWj/TV6Y8wT6OvqhmRT9tsIk8BDSjvsg0Uj8zoqU8M/yjvj79Rz93fSQ84LeXvjHfPz/sUPg7+wKWvjQ5Qz+p45A8ZsOKvoapQT+rZKY8KvWFvsQ9Pz9Nzxw8YUSZPsGwWz++bRW9ml2QPngoXz95+SI8ebSSPnMBTz+2+2O9pMmePmsOWj/TV6Y8TOCePgtCTj9o2DS9BDSjPsg0Uj8zoqU8M/yjPj79Rz93fSQ84LeXPjHfPz/sUPg7+wKWPjQ5Qz+p45A8wT6OPqhmRT9tsIk8ZsOKPoapQT+rZKY8KvWFPsQ9Pz9Nzxw87wqWvgiqXj+MCrq8ueWTvlSAWT/UZj+9ZIuYvrgJXz9RGxw8NeWlvkCwVz/RcSc8eVGgvthfWz8Xzp88aKWkviBJVz/PYaE8fw2dvtXTWD+x45A8sQimvkVZUj/bI6M8ejuVvts0Wj+z45A8j6aTvq7mUz9xM4g8w5KJvuaaRD/HvKU8dHaPvvuWXj/harm8ipGOvsJVWj+Iqya9Re2HvhMjQD8ZNaA8dpOFvh4QQD8Zybu8VCCQvo/KQD8TbaU8ts6JvjJqST/+iju9T6yivjAbSD/F2KA8X0ONvjK3RD/i8j29rHSZvhKhTj8S3lq91fmLvplKPz9Bf8O8u+6YvuhVRD+iuhC9CciTvvjqQD/GVeS85L+gviS8SD/wZfK8GS2fvuJaQz+3uw4884KXvhdrQD/LEps8YTeevqWkQz85KZ087wqWPgiqXj+MCrq8ueWTPlSAWT/UZj+9ZIuYPrgJXz9RGxw8NeWlPkCwVz/RcSc8eVGgPthfWz8Xzp88aKWkPiBJVz/PYaE8fw2dPtXTWD+x45A8sQimPkVZUj/bI6M8ejuVPts0Wj+z45A8j6aTPq7mUz9xM4g8w5KJPuaaRD/HvKU8dHaPPvuWXj/harm8ipGOPsJVWj+Iqya9Re2HPhMjQD8ZNaA8dpOFPh4QQD8Zybu8VCCQPo/KQD8TbaU8ts6JPjJqST/+iju984KXPhdrQD/LEps8X0ONPjK3RD/i8j29rHSZPhKhTj8S3lq91fmLPplKPz9Bf8O8u+6YPuhVRD+iuhC9CciTPvjqQD/GVeS85L+gPiS8SD/wZfK8GS2fPuJaQz+3uw48YTeePqWkQz85KZ08T6yiPjAbSD/F2KA8viuhvs/aWz+bmha8X7WXvkBJXz+qxtK7U4ycvsFLXD/x8Na8CZSPvt5oXz+Xc8G71UKQvhAhXT9DWwy9UJiUvn7XXD+OqRS9oAyavm7OWD9UCTW9LHSfvhl7WD9Eogy9bmGavm4XVD+welG9ga6TvsGpVD82dlu9ZLGNviVJVT80yD29W4aMvg5PTz+A50S9OgeQvs9yST8GTFm9luaWvipFST+mYFC9iWSHvqBlRD9qzSS9XV2HvuB1QT93Swy9PtyLvt9SQT+wAha95xeFvj0wPz//2tG7kfyMvjZXPj/TpQO8mTKYvsaCQD9vvUC8uCKRvub5QT92URi9Fl+dvvBCRD+ttMO8EGacvnX3SD/UHyu9UZ2gvtAqRT9d14O8IiSjvjThTT8+//+8G4KjvvmoSD84X46800ikvoY+SD+Ojoe7guClvlqMTT/HWpC8C0SkvicaUz9U7+y8k+efviKjUz8MWiq9c92jvj7IVz812bS8OGamvrxLVj/7oWe8aeWmvimLUj8mDoa8GvSmvkhOTT8sA127k8WnvuqIUj+UwEG7JV2mvnRtVz+TrHC738CmvmgoTT9fnTA8Dkqlvo8mTT/ZEaM8duuYvkbrXT/jIqA8OHaivoBTTT/zn6U8cBCgvrCTSD/PWKU8OSCivmafVj8JsqU8qwqfvrZXUj9zM4g8qD+evgWoTT9vM4g8ogacvhQVST9vM4g83GievsxIVj9zsIk8G6aRviP0XT8dSqE8Fx6Zvsq2WT93LYs8ql2TvvAMXD9Th6Y8eF6QvnRyWT8jwaU8ZW6ZvvLZVj/sW3o8z0yUvqn4Vz9zsIk8oh2PvorHVD+he6U8EoOZvoP6Uj8sVnY8KLeNvuL4Tj+fe6U8mXqSvkqYTj9vM4g8MJKYvg4cTj8sVnY8ilqLvjg9ST+de6U8qDeQvpxfST9vM4g8JV+WvtpWST8kVnY8SOuYvuVuRT9vsIk8u9STvlarRT/gW3o8wdyOvpwYPz+f+Zw8LrWNvnwgQz+n45A87LWRvuP9Qj9vLYs87eCWvkXMQT9jwKU8itSfvhDgQz+5mMG7viuhPs/aWz+bmha8U4ycPsFLXD/x8Na8UJiUPn7XXD+OqRS9oAyaPm7OWD9UCTW9LHSfPhl7WD9Eogy9bmGaPm4XVD+welG91UKQPhAhXT9DWwy9ga6TPsGpVD82dlu9ZLGNPiVJVT80yD29X7WXPkBJXz+qxtK7CZSPPt5oXz+Xc8G7W4aMPg5PTz+A50S9OgeQPs9yST8GTFm9iWSHPqBlRD9qzSS9luaWPipFST+mYFC9XV2HPuB1QT93Swy9k+efPiKjUz8MWiq9c92jPj7IVz812bS8C0SkPicaUz9U7+y85xeFPj0wPz//2tG7kfyMPjZXPj/TpQO8mTKYPsaCQD9vvUC8PtyLPt9SQT+wAha9uCKRPub5QT92URi9EGacPnX3SD/UHyu9Fl+dPvBCRD+ttMO8IiSjPjThTT8+//+8UZ2gPtAqRT9d14O8G4KjPvmoSD84X468guClPlqMTT/HWpC800ikPoY+SD+Ojoe7itSfPhDgQz+5mMG7wdyOPpwYPz+f+Zw8GvSmPkhOTT8sA127aeWmPimLUj8mDoa8OGamPrxLVj/7oWe8k8WnPuqIUj+UwEG7JV2mPnRtVz+TrHC738CmPmgoTT9fnTA8DkqlPo8mTT/ZEaM8duuYPkbrXT/jIqA8G6aRPiP0XT8dSqE8OSCiPmafVj8JsqU8ql2TPvAMXD9Th6Y8eF6QPnRyWT8jwaU8Fx6ZPsq2WT93LYs83GiePsxIVj9zsIk8ZW6ZPvLZVj/sW3o8qwqfPrZXUj9zM4g8z0yUPqn4Vz9zsIk8EoOZPoP6Uj8sVnY8oh2PPorHVD+he6U8KLeNPuL4Tj+fe6U8mXqSPkqYTj9vM4g8ilqLPjg9ST+de6U8MJKYPg4cTj8sVnY8qDeQPpxfST9vM4g8qD+ePgWoTT9vM4g8JV+WPtpWST8kVnY8OHaiPoBTTT/zn6U8ogacPhQVST9vM4g8cBCgPrCTSD/PWKU8SOuYPuVuRT9vsIk8u9STPlarRT/gW3o87eCWPkXMQT9jwKU87LWRPuP9Qj9vLYs8LrWNPnwgQz+n45A8K4hUgRjvHsyS3QK72AOZj2Ce7pLN8J/YCs6MskGBN0Cxv8qpV/1G8IKIFIF2GG5IAPPerG4FrZCI8lusSgU8kCjy6q2ABeCQXvLuVGAFGZDl5Jtj7X8GQxPRimmNY58+i6Sjdkky2khziKeBIO9CzNN3VIG/54u3TUDKqTqZgrpsIgK7JvxlcPQxjLK9fse//gzerJD6UW+eYe6SMQ9fJ3x3FIGI55C3dg1brLT6wm/WDeqtfvoeb6AN7lSe+uVvGRubYxGA+LzrLoppcZxfwXNbo3aaEeMmi3engd4QvDOHQy24wwTfBwuBcnbdEStJmokWhRLwq8ype7USN/QUk1EWVYSCknPA3ijBGe37sSp3vC24O/sf+PN+cnYh7tO2ZHYWhewPUzNVhLUSxwvqbK3pVYR8bYs/INfBGRzTAEN3L7Ddpu3gJHWj9cVu9YbvOP6z7ToPGrDqCRI+W0h0u5qItHe+FuJHV2YogcAOw1LwkQKAwPCFz58sel+vAAt481JcMid+icQTkSGariboRw97KoLP6o/IfLRvSEtfxz6H0LDdWBIe24lc9cWQCngQxgGz7cTw5E9kd7R33BG6MxT2Ej6jt4pEDm4CgBrg4rdf03pfT//zhwutXDLXgXU7624hmlDZFrinmSiBPvE7re+EKoKG7j61gktvSLOgN8HXTrXqXLTmMNjVYPt++Mg/oWRi0/EaSlOnK92dgpBXvhRZXKKJxGLOykxYkSG7p8xvmH5sayRgRrxGTYJHCttfXHJAahbhucZ7Zu181tlRx99ymIBiEOZN/byP2qW5w0Xz4BHQnr65PvKhu1tnNdxMe8xyM9oRCZiAfOWEvhEVSlT86L1stnE6gk7va9vCrsH23FoOZ+bIOaAG6CZo89wmJVtAAfjvRCJmKnMr40YAvb0+Kx8kRva8gxq3P0T3DRUlPIZLQ4eZwwhjGE/80J2711R3XiLETrwnsbXqoksYzyYqYPuABzbAXZti0w3ltKxX1N2dfG+nQeqmXKJ1O5wxNLNYkej7CN+PZ35sk9ueuUK5TYK39SOgoo1AaugeRTmDme18KCatOB+NmICc7xiyAUOP2kr1Om8LHxHQYEFFwQxeu1uXyiKzgzNyMyTu9Wd+g+WEQO7ptaoD6L2SSY3F9pwYTwIvYUQII1oOlxk2xl756CaWDCLZ2aRAAQYQut2Y1XMrG7n+QkHBKx/auQhDe+W3P9SqykPZw4ZLu3hlPCerd17cO7BDfLHvayM9UD7gNfbCKW6hwm1ay+LYWTVCyDMB0nxMrb7Erc/UZvMVibvJ/uActf43Rz879rP6fU4TIePb8erMJFMe1scz+MEasQ9B2Ww1ALeF6mTzNtsCNm3woMQRuHI8XfaGwNsIQ5s47+4R/9+9N3cZAB9z+BQpoPDoOdC34TZt1twpJQ7AoIXBFQbZCUe688B/PBse95FFbFMF7SEQwPVDAyvwWQjBWTdOFfr4EyhaLBU1G1mZu6EauDPpR5u7KSnnQaRhpr/8C/NK01WSvK0bN02CZQu9EiFVWYL/qwN/DpxYbmXVvSIHE7Hc+ncVPAu+wAhIRrx/HX270v17ExwWn6pK+XMMqwLLnL378QxyEU9o3f59Az4Djofe/TQCex8PpXV4vr/PH8FvDpFbwWpKjHaovyrFy22xsTgLn08skXKCL/C6z3yOBYT5HTNIO49AfObvqc4/nW9/HfIA1H+bdYSu8RDU85kyibEoaEfrm+F2m/H708OWWax+7mzcBIjYZ+Hu2MppfI161+qSyO1zKnt95PHHF4MHd/oTQ0kkaSd3gQ6sUoNvA35T4d3HiYGoftgS2EhYb0h98ODlx2Fn+3tN2lzHSYE2gOoS4kjmb6h8tQ9QTytn33qQDgtTDIHDgdAS0kjEltWQCinTRyOApokZEk1I1XsFQowE9ztgbBGQH+DsxhKAmpphFW9IJYL5hUHvwckuMwhEU/44eB7K9sKpBGnzNswB0oKzUUG3wDv2SwWBseve49sNFTLbq+HWx8sHPeVN8EHZFxF62kM2/uAW98kleRVk88gk/MmRD6DE7UeMw5Gly+Impsm9OlLP1JF9SsGhCYbAmUmNxMYQ7hH/H0HIXg/oOS5IHcmH5gAfiwfq1pEp3CnFPibLwvS+wKIKl+R/4n27LAKD7Nz4E7EiBYfqCz9/POPhB265k1MFEd7uPwm8Ayu2BeTxeT4VBiX2t0WlyE4VBAfr113luDMVuGNEpNMVNeOmZUQC9PNK2Qbp6tXW50H5B2vzUeQ3TXya80J/8ZxYkJopQuzeVVnBh54/0MwIRKsBxocphAVCcvsHxIzuT2ghAYH8U/3LnEEEDfPi6Z+qjpiyP8D8jodihKo/g+APpYmHQEAv4MFv8G6jPpS1jHZWQNQ6M5KxsYIgoTU7aVmsvdZxvcNwQHwYEFUxlYONeicVbDcRjCp7gRsNOPp32GcdESY1E2ThdmMOAyznfAd3BOy7tr9ib3/hDf4r2pYnd33xUq11fqh+8xFzNnuQA36rHiE4ppBIfQ4fGTidmPt7sSWiOBiQqHzDHhI4tX42gBTtHLfTmN96PiagOH9kdYRQDu4r8n7DgS7tLLfSbnKCzw9EMAtmMolN15a4gnEFhAXiy7c6adWQ9NYruNt/ponl7bG32X35hb0QPTbsf5qaneqPt56TEZDfHxI5")
}]
blend_shape_mode = 0

[sub_resource type="Animation" id="Animation_wy4nw"]
resource_name = "Jump_2"
length = 1.2667
loop_mode = 1
tracks/0/type = "position_3d"
tracks/0/imported = true
tracks/0/enabled = false
tracks/0/path = NodePath("Skeleton_01/Skeleton3D:Root")
tracks/0/interp = 0
tracks/0/loop_wrap = true
tracks/0/keys = PackedFloat32Array()
tracks/1/type = "position_3d"
tracks/1/imported = true
tracks/1/enabled = false
tracks/1/path = NodePath("Skeleton_01/Skeleton3D:Hips")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = PackedFloat32Array(0, 1, -0.0731411, 1.26425, -0.101048, 0.0333333, 1, -0.0762938, 1.26512, -0.100908, 0.0666667, 1, -0.0792626, 1.26552, -0.100774, 0.1, 1, -0.0820513, 1.26524, -0.100647, 0.133333, 1, -0.0846631, 1.26496, -0.10052, 0.166667, 1, -0.0870673, 1.26467, -0.100378, 0.2, 1, -0.0892946, 1.26439, -0.100225, 0.233333, 1, -0.0913433, 1.26369, -0.100058, 0.266667, 1, -0.0932111, 1.2627, -0.0998617, 0.3, 1, -0.0948446, 1.26148, -0.0996464, 0.333333, 1, -0.0961271, 1.25979, -0.0993627, 0.366667, 1, -0.0970173, 1.25757, -0.0989687, 0.4, 1, -0.0975801, 1.25494, -0.0983892, 0.433333, 1, -0.097832, 1.25203, -0.0976682, 0.466667, 1, -0.0978766, 1.24901, -0.0969312, 0.5, 1, -0.0979212, 1.24603, -0.0962714, 0.566667, 1, -0.0958946, 1.24088, -0.0955819, 0.6, 1, -0.0935341, 1.23908, -0.0956507, 0.633333, 1, -0.0906576, 1.23816, -0.0958777, 0.666667, 1, -0.0875191, 1.23762, -0.0963291, 0.7, 1, -0.0844196, 1.23726, -0.0969619, 0.733333, 1, -0.0816446, 1.23764, -0.0977157, 0.766667, 1, -0.0794523, 1.23803, -0.0985609, 0.8, 1, -0.0777751, 1.23842, -0.0994692, 0.833333, 1, -0.0762031, 1.2388, -0.100383, 0.866667, 1, -0.0746951, 1.23962, -0.101258, 0.9, 1, -0.0732786, 1.24121, -0.102073, 0.933333, 1, -0.0719821, 1.24302, -0.102814, 1.03333, 1, -0.0692118, 1.2494, -0.104355, 1.06667, 1, -0.0687341, 1.25164, -0.10443, 1.1, 1, -0.0685761, 1.25385, -0.104437, 1.13333, 1, -0.0688161, 1.25598, -0.104312, 1.16667, 1, -0.0696831, 1.258, -0.103912, 1.2, 1, -0.0709751, 1.26018, -0.103345, 1.23333, 1, -0.0722248, 1.26248, -0.102544, 1.2667, 1, -0.0729826, 1.26394, -0.101511)
tracks/2/type = "rotation_3d"
tracks/2/imported = true
tracks/2/enabled = true
tracks/2/path = NodePath("Skeleton_01/Skeleton3D:Hips")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = PackedFloat32Array(0, 1, -0.214301, -0.331054, 0.0588823, 0.917067, 0.0333333, 1, -0.224035, -0.328166, 0.0561403, 0.915949, 0.0666667, 1, -0.23211, -0.325662, 0.0539258, 0.914965, 0.1, 1, -0.238813, -0.323472, 0.0521249, 0.914121, 0.133333, 1, -0.244426, -0.321522, 0.0506332, 0.913409, 0.166667, 1, -0.24915, -0.31975, 0.049371, 0.912823, 0.2, 1, -0.253435, -0.318038, 0.0482041, 0.912304, 0.233333, 1, -0.257568, -0.316305, 0.0470468, 0.91181, 0.266667, 1, -0.26183, -0.314467, 0.0458164, 0.911294, 0.3, 1, -0.266094, -0.312425, 0.0445347, 0.910825, 0.333333, 1, -0.269905, -0.310236, 0.0431774, 0.910517, 0.366667, 1, -0.273132, -0.307998, 0.0417484, 0.910381, 0.4, 1, -0.275674, -0.305838, 0.0403624, 0.910405, 0.433333, 1, -0.276764, -0.303917, 0.0393494, 0.910762, 0.466667, 1, -0.2777, -0.302401, 0.0383803, 0.911023, 0.5, 1, -0.277599, -0.301475, 0.0377868, 0.911386, 0.533333, 1, -0.276253, -0.300817, 0.0377738, 0.912012, 0.566667, 1, -0.27377, -0.300717, 0.0379938, 0.912785, 0.6, 1, -0.270603, -0.300619, 0.0385237, 0.913739, 0.633333, 1, -0.266916, -0.30053, 0.0393126, 0.914818, 0.666667, 1, -0.262861, -0.300466, 0.0403463, 0.915968, 0.7, 1, -0.258531, -0.300825, 0.0413623, 0.917036, 0.733333, 1, -0.254153, -0.30139, 0.0425442, 0.91802, 0.766667, 1, -0.249976, -0.302098, 0.0436054, 0.918884, 0.8, 1, -0.246152, -0.30298, 0.0444868, 0.919583, 0.833333, 1, -0.242679, -0.304192, 0.0454489, 0.920059, 0.866667, 1, -0.239509, -0.305624, 0.0463859, 0.920368, 0.9, 1, -0.236605, -0.307245, 0.0473215, 0.920532, 0.933333, 1, -0.23393, -0.309023, 0.0482783, 0.920571, 1.1, 1, -0.222737, -0.319092, 0.0543035, 0.919576, 1.13333, 1, -0.220626, -0.321243, 0.0558325, 0.919244, 1.16667, 1, -0.218584, -0.32392, 0.0570958, 0.918715, 1.2, 1, -0.216621, -0.326862, 0.0580391, 0.918078, 1.23333, 1, -0.215359, -0.329356, 0.0585311, 0.917453, 1.26667, 1, -0.214525, -0.330771, 0.0588102, 0.917121)
tracks/3/type = "position_3d"
tracks/3/imported = true
tracks/3/enabled = true
tracks/3/path = NodePath("Skeleton_01/Skeleton3D:LeftUpLeg")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = PackedFloat32Array()
tracks/4/type = "rotation_3d"
tracks/4/imported = true
tracks/4/enabled = true
tracks/4/path = NodePath("Skeleton_01/Skeleton3D:LeftUpLeg")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = PackedFloat32Array(0, 1, 0.702759, 0.443967, 0.42249, 0.361283, 0.0333333, 1, 0.697665, 0.450916, 0.423067, 0.361874, 0.0666667, 1, 0.692047, 0.458174, 0.424617, 0.361729, 0.1, 1, 0.68619, 0.465473, 0.42672, 0.361093, 0.133333, 1, 0.680529, 0.47246, 0.428741, 0.360336, 0.166667, 1, 0.675148, 0.478983, 0.430711, 0.359497, 0.2, 1, 0.670112, 0.485053, 0.432548, 0.358575, 0.233333, 1, 0.665926, 0.490233, 0.433564, 0.358102, 0.266667, 1, 0.662729, 0.49436, 0.433648, 0.358257, 0.3, 1, 0.660387, 0.498051, 0.433378, 0.357796, 0.333333, 1, 0.657648, 0.50176, 0.434294, 0.356544, 0.366667, 1, 0.654872, 0.505058, 0.436142, 0.354738, 0.4, 1, 0.652885, 0.507106, 0.438835, 0.352148, 0.433333, 1, 0.651319, 0.508573, 0.44145, 0.349654, 0.466667, 1, 0.650406, 0.509031, 0.443497, 0.348095, 0.5, 1, 0.650072, 0.509026, 0.444687, 0.347206, 0.533333, 1, 0.650291, 0.508585, 0.445076, 0.346945, 0.566667, 1, 0.650997, 0.508078, 0.445068, 0.346373, 0.6, 1, 0.652377, 0.506514, 0.445483, 0.345532, 0.633333, 1, 0.654274, 0.504425, 0.445452, 0.345042, 0.666667, 1, 0.656706, 0.501834, 0.444832, 0.344998, 0.7, 1, 0.659297, 0.49893, 0.444351, 0.344888, 0.733333, 1, 0.66194, 0.495927, 0.443871, 0.344776, 0.766667, 1, 0.664967, 0.492524, 0.442941, 0.345025, 0.8, 1, 0.668357, 0.488765, 0.441518, 0.345643, 0.833333, 1, 0.672059, 0.484688, 0.439586, 0.346668, 0.866667, 1, 0.675557, 0.480712, 0.4379, 0.347538, 0.9, 1, 0.678731, 0.476956, 0.43657, 0.3482, 0.933333, 1, 0.681961, 0.473167, 0.435045, 0.348967, 0.966667, 1, 0.684772, 0.469253, 0.433836, 0.350251, 1, 1, 0.687412, 0.465543, 0.432681, 0.351457, 1.03333, 1, 0.689895, 0.462072, 0.43147, 0.352658, 1.06667, 1, 0.692143, 0.458875, 0.430328, 0.353821, 1.1, 1, 0.694246, 0.455826, 0.429272, 0.354925, 1.13333, 1, 0.696169, 0.452979, 0.428257, 0.356027, 1.16667, 1, 0.697752, 0.450563, 0.427409, 0.357011, 1.2, 1, 0.699098, 0.448444, 0.426688, 0.357907, 1.23333, 1, 0.699971, 0.447093, 0.426349, 0.358294, 1.26667, 1, 0.700427, 0.446163, 0.426132, 0.35882)
tracks/5/type = "position_3d"
tracks/5/imported = true
tracks/5/enabled = true
tracks/5/path = NodePath("Skeleton_01/Skeleton3D:LeftLeg")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = PackedFloat32Array()
tracks/6/type = "rotation_3d"
tracks/6/imported = true
tracks/6/enabled = true
tracks/6/path = NodePath("Skeleton_01/Skeleton3D:LeftLeg")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = PackedFloat32Array(0, 1, 0.043682, -0.0098565, 0.918206, 0.393564, 0.0333333, 1, 0.0406338, -0.00905339, 0.925663, 0.376052, 0.0666667, 1, 0.0358795, -0.00876976, 0.931399, 0.36212, 0.1, 1, 0.0305251, -0.00885857, 0.935763, 0.351193, 0.133333, 1, 0.0261026, -0.00917485, 0.939049, 0.342667, 0.166667, 1, 0.0222658, -0.00952468, 0.94151, 0.336113, 0.2, 1, 0.0186501, -0.0097055, 0.943566, 0.330517, 0.233333, 1, 0.0185022, -0.0101369, 0.945403, 0.325219, 0.266667, 1, 0.022563, -0.0108704, 0.947234, 0.319562, 0.3, 1, 0.0279792, -0.0116488, 0.94934, 0.312786, 0.333333, 1, 0.0304604, -0.0121107, 0.951564, 0.305699, 0.366667, 1, 0.0306172, -0.0121934, 0.953651, 0.299104, 0.4, 1, 0.0303584, -0.0119725, 0.955466, 0.293291, 0.433333, 1, 0.0291326, -0.0114973, 0.956887, 0.288768, 0.466667, 1, 0.0278643, -0.0112044, 0.957774, 0.285949, 0.5, 1, 0.0261817, -0.0108587, 0.958289, 0.284391, 0.533333, 1, 0.025053, -0.0106603, 0.958263, 0.284587, 0.566667, 1, 0.0253972, -0.010817, 0.958078, 0.285176, 0.6, 1, 0.0269773, -0.0112674, 0.957852, 0.285772, 0.633333, 1, 0.0292907, -0.0115809, 0.957598, 0.286381, 0.666667, 1, 0.0318265, -0.0115526, 0.957328, 0.287013, 0.7, 1, 0.0330412, -0.0113897, 0.956566, 0.289412, 0.733333, 1, 0.0330412, -0.0110804, 0.955706, 0.292253, 0.766667, 1, 0.0330226, -0.0105961, 0.954484, 0.296239, 0.8, 1, 0.0334442, -0.0100867, 0.952889, 0.301302, 0.833333, 1, 0.0341664, -0.00951925, 0.950864, 0.307569, 0.866667, 1, 0.0346625, -0.00929456, 0.948519, 0.314681, 0.9, 1, 0.0351111, -0.00953546, 0.945893, 0.322432, 0.933333, 1, 0.0358385, -0.00979764, 0.943022, 0.330649, 1.03333, 1, 0.0366826, -0.010583, 0.933665, 0.356106, 1.06667, 1, 0.0367737, -0.0108281, 0.930545, 0.364166, 1.1, 1, 0.037016, -0.0110592, 0.927592, 0.371592, 1.13333, 1, 0.037424, -0.0112727, 0.924873, 0.378261, 1.16667, 1, 0.0376263, -0.0114529, 0.922498, 0.383994, 1.2, 1, 0.0379702, -0.0116033, 0.920547, 0.388609, 1.23333, 1, 0.0383125, -0.0117021, 0.919284, 0.39155, 1.26667, 1, 0.0385053, -0.0117524, 0.918674, 0.39296)
tracks/7/type = "rotation_3d"
tracks/7/imported = true
tracks/7/enabled = true
tracks/7/path = NodePath("Skeleton_01/Skeleton3D:LeftFoot")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = PackedFloat32Array(0, 1, 0.461023, 0.427677, -0.569389, 0.529477, 0.0333333, 1, 0.463055, 0.422307, -0.571019, 0.530259, 0.0666667, 1, 0.464508, 0.417453, -0.572687, 0.531032, 0.1, 1, 0.465093, 0.413507, -0.574423, 0.531733, 0.133333, 1, 0.464687, 0.410683, -0.576574, 0.531947, 0.166667, 1, 0.463703, 0.408756, -0.578712, 0.531969, 0.2, 1, 0.462908, 0.407173, -0.580669, 0.531742, 0.233333, 1, 0.460935, 0.407048, -0.583527, 0.530422, 0.266667, 1, 0.458375, 0.40911, -0.586772, 0.527466, 0.3, 1, 0.455566, 0.411784, -0.590181, 0.524004, 0.333333, 1, 0.452977, 0.414345, -0.592596, 0.521497, 0.366667, 1, 0.451821, 0.417023, -0.593864, 0.518919, 0.4, 1, 0.451888, 0.419861, -0.594573, 0.515749, 0.433333, 1, 0.452842, 0.422056, -0.594679, 0.512991, 0.466667, 1, 0.453859, 0.423781, -0.594645, 0.510705, 0.5, 1, 0.454888, 0.424376, -0.593984, 0.510064, 0.533333, 1, 0.455786, 0.42457, -0.592997, 0.51025, 0.566667, 1, 0.455915, 0.425281, -0.592614, 0.509986, 0.633333, 1, 0.454215, 0.427275, -0.59337, 0.508957, 0.7, 1, 0.454027, 0.42797, -0.592965, 0.509012, 0.733333, 1, 0.454976, 0.427421, -0.59196, 0.509796, 0.766667, 1, 0.456275, 0.426565, -0.590683, 0.510831, 0.8, 1, 0.457635, 0.425652, -0.589354, 0.511911, 0.833333, 1, 0.459063, 0.424692, -0.587852, 0.513155, 0.866667, 1, 0.45991, 0.424369, -0.586052, 0.51472, 0.9, 1, 0.460228, 0.424587, -0.584265, 0.516285, 0.933333, 1, 0.46046, 0.424868, -0.58254, 0.517794, 0.966667, 1, 0.460721, 0.425127, -0.580646, 0.519474, 1, 1, 0.461002, 0.425355, -0.57873, 0.521174, 1.03333, 1, 0.461173, 0.425669, -0.576892, 0.5228, 1.06667, 1, 0.461433, 0.425873, -0.575159, 0.524313, 1.1, 1, 0.461486, 0.426089, -0.57367, 0.52572, 1.13333, 1, 0.461436, 0.426387, -0.572387, 0.526919, 1.16667, 1, 0.461333, 0.426728, -0.571144, 0.528081, 1.2, 1, 0.461407, 0.426862, -0.570385, 0.528728, 1.26667, 1, 0.461324, 0.427321, -0.569363, 0.529531)
tracks/8/type = "position_3d"
tracks/8/imported = true
tracks/8/enabled = true
tracks/8/path = NodePath("Skeleton_01/Skeleton3D:LeftToeBase")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = PackedFloat32Array()
tracks/9/type = "rotation_3d"
tracks/9/imported = true
tracks/9/enabled = true
tracks/9/path = NodePath("Skeleton_01/Skeleton3D:LeftToeBase")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = PackedFloat32Array(0, 1, 0.00503226, 0.9998, 0.0151207, 0.0120585, 0.0333333, 1, 0.00456518, 0.999806, 0.0159201, 0.0107121, 0.0666667, 1, 0.00419986, 0.999809, 0.0166225, 0.00940985, 0.1, 1, 0.00392066, 0.999811, 0.0172311, 0.00814534, 0.133333, 1, 0.00359141, 0.99981, 0.0177436, 0.00716356, 0.166667, 1, 0.00328885, 0.99981, 0.018149, 0.00626942, 0.266667, 1, 0.00247236, 0.999813, 0.0188112, 0.00362349, 0.333333, 1, 0.00107391, 0.999817, 0.0188236, 0.00332402, 0.4, 1, -0.000451976, 0.999821, 0.0187068, 0.00296531, 0.466667, 1, -0.00158448, 0.999823, 0.0185383, 0.00278902, 0.666667, 1, -0.00195598, 0.99983, 0.0179741, 0.00365966, 0.766667, 1, -0.0013871, 0.999834, 0.0176112, 0.00444306, 0.833333, 1, -0.000574308, 0.999837, 0.017289, 0.00516479, 0.9, 1, 0.000537714, 0.999839, 0.0168977, 0.00602312, 0.966667, 1, 0.00181211, 0.999838, 0.0164793, 0.00695472, 1.03333, 1, 0.0031073, 0.999835, 0.0160565, 0.00789477, 1.1, 1, 0.00409576, 0.999827, 0.015674, 0.00919401, 1.16667, 1, 0.0048051, 0.999816, 0.0153654, 0.0104205, 1.23333, 1, 0.00511648, 0.999806, 0.0151711, 0.0114652, 1.26667, 1, 0.00507663, 0.999802, 0.01513, 0.0118983)
tracks/10/type = "position_3d"
tracks/10/imported = true
tracks/10/enabled = true
tracks/10/path = NodePath("Skeleton_01/Skeleton3D:Spine")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = PackedFloat32Array()
tracks/11/type = "rotation_3d"
tracks/11/imported = true
tracks/11/enabled = true
tracks/11/path = NodePath("Skeleton_01/Skeleton3D:Spine")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = PackedFloat32Array(0, 1, 0.198236, 0.00648596, -0.0328207, 0.979583, 0.0333333, 1, 0.201098, 0.00631666, -0.0324027, 0.979015, 0.0666667, 1, 0.203389, 0.00617666, -0.0320353, 0.978554, 0.1, 1, 0.205204, 0.00605933, -0.0317077, 0.978187, 0.133333, 1, 0.206641, 0.00595819, -0.0314105, 0.977894, 0.166667, 1, 0.207766, 0.00586803, -0.0311352, 0.977665, 0.2, 1, 0.208733, 0.00578014, -0.0308659, 0.977468, 0.233333, 1, 0.209639, 0.00568946, -0.0305933, 0.977284, 0.266667, 1, 0.21058, 0.00559043, -0.0303065, 0.977091, 0.3, 1, 0.211663, 0.00547063, -0.0299892, 0.976867, 0.366667, 1, 0.213722, 0.00511792, -0.0292612, 0.976443, 0.433333, 1, 0.215143, 0.00472394, -0.0285265, 0.976155, 0.566667, 1, 0.215581, 0.0044478, -0.0276354, 0.976085, 0.733333, 1, 0.214498, 0.00456563, -0.0273593, 0.97633, 0.8, 1, 0.213087, 0.00466181, -0.0273762, 0.976638, 0.833333, 1, 0.212088, 0.00474892, -0.0275469, 0.976851, 0.866667, 1, 0.210952, 0.00485545, -0.0278234, 0.977088, 0.9, 1, 0.209711, 0.00497995, -0.0281879, 0.977344, 1.06667, 1, 0.202996, 0.0058018, -0.0307198, 0.97868, 1.1, 1, 0.201795, 0.00596821, -0.0312314, 0.978911, 1.13333, 1, 0.200714, 0.00612095, -0.031702, 0.979118, 1.16667, 1, 0.199784, 0.00625562, -0.032115, 0.979294, 1.23333, 1, 0.198556, 0.00644006, -0.0326823, 0.979523, 1.26667, 1, 0.198328, 0.00647721, -0.0327988, 0.979565)
tracks/12/type = "position_3d"
tracks/12/imported = true
tracks/12/enabled = true
tracks/12/path = NodePath("Skeleton_01/Skeleton3D:Spine1")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = PackedFloat32Array()
tracks/13/type = "rotation_3d"
tracks/13/imported = true
tracks/13/enabled = true
tracks/13/path = NodePath("Skeleton_01/Skeleton3D:Spine1")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = PackedFloat32Array()
tracks/14/type = "position_3d"
tracks/14/imported = true
tracks/14/enabled = true
tracks/14/path = NodePath("Skeleton_01/Skeleton3D:Neck")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = PackedFloat32Array()
tracks/15/type = "rotation_3d"
tracks/15/imported = true
tracks/15/enabled = true
tracks/15/path = NodePath("Skeleton_01/Skeleton3D:Neck")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = PackedFloat32Array(0, 1, 0.301597, 0.0700628, -0.149428, 0.939043, 0.0333333, 1, 0.303664, 0.0702859, -0.151441, 0.938037, 0.0666667, 1, 0.305766, 0.0705252, -0.153175, 0.937054, 0.1, 1, 0.307881, 0.070773, -0.154681, 0.936095, 0.133333, 1, 0.30999, 0.0710301, -0.156012, 0.935159, 0.166667, 1, 0.312067, 0.0713096, -0.157205, 0.934246, 0.2, 1, 0.314094, 0.0716214, -0.158341, 0.93335, 0.233333, 1, 0.316054, 0.0719829, -0.159471, 0.932468, 0.3, 1, 0.319694, 0.0729003, -0.161909, 0.930734, 0.333333, 1, 0.321455, 0.0730639, -0.163126, 0.929902, 0.366667, 1, 0.323178, 0.072657, -0.164139, 0.929158, 0.4, 1, 0.324786, 0.072069, -0.164981, 0.928494, 0.433333, 1, 0.326123, 0.0715781, -0.16563, 0.927948, 0.466667, 1, 0.32708, 0.0712206, -0.16603, 0.927567, 0.7, 1, 0.326348, 0.0708093, -0.165514, 0.927948, 0.733333, 1, 0.325628, 0.0707144, -0.165098, 0.928283, 0.766667, 1, 0.324681, 0.0706033, -0.164523, 0.928725, 0.8, 1, 0.323477, 0.0704715, -0.163787, 0.929285, 0.833333, 1, 0.321984, 0.0703254, -0.162865, 0.929976, 0.866667, 1, 0.320292, 0.0701795, -0.161809, 0.930755, 0.9, 1, 0.318446, 0.0700441, -0.160646, 0.9316, 0.933333, 1, 0.316491, 0.0699282, -0.1594, 0.932489, 1, 1, 0.31244, 0.069791, -0.156769, 0.934309, 1.03333, 1, 0.310449, 0.0697691, -0.155454, 0.935194, 1.06667, 1, 0.308539, 0.0697765, -0.154178, 0.936036, 1.1, 1, 0.306781, 0.0698129, -0.152992, 0.936806, 1.13333, 1, 0.305203, 0.0698658, -0.151917, 0.937492, 1.16667, 1, 0.303847, 0.0699273, -0.150987, 0.938078, 1.2, 1, 0.302756, 0.0699873, -0.150233, 0.938547, 1.26667, 1, 0.301728, 0.0700381, -0.149517, 0.938989)
tracks/16/type = "scale_3d"
tracks/16/imported = true
tracks/16/enabled = true
tracks/16/path = NodePath("Skeleton_01/Skeleton3D:Neck")
tracks/16/interp = 0
tracks/16/loop_wrap = true
tracks/16/keys = PackedFloat32Array()
tracks/17/type = "position_3d"
tracks/17/imported = true
tracks/17/enabled = true
tracks/17/path = NodePath("Skeleton_01/Skeleton3D:Head")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = PackedFloat32Array()
tracks/18/type = "rotation_3d"
tracks/18/imported = true
tracks/18/enabled = true
tracks/18/path = NodePath("Skeleton_01/Skeleton3D:Head")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = PackedFloat32Array(0, 1, -0.161071, 0.0732174, -0.0355586, 0.983581, 0.0333333, 1, -0.155896, 0.0704141, -0.0305068, 0.984788, 0.0666667, 1, -0.151715, 0.0682584, -0.0264364, 0.98571, 0.1, 1, -0.148355, 0.0666485, -0.0231651, 0.986414, 0.133333, 1, -0.145637, 0.0654979, -0.0205065, 0.986955, 0.166667, 1, -0.14342, 0.0647707, -0.0183046, 0.98737, 0.233333, 1, -0.139425, 0.0641346, -0.0142029, 0.988052, 0.266667, 1, -0.137261, 0.0640385, -0.0119082, 0.988391, 0.3, 1, -0.134649, 0.0639768, -0.0090752, 0.988784, 0.333333, 1, -0.131621, 0.0641796, -0.00560882, 0.989204, 0.366667, 1, -0.128422, 0.0646209, -0.00175927, 0.989611, 0.4, 1, -0.125325, 0.0652222, 0.00208389, 0.989967, 0.433333, 1, -0.12271, 0.0658892, 0.00540446, 0.990238, 0.466667, 1, -0.12082, 0.0665063, 0.00783743, 0.990413, 0.5, 1, -0.119925, 0.0669726, 0.00899845, 0.990481, 0.633333, 1, -0.120409, 0.0677838, 0.00848889, 0.990371, 0.666667, 1, -0.120943, 0.0679031, 0.00791305, 0.990303, 0.7, 1, -0.121776, 0.0680232, 0.00701558, 0.990199, 0.733333, 1, -0.12292, 0.0681586, 0.00578437, 0.990056, 0.766667, 1, -0.124422, 0.0683204, 0.00416744, 0.989866, 0.8, 1, -0.12633, 0.068529, 0.00211142, 0.989616, 0.833333, 1, -0.128697, 0.0687829, -0.000441008, 0.989296, 0.866667, 1, -0.131381, 0.069075, -0.00333698, 0.988917, 0.9, 1, -0.134309, 0.0694054, -0.00649981, 0.988485, 0.933333, 1, -0.137411, 0.0697702, -0.00985287, 0.988005, 1, 1, -0.143841, 0.0705872, -0.0168171, 0.986937, 1.03333, 1, -0.147003, 0.0710207, -0.0202477, 0.986375, 1.06667, 1, -0.150036, 0.0714564, -0.0235424, 0.985814, 1.1, 1, -0.15283, 0.071876, -0.0265791, 0.985277, 1.13333, 1, -0.155338, 0.0722629, -0.0293093, 0.984779, 1.16667, 1, -0.157493, 0.0726139, -0.0316572, 0.984338, 1.2, 1, -0.159229, 0.0728932, -0.033549, 0.983975, 1.23333, 1, -0.160383, 0.0730965, -0.0348078, 0.983729, 1.26667, 1, -0.160962, 0.073192, -0.03544, 0.983605)
tracks/19/type = "scale_3d"
tracks/19/imported = true
tracks/19/enabled = true
tracks/19/path = NodePath("Skeleton_01/Skeleton3D:Head")
tracks/19/interp = 0
tracks/19/loop_wrap = true
tracks/19/keys = PackedFloat32Array()
tracks/20/type = "position_3d"
tracks/20/imported = true
tracks/20/enabled = true
tracks/20/path = NodePath("Skeleton_01/Skeleton3D:LeftShoulder")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = PackedFloat32Array()
tracks/21/type = "rotation_3d"
tracks/21/imported = true
tracks/21/enabled = true
tracks/21/path = NodePath("Skeleton_01/Skeleton3D:LeftShoulder")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = PackedFloat32Array()
tracks/22/type = "position_3d"
tracks/22/imported = true
tracks/22/enabled = true
tracks/22/path = NodePath("Skeleton_01/Skeleton3D:LeftArm")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = PackedFloat32Array()
tracks/23/type = "rotation_3d"
tracks/23/imported = true
tracks/23/enabled = true
tracks/23/path = NodePath("Skeleton_01/Skeleton3D:LeftArm")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = PackedFloat32Array(0, 1, -0.0462848, -0.062983, 0.376154, 0.923254, 0.0333333, 1, -0.0395201, -0.0573204, 0.356638, 0.931645, 0.0666667, 1, -0.033937, -0.0525586, 0.340824, 0.938043, 0.1, 1, -0.0292662, -0.0485462, 0.328057, 0.942956, 0.133333, 1, -0.0252428, -0.0451404, 0.317639, 0.9468, 0.166667, 1, -0.0216327, -0.042249, 0.309007, 0.949875, 0.233333, 1, -0.014317, -0.0372335, 0.293035, 0.955269, 0.266667, 1, -0.0101145, -0.0348424, 0.284191, 0.958081, 0.3, 1, -0.00495214, -0.032342, 0.273273, 0.96138, 0.333333, 1, 0.00197588, -0.0300545, 0.259453, 0.965286, 0.366667, 1, 0.0103545, -0.0280574, 0.243541, 0.96943, 0.4, 1, 0.0191313, -0.0262792, 0.227236, 0.973297, 0.433333, 1, 0.0270102, -0.0249512, 0.212839, 0.976395, 0.466667, 1, 0.0329229, -0.0241255, 0.20214, 0.978506, 0.5, 1, 0.0358012, -0.023797, 0.196972, 0.979466, 0.6, 1, 0.0356618, -0.0240349, 0.197499, 0.979359, 0.633333, 1, 0.0350291, -0.0243507, 0.198977, 0.979075, 0.666667, 1, 0.0340102, -0.0248655, 0.201354, 0.978612, 0.7, 1, 0.0324187, -0.0256837, 0.205064, 0.977874, 0.733333, 1, 0.0302272, -0.0268053, 0.210153, 0.976833, 0.766667, 1, 0.0273413, -0.0282766, 0.216827, 0.975417, 0.8, 1, 0.0236554, -0.0301474, 0.225302, 0.973535, 0.833333, 1, 0.0190465, -0.0324491, 0.2358, 0.971073, 0.866667, 1, 0.0137882, -0.0350535, 0.247676, 0.96811, 0.9, 1, 0.0080136, -0.0378908, 0.260599, 0.96467, 0.933333, 1, 0.00185818, -0.0408896, 0.274239, 0.96079, 1, 1, -0.0110573, -0.0470097, 0.30235, 0.951973, 1.03333, 1, -0.0174751, -0.0499436, 0.316075, 0.947257, 1.06667, 1, -0.0236266, -0.0528578, 0.329171, 0.942494, 1.1, 1, -0.0293977, -0.0552668, 0.341177, 0.937912, 1.13333, 1, -0.0345506, -0.0575582, 0.351894, 0.93363, 1.16667, 1, -0.038954, -0.0596156, 0.361052, 0.929823, 1.2, 1, -0.0424832, -0.0613188, 0.368387, 0.926675, 1.23333, 1, -0.0448421, -0.0624225, 0.373253, 0.92454, 1.26667, 1, -0.0460475, -0.0629182, 0.375694, 0.923458)
tracks/24/type = "position_3d"
tracks/24/imported = true
tracks/24/enabled = true
tracks/24/path = NodePath("Skeleton_01/Skeleton3D:LeftForeArm")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = PackedFloat32Array()
tracks/25/type = "rotation_3d"
tracks/25/imported = true
tracks/25/enabled = true
tracks/25/path = NodePath("Skeleton_01/Skeleton3D:LeftForeArm")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = PackedFloat32Array(0, 1, 0.0401569, 0.644167, -0.265824, 0.716082, 0.0333333, 1, 0.0293914, 0.647635, -0.254708, 0.717515, 0.0666667, 1, 0.0209656, 0.650367, -0.24572, 0.718474, 0.1, 1, 0.0146457, 0.652558, -0.238778, 0.718985, 0.133333, 1, 0.00945413, 0.654492, -0.232811, 0.71927, 0.166667, 1, 0.00592706, 0.656264, -0.22852, 0.71907, 0.2, 1, 0.00256959, 0.658155, -0.224261, 0.718701, 0.233333, 1, -0.00031164, 0.660258, -0.220407, 0.71797, 0.266667, 1, -0.00341623, 0.662663, -0.216371, 0.716972, 0.3, 1, -0.00707575, 0.665782, -0.211563, 0.715489, 0.333333, 1, -0.0107143, 0.670242, -0.206373, 0.712791, 0.366667, 1, -0.0138434, 0.675859, -0.201239, 0.70889, 0.4, 1, -0.0163981, 0.681841, -0.196427, 0.704444, 0.433333, 1, -0.0182852, 0.687265, -0.192487, 0.700201, 0.466667, 1, -0.0196516, 0.691276, -0.189797, 0.696942, 0.5, 1, -0.0201063, 0.693275, -0.188382, 0.695325, 0.6, 1, -0.0206334, 0.693068, -0.188782, 0.695408, 0.633333, 1, -0.0200025, 0.692733, -0.189781, 0.695488, 0.666667, 1, -0.0191052, 0.692165, -0.19097, 0.695754, 0.7, 1, -0.0177886, 0.691324, -0.19248, 0.696208, 0.733333, 1, -0.0158904, 0.690195, -0.19446, 0.696824, 0.766667, 1, -0.013678, 0.688608, -0.197333, 0.697632, 0.8, 1, -0.0109236, 0.686546, -0.201036, 0.698656, 0.833333, 1, -0.00760593, 0.683911, -0.205714, 0.699921, 0.866667, 1, -0.00374831, 0.680905, -0.210906, 0.701337, 0.9, 1, 0.000474404, 0.677559, -0.216539, 0.702868, 0.933333, 1, 0.00497639, 0.673948, -0.222451, 0.704475, 1, 1, 0.0143658, 0.666243, -0.234568, 0.707737, 1.03333, 1, 0.0190323, 0.662345, -0.240436, 0.709315, 1.06667, 1, 0.0235402, 0.658539, -0.24601, 0.710811, 1.1, 1, 0.0277216, 0.65499, -0.251084, 0.712163, 1.13333, 1, 0.0314997, 0.651758, -0.255599, 0.713364, 1.16667, 1, 0.0347822, 0.64896, -0.259429, 0.714379, 1.2, 1, 0.0373562, 0.646625, -0.262567, 0.715219, 1.23333, 1, 0.0394029, 0.645232, -0.264518, 0.715648, 1.26667, 1, 0.040279, 0.644371, -0.265735, 0.715925)
tracks/26/type = "rotation_3d"
tracks/26/imported = true
tracks/26/enabled = true
tracks/26/path = NodePath("Skeleton_01/Skeleton3D:LeftHand")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = PackedFloat32Array(0, 1, 0.107554, -0.0497513, 0.294738, 0.948202, 0.0333333, 1, 0.109514, -0.0431884, 0.290857, 0.949497, 0.0666667, 1, 0.110704, -0.0383865, 0.288163, 0.950386, 0.1, 1, 0.111429, -0.0350245, 0.286505, 0.950932, 0.133333, 1, 0.111873, -0.0327274, 0.285731, 0.951195, 0.2, 1, 0.112232, -0.029657, 0.286363, 0.951063, 0.233333, 1, 0.112282, -0.0278775, 0.287323, 0.950821, 0.266667, 1, 0.112226, -0.025364, 0.288506, 0.95054, 0.3, 1, 0.11174, -0.0213969, 0.289957, 0.950253, 0.333333, 1, 0.109412, -0.0158222, 0.292869, 0.94974, 0.366667, 1, 0.104923, -0.00949724, 0.297391, 0.948925, 0.4, 1, 0.0991047, -0.00357255, 0.302677, 0.94792, 0.433333, 1, 0.0931611, 0.000896476, 0.30773, 0.946902, 0.466667, 1, 0.0884264, 0.00375802, 0.311638, 0.94607, 0.5, 1, 0.0860477, 0.00499018, 0.313576, 0.945643, 0.766667, 1, 0.0875217, 0.00544733, 0.313596, 0.945499, 0.8, 1, 0.0884315, 0.0052067, 0.31337, 0.94549, 0.833333, 1, 0.0900216, 0.00387729, 0.312715, 0.945564, 0.866667, 1, 0.0920818, 0.00161711, 0.311724, 0.945699, 0.9, 1, 0.0944549, -0.00150292, 0.310452, 0.945884, 0.933333, 1, 0.0969749, -0.00540133, 0.308955, 0.946105, 0.966667, 1, 0.0994573, -0.0100667, 0.307266, 0.946359, 1, 1, 0.101741, -0.0152361, 0.305479, 0.946625, 1.06667, 1, 0.105304, -0.0263992, 0.30183, 0.94716, 1.1, 1, 0.10641, -0.0319445, 0.3001, 0.947416, 1.13333, 1, 0.107119, -0.0371399, 0.29851, 0.947649, 1.16667, 1, 0.107492, -0.041759, 0.297116, 0.947852, 1.2, 1, 0.107619, -0.0455785, 0.295975, 0.948019, 1.23333, 1, 0.107542, -0.0481773, 0.295197, 0.948142, 1.26667, 1, 0.107539, -0.0495002, 0.294809, 0.948195)
tracks/27/type = "position_3d"
tracks/27/imported = true
tracks/27/enabled = true
tracks/27/path = NodePath("Skeleton_01/Skeleton3D:LeftHandIndex1")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = PackedFloat32Array()
tracks/28/type = "rotation_3d"
tracks/28/imported = true
tracks/28/enabled = true
tracks/28/path = NodePath("Skeleton_01/Skeleton3D:LeftHandIndex1")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = PackedFloat32Array()
tracks/29/type = "position_3d"
tracks/29/imported = true
tracks/29/enabled = true
tracks/29/path = NodePath("Skeleton_01/Skeleton3D:LeftHandIndex2")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = PackedFloat32Array()
tracks/30/type = "rotation_3d"
tracks/30/imported = true
tracks/30/enabled = true
tracks/30/path = NodePath("Skeleton_01/Skeleton3D:LeftHandIndex2")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = PackedFloat32Array()
tracks/31/type = "position_3d"
tracks/31/imported = true
tracks/31/enabled = true
tracks/31/path = NodePath("Skeleton_01/Skeleton3D:LeftHandMiddle1")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = PackedFloat32Array()
tracks/32/type = "rotation_3d"
tracks/32/imported = true
tracks/32/enabled = true
tracks/32/path = NodePath("Skeleton_01/Skeleton3D:LeftHandMiddle1")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = PackedFloat32Array()
tracks/33/type = "position_3d"
tracks/33/imported = true
tracks/33/enabled = true
tracks/33/path = NodePath("Skeleton_01/Skeleton3D:LeftHandMiddle2")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/keys = PackedFloat32Array()
tracks/34/type = "rotation_3d"
tracks/34/imported = true
tracks/34/enabled = true
tracks/34/path = NodePath("Skeleton_01/Skeleton3D:LeftHandMiddle2")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/keys = PackedFloat32Array()
tracks/35/type = "position_3d"
tracks/35/imported = true
tracks/35/enabled = true
tracks/35/path = NodePath("Skeleton_01/Skeleton3D:LeftHandPinky1")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/keys = PackedFloat32Array()
tracks/36/type = "rotation_3d"
tracks/36/imported = true
tracks/36/enabled = true
tracks/36/path = NodePath("Skeleton_01/Skeleton3D:LeftHandPinky1")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/keys = PackedFloat32Array()
tracks/37/type = "rotation_3d"
tracks/37/imported = true
tracks/37/enabled = true
tracks/37/path = NodePath("Skeleton_01/Skeleton3D:LeftHandPinky2")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/keys = PackedFloat32Array()
tracks/38/type = "position_3d"
tracks/38/imported = true
tracks/38/enabled = true
tracks/38/path = NodePath("Skeleton_01/Skeleton3D:LeftHandProp")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/keys = PackedFloat32Array()
tracks/39/type = "rotation_3d"
tracks/39/imported = true
tracks/39/enabled = true
tracks/39/path = NodePath("Skeleton_01/Skeleton3D:LeftHandProp")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/keys = PackedFloat32Array()
tracks/40/type = "position_3d"
tracks/40/imported = true
tracks/40/enabled = true
tracks/40/path = NodePath("Skeleton_01/Skeleton3D:LeftHandRing1")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/keys = PackedFloat32Array()
tracks/41/type = "rotation_3d"
tracks/41/imported = true
tracks/41/enabled = true
tracks/41/path = NodePath("Skeleton_01/Skeleton3D:LeftHandRing1")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/keys = PackedFloat32Array()
tracks/42/type = "position_3d"
tracks/42/imported = true
tracks/42/enabled = true
tracks/42/path = NodePath("Skeleton_01/Skeleton3D:LeftHandRing2")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/keys = PackedFloat32Array()
tracks/43/type = "rotation_3d"
tracks/43/imported = true
tracks/43/enabled = true
tracks/43/path = NodePath("Skeleton_01/Skeleton3D:LeftHandRing2")
tracks/43/interp = 1
tracks/43/loop_wrap = true
tracks/43/keys = PackedFloat32Array()
tracks/44/type = "position_3d"
tracks/44/imported = true
tracks/44/enabled = true
tracks/44/path = NodePath("Skeleton_01/Skeleton3D:LeftHandThumb1")
tracks/44/interp = 1
tracks/44/loop_wrap = true
tracks/44/keys = PackedFloat32Array()
tracks/45/type = "rotation_3d"
tracks/45/imported = true
tracks/45/enabled = true
tracks/45/path = NodePath("Skeleton_01/Skeleton3D:LeftHandThumb1")
tracks/45/interp = 1
tracks/45/loop_wrap = true
tracks/45/keys = PackedFloat32Array()
tracks/46/type = "position_3d"
tracks/46/imported = true
tracks/46/enabled = true
tracks/46/path = NodePath("Skeleton_01/Skeleton3D:LeftHandThumb2")
tracks/46/interp = 1
tracks/46/loop_wrap = true
tracks/46/keys = PackedFloat32Array()
tracks/47/type = "rotation_3d"
tracks/47/imported = true
tracks/47/enabled = true
tracks/47/path = NodePath("Skeleton_01/Skeleton3D:LeftHandThumb2")
tracks/47/interp = 1
tracks/47/loop_wrap = true
tracks/47/keys = PackedFloat32Array()
tracks/48/type = "position_3d"
tracks/48/imported = true
tracks/48/enabled = true
tracks/48/path = NodePath("Skeleton_01/Skeleton3D:RightShoulder")
tracks/48/interp = 1
tracks/48/loop_wrap = true
tracks/48/keys = PackedFloat32Array()
tracks/49/type = "rotation_3d"
tracks/49/imported = true
tracks/49/enabled = true
tracks/49/path = NodePath("Skeleton_01/Skeleton3D:RightShoulder")
tracks/49/interp = 1
tracks/49/loop_wrap = true
tracks/49/keys = PackedFloat32Array()
tracks/50/type = "position_3d"
tracks/50/imported = true
tracks/50/enabled = true
tracks/50/path = NodePath("Skeleton_01/Skeleton3D:RightArm")
tracks/50/interp = 1
tracks/50/loop_wrap = true
tracks/50/keys = PackedFloat32Array()
tracks/51/type = "rotation_3d"
tracks/51/imported = true
tracks/51/enabled = true
tracks/51/path = NodePath("Skeleton_01/Skeleton3D:RightArm")
tracks/51/interp = 1
tracks/51/loop_wrap = true
tracks/51/keys = PackedFloat32Array(0, 1, 0.189305, 0.0545081, -0.338254, 0.920205, 0.0333333, 1, 0.192087, 0.045161, -0.335233, 0.921239, 0.0666667, 1, 0.194315, 0.0374018, -0.332713, 0.922033, 0.1, 1, 0.19611, 0.0309581, -0.330589, 0.922656, 0.133333, 1, 0.197579, 0.0255593, -0.328752, 0.923164, 0.166667, 1, 0.198793, 0.0210165, -0.327107, 0.923602, 0.2, 1, 0.199888, 0.0168985, -0.325494, 0.92402, 0.233333, 1, 0.200941, 0.0129346, -0.323802, 0.92445, 0.266667, 1, 0.202024, 0.00885347, -0.32192, 0.924919, 0.3, 1, 0.203321, 0.00439116, -0.319625, 0.925463, 0.333333, 1, 0.205423, 0.000182552, -0.316635, 0.926037, 0.366667, 1, 0.208313, -0.00335832, -0.313083, 0.926592, 0.4, 1, 0.211519, -0.00623353, -0.309382, 0.927094, 0.433333, 1, 0.214485, -0.00831551, -0.306074, 0.927494, 0.466667, 1, 0.216743, -0.00967664, -0.303597, 0.92777, 0.5, 1, 0.217906, -0.010069, -0.302434, 0.927873, 0.666667, 1, 0.217338, -0.00858438, -0.303233, 0.927761, 0.7, 1, 0.216853, -0.00727883, -0.303963, 0.927647, 0.733333, 1, 0.216181, -0.00548657, -0.304964, 0.927487, 0.766667, 1, 0.215287, -0.00313361, -0.306279, 0.927273, 0.8, 1, 0.214135, -0.000141727, -0.307948, 0.926992, 0.833333, 1, 0.212669, 0.00357077, -0.31002, 0.926632, 0.866667, 1, 0.210972, 0.00778135, -0.312367, 0.926206, 0.9, 1, 0.209073, 0.0123786, -0.314925, 0.92572, 0.933333, 1, 0.207009, 0.0172509, -0.317632, 0.925181, 1, 1, 0.202534, 0.027357, -0.323245, 0.923983, 1.03333, 1, 0.200245, 0.0323321, -0.326001, 0.923352, 1.06667, 1, 0.197994, 0.0371094, -0.328643, 0.922721, 1.1, 1, 0.195864, 0.041509, -0.331078, 0.922118, 1.13333, 1, 0.193912, 0.045462, -0.333263, 0.921557, 1.16667, 1, 0.192205, 0.0488617, -0.33514, 0.92106, 1.2, 1, 0.19081, 0.0515994, -0.336649, 0.92065, 1.23333, 1, 0.18987, 0.0534217, -0.337655, 0.920372, 1.26667, 1, 0.189395, 0.0543362, -0.338159, 0.920231)
tracks/52/type = "position_3d"
tracks/52/imported = true
tracks/52/enabled = true
tracks/52/path = NodePath("Skeleton_01/Skeleton3D:RightForeArm")
tracks/52/interp = 1
tracks/52/loop_wrap = true
tracks/52/keys = PackedFloat32Array()
tracks/53/type = "rotation_3d"
tracks/53/imported = true
tracks/53/enabled = true
tracks/53/path = NodePath("Skeleton_01/Skeleton3D:RightForeArm")
tracks/53/interp = 1
tracks/53/loop_wrap = true
tracks/53/keys = PackedFloat32Array(0, 1, -0.102316, -0.695302, 0.0425969, 0.710121, 0.0333333, 1, -0.103003, -0.70319, 0.0366517, 0.702546, 0.0666667, 1, -0.103545, -0.709215, 0.0319377, 0.696615, 0.1, 1, -0.103934, -0.713785, 0.0283246, 0.692031, 0.133333, 1, -0.104145, -0.717278, 0.0256352, 0.688483, 0.166667, 1, -0.104203, -0.719991, 0.0238643, 0.6857, 0.2, 1, -0.103794, -0.722401, 0.0223254, 0.683276, 0.233333, 1, -0.10309, -0.724795, 0.0210147, 0.680884, 0.266667, 1, -0.102077, -0.727508, 0.0197326, 0.678177, 0.3, 1, -0.100377, -0.730987, 0.0179667, 0.674729, 0.333333, 1, -0.0979086, -0.735194, 0.0159853, 0.670559, 0.366667, 1, -0.0946886, -0.739779, 0.013897, 0.666009, 0.4, 1, -0.0906135, -0.743722, 0.0113118, 0.662223, 0.433333, 1, -0.0872434, -0.747362, 0.00962087, 0.658594, 0.466667, 1, -0.0844747, -0.749861, 0.00842548, 0.656126, 0.5, 1, -0.0829145, -0.751193, 0.00815049, 0.654804, 0.6, 1, -0.0816998, -0.750621, 0.00901012, 0.655601, 0.666667, 1, -0.081913, -0.749748, 0.0100569, 0.656557, 0.7, 1, -0.0823744, -0.748976, 0.0109497, 0.657366, 0.733333, 1, -0.0828745, -0.747495, 0.0119481, 0.658969, 0.766667, 1, -0.0835701, -0.745599, 0.0132704, 0.661001, 0.8, 1, -0.0844281, -0.74313, 0.0148956, 0.663633, 0.833333, 1, -0.0856084, -0.740073, 0.0169638, 0.66684, 0.866667, 1, -0.0869566, -0.736566, 0.0192624, 0.670476, 0.9, 1, -0.0887004, -0.732808, 0.0220041, 0.67427, 0.966667, 1, -0.09149, -0.724151, 0.0267249, 0.683024, 1, 1, -0.0931566, -0.719751, 0.0293105, 0.68733, 1.03333, 1, -0.0947849, -0.715357, 0.031795, 0.69157, 1.06667, 1, -0.0963445, -0.711081, 0.0341361, 0.69564, 1.1, 1, -0.0978536, -0.707157, 0.0363257, 0.69931, 1.13333, 1, -0.0992035, -0.703586, 0.0382579, 0.702611, 1.16667, 1, -0.100364, -0.700487, 0.0398969, 0.705445, 1.2, 1, -0.101301, -0.697976, 0.0412057, 0.707722, 1.23333, 1, -0.101883, -0.696346, 0.0420233, 0.709194, 1.26667, 1, -0.102147, -0.695547, 0.0424032, 0.709917)
tracks/54/type = "rotation_3d"
tracks/54/imported = true
tracks/54/enabled = true
tracks/54/path = NodePath("Skeleton_01/Skeleton3D:RightHand")
tracks/54/interp = 1
tracks/54/loop_wrap = true
tracks/54/keys = PackedFloat32Array(0, 1, 0.0251612, 0.0162702, -0.296692, 0.954503, 0.0333333, 1, 0.0268823, 0.0222006, -0.301409, 0.952857, 0.0666667, 1, 0.022301, 0.0278057, -0.305348, 0.951573, 0.1, 1, 0.0132864, 0.0299117, -0.308592, 0.950631, 0.133333, 1, 0.00254851, 0.0271844, -0.31123, 0.949942, 0.166667, 1, -0.00656625, 0.0189242, -0.313261, 0.949456, 0.2, 1, -0.0114488, 0.00716735, -0.314887, 0.949033, 0.233333, 1, -0.00996295, -0.00599489, -0.3162, 0.948621, 0.266667, 1, -0.000762098, -0.01729, -0.317177, 0.948209, 0.3, 1, 0.0163548, -0.0191406, -0.318128, 0.947714, 0.333333, 1, 0.0314198, -0.00195179, -0.318186, 0.947505, 0.366667, 1, 0.0240053, 0.0234349, -0.317351, 0.947715, 0.4, 1, -0.0048798, 0.0263366, -0.315744, 0.948466, 0.433333, 1, -0.0208277, -0.00605522, -0.313919, 0.949202, 0.466667, 1, -0.0123656, -0.0407457, -0.31224, 0.949048, 0.5, 1, -0.000261458, -0.057594, -0.311184, 0.948603, 0.533333, 1, 0.00147565, -0.0594675, -0.311097, 0.948515, 0.566667, 1, 0.000667494, -0.0592221, -0.311133, 0.948519, 0.6, 1, -0.00135364, -0.0585692, -0.311119, 0.948564, 0.633333, 1, -0.00507653, -0.0572295, -0.311027, 0.948663, 0.666667, 1, -0.0108952, -0.0547727, -0.310812, 0.948829, 0.7, 1, -0.0193939, -0.0500931, -0.310434, 0.949076, 0.733333, 1, -0.0299172, -0.0423516, -0.310061, 0.949302, 0.766667, 1, -0.0414926, -0.0303077, -0.30954, 0.949497, 0.8, 1, -0.0522064, -0.0127736, -0.308825, 0.949599, 0.833333, 1, -0.0576358, 0.0107508, -0.307949, 0.949595, 0.866667, 1, -0.054953, 0.0363955, -0.306895, 0.949458, 0.9, 1, -0.0429906, 0.0598715, -0.305757, 0.949252, 0.933333, 1, -0.0231542, 0.0766976, -0.304617, 0.9491, 0.966667, 1, 0.000678609, 0.0819112, -0.303481, 0.94931, 1, 1, 0.0223332, 0.07719, -0.302404, 0.949787, 1.03333, 1, 0.0379689, 0.0653047, -0.301355, 0.950515, 1.06667, 1, 0.0458978, 0.0503048, -0.300356, 0.951393, 1.1, 1, 0.0459192, 0.0366136, -0.299462, 0.952299, 1.13333, 1, 0.0418349, 0.0264955, -0.298659, 0.953074, 1.16667, 1, 0.0361821, 0.0203043, -0.297945, 0.953681, 1.2, 1, 0.0308663, 0.0172983, -0.29734, 0.954116, 1.23333, 1, 0.0272564, 0.0164435, -0.29694, 0.954365, 1.26667, 1, 0.0254882, 0.0162798, -0.296735, 0.954481)
tracks/55/type = "position_3d"
tracks/55/imported = true
tracks/55/enabled = true
tracks/55/path = NodePath("Skeleton_01/Skeleton3D:RightHandIndex1")
tracks/55/interp = 1
tracks/55/loop_wrap = true
tracks/55/keys = PackedFloat32Array()
tracks/56/type = "rotation_3d"
tracks/56/imported = true
tracks/56/enabled = true
tracks/56/path = NodePath("Skeleton_01/Skeleton3D:RightHandIndex1")
tracks/56/interp = 1
tracks/56/loop_wrap = true
tracks/56/keys = PackedFloat32Array()
tracks/57/type = "position_3d"
tracks/57/imported = true
tracks/57/enabled = true
tracks/57/path = NodePath("Skeleton_01/Skeleton3D:RightHandIndex2")
tracks/57/interp = 1
tracks/57/loop_wrap = true
tracks/57/keys = PackedFloat32Array()
tracks/58/type = "rotation_3d"
tracks/58/imported = true
tracks/58/enabled = true
tracks/58/path = NodePath("Skeleton_01/Skeleton3D:RightHandIndex2")
tracks/58/interp = 1
tracks/58/loop_wrap = true
tracks/58/keys = PackedFloat32Array()
tracks/59/type = "position_3d"
tracks/59/imported = true
tracks/59/enabled = true
tracks/59/path = NodePath("Skeleton_01/Skeleton3D:RightHandMiddle1")
tracks/59/interp = 1
tracks/59/loop_wrap = true
tracks/59/keys = PackedFloat32Array()
tracks/60/type = "rotation_3d"
tracks/60/imported = true
tracks/60/enabled = true
tracks/60/path = NodePath("Skeleton_01/Skeleton3D:RightHandMiddle1")
tracks/60/interp = 1
tracks/60/loop_wrap = true
tracks/60/keys = PackedFloat32Array()
tracks/61/type = "position_3d"
tracks/61/imported = true
tracks/61/enabled = true
tracks/61/path = NodePath("Skeleton_01/Skeleton3D:RightHandMiddle2")
tracks/61/interp = 1
tracks/61/loop_wrap = true
tracks/61/keys = PackedFloat32Array()
tracks/62/type = "rotation_3d"
tracks/62/imported = true
tracks/62/enabled = true
tracks/62/path = NodePath("Skeleton_01/Skeleton3D:RightHandMiddle2")
tracks/62/interp = 1
tracks/62/loop_wrap = true
tracks/62/keys = PackedFloat32Array()
tracks/63/type = "position_3d"
tracks/63/imported = true
tracks/63/enabled = true
tracks/63/path = NodePath("Skeleton_01/Skeleton3D:RightHandPinky1")
tracks/63/interp = 1
tracks/63/loop_wrap = true
tracks/63/keys = PackedFloat32Array()
tracks/64/type = "rotation_3d"
tracks/64/imported = true
tracks/64/enabled = true
tracks/64/path = NodePath("Skeleton_01/Skeleton3D:RightHandPinky1")
tracks/64/interp = 1
tracks/64/loop_wrap = true
tracks/64/keys = PackedFloat32Array()
tracks/65/type = "rotation_3d"
tracks/65/imported = true
tracks/65/enabled = true
tracks/65/path = NodePath("Skeleton_01/Skeleton3D:RightHandPinky2")
tracks/65/interp = 1
tracks/65/loop_wrap = true
tracks/65/keys = PackedFloat32Array()
tracks/66/type = "position_3d"
tracks/66/imported = true
tracks/66/enabled = true
tracks/66/path = NodePath("Skeleton_01/Skeleton3D:RightHandProp")
tracks/66/interp = 1
tracks/66/loop_wrap = true
tracks/66/keys = PackedFloat32Array()
tracks/67/type = "position_3d"
tracks/67/imported = true
tracks/67/enabled = true
tracks/67/path = NodePath("Skeleton_01/Skeleton3D:RightHandRing1")
tracks/67/interp = 1
tracks/67/loop_wrap = true
tracks/67/keys = PackedFloat32Array()
tracks/68/type = "rotation_3d"
tracks/68/imported = true
tracks/68/enabled = true
tracks/68/path = NodePath("Skeleton_01/Skeleton3D:RightHandRing1")
tracks/68/interp = 1
tracks/68/loop_wrap = true
tracks/68/keys = PackedFloat32Array()
tracks/69/type = "position_3d"
tracks/69/imported = true
tracks/69/enabled = true
tracks/69/path = NodePath("Skeleton_01/Skeleton3D:RightHandRing2")
tracks/69/interp = 1
tracks/69/loop_wrap = true
tracks/69/keys = PackedFloat32Array()
tracks/70/type = "rotation_3d"
tracks/70/imported = true
tracks/70/enabled = true
tracks/70/path = NodePath("Skeleton_01/Skeleton3D:RightHandRing2")
tracks/70/interp = 1
tracks/70/loop_wrap = true
tracks/70/keys = PackedFloat32Array()
tracks/71/type = "position_3d"
tracks/71/imported = true
tracks/71/enabled = true
tracks/71/path = NodePath("Skeleton_01/Skeleton3D:RightHandThumb1")
tracks/71/interp = 1
tracks/71/loop_wrap = true
tracks/71/keys = PackedFloat32Array()
tracks/72/type = "rotation_3d"
tracks/72/imported = true
tracks/72/enabled = true
tracks/72/path = NodePath("Skeleton_01/Skeleton3D:RightHandThumb1")
tracks/72/interp = 1
tracks/72/loop_wrap = true
tracks/72/keys = PackedFloat32Array()
tracks/73/type = "position_3d"
tracks/73/imported = true
tracks/73/enabled = true
tracks/73/path = NodePath("Skeleton_01/Skeleton3D:RightHandThumb2")
tracks/73/interp = 1
tracks/73/loop_wrap = true
tracks/73/keys = PackedFloat32Array()
tracks/74/type = "rotation_3d"
tracks/74/imported = true
tracks/74/enabled = true
tracks/74/path = NodePath("Skeleton_01/Skeleton3D:RightHandThumb2")
tracks/74/interp = 1
tracks/74/loop_wrap = true
tracks/74/keys = PackedFloat32Array()
tracks/75/type = "position_3d"
tracks/75/imported = true
tracks/75/enabled = true
tracks/75/path = NodePath("Skeleton_01/Skeleton3D:RightUpLeg")
tracks/75/interp = 1
tracks/75/loop_wrap = true
tracks/75/keys = PackedFloat32Array()
tracks/76/type = "rotation_3d"
tracks/76/imported = true
tracks/76/enabled = true
tracks/76/path = NodePath("Skeleton_01/Skeleton3D:RightUpLeg")
tracks/76/interp = 1
tracks/76/loop_wrap = true
tracks/76/keys = PackedFloat32Array(0, 1, 0.667174, -0.545904, -0.459788, 0.21322, 0.0333333, 1, 0.659698, -0.547262, -0.467899, 0.215346, 0.0666667, 1, 0.653778, -0.547825, -0.473889, 0.218842, 0.1, 1, 0.649408, -0.547602, -0.477914, 0.223608, 0.133333, 1, 0.645786, -0.54701, -0.481111, 0.228633, 0.166667, 1, 0.642717, -0.546164, -0.483834, 0.233506, 0.2, 1, 0.63983, -0.545313, -0.486495, 0.237853, 0.233333, 1, 0.636954, -0.544546, -0.489413, 0.241315, 0.266667, 1, 0.634133, -0.543984, -0.492248, 0.24423, 0.3, 1, 0.63152, -0.543557, -0.495152, 0.246074, 0.333333, 1, 0.62936, -0.54306, -0.497976, 0.247007, 0.4, 1, 0.625453, -0.543019, -0.504423, 0.243919, 0.433333, 1, 0.623883, -0.543509, -0.506916, 0.241668, 0.466667, 1, 0.622637, -0.543833, -0.508997, 0.239773, 0.5, 1, 0.621882, -0.543836, -0.510534, 0.238454, 0.533333, 1, 0.621853, -0.544154, -0.511014, 0.236771, 0.566667, 1, 0.622506, -0.544416, -0.510913, 0.234661, 0.6, 1, 0.624237, -0.543557, -0.511115, 0.231594, 0.633333, 1, 0.626065, -0.54262, -0.511466, 0.228054, 0.7, 1, 0.630084, -0.540736, -0.512051, 0.220005, 0.733333, 1, 0.633274, -0.539391, -0.510877, 0.216855, 0.766667, 1, 0.636684, -0.538056, -0.509297, 0.213882, 0.8, 1, 0.640268, -0.536707, -0.507343, 0.211202, 0.833333, 1, 0.643401, -0.536294, -0.505125, 0.208022, 0.866667, 1, 0.647104, -0.535681, -0.501906, 0.205896, 0.9, 1, 0.651409, -0.534883, -0.497483, 0.205126, 0.933333, 1, 0.65554, -0.534047, -0.493098, 0.204732, 0.966667, 1, 0.659492, -0.533326, -0.488285, 0.205457, 1, 1, 0.663138, -0.532727, -0.483247, 0.207176, 1.03333, 1, 0.665686, -0.533211, -0.478224, 0.209405, 1.06667, 1, 0.66762, -0.534401, -0.473433, 0.211095, 1.1, 1, 0.668986, -0.536074, -0.469097, 0.212202, 1.16667, 1, 0.67026, -0.539837, -0.461988, 0.214232, 1.2, 1, 0.670232, -0.541993, -0.459006, 0.215282, 1.23333, 1, 0.670169, -0.543388, -0.457216, 0.21577, 1.26667, 1, 0.670026, -0.544248, -0.45614, 0.216323)
tracks/77/type = "position_3d"
tracks/77/imported = true
tracks/77/enabled = true
tracks/77/path = NodePath("Skeleton_01/Skeleton3D:RightLeg")
tracks/77/interp = 1
tracks/77/loop_wrap = true
tracks/77/keys = PackedFloat32Array()
tracks/78/type = "rotation_3d"
tracks/78/imported = true
tracks/78/enabled = true
tracks/78/path = NodePath("Skeleton_01/Skeleton3D:RightLeg")
tracks/78/interp = 1
tracks/78/loop_wrap = true
tracks/78/keys = PackedFloat32Array(0, 1, 0.0430863, 0.0280434, -0.943006, 0.32878, 0.0333333, 1, 0.0423959, 0.0262051, -0.949326, 0.310315, 0.0666667, 1, 0.0439255, 0.0244071, -0.953981, 0.295627, 0.1, 1, 0.0472928, 0.022598, -0.957363, 0.284093, 0.133333, 1, 0.0503276, 0.0209876, -0.959882, 0.275053, 0.166667, 1, 0.0523078, 0.0195724, -0.961779, 0.268069, 0.2, 1, 0.0536068, 0.0183611, -0.963374, 0.262106, 0.233333, 1, 0.0553649, 0.0175837, -0.964797, 0.256501, 0.266667, 1, 0.0589119, 0.0171514, -0.96614, 0.250615, 0.3, 1, 0.0645479, 0.0167119, -0.96754, 0.243764, 0.333333, 1, 0.0704746, 0.0160784, -0.968844, 0.236888, 0.366667, 1, 0.0735445, 0.015351, -0.970112, 0.230734, 0.4, 1, 0.0741296, 0.01475, -0.971322, 0.225434, 0.433333, 1, 0.0734308, 0.0143826, -0.972315, 0.221371, 0.466667, 1, 0.071259, 0.0142035, -0.972868, 0.219654, 0.5, 1, 0.0670797, 0.0139831, -0.973053, 0.220166, 0.533333, 1, 0.0626221, 0.0136361, -0.973256, 0.220602, 0.566667, 1, 0.0596215, 0.0133806, -0.973357, 0.221003, 0.633333, 1, 0.0584727, 0.0135748, -0.973274, 0.221663, 0.666667, 1, 0.0566879, 0.0139225, -0.97332, 0.221904, 0.7, 1, 0.0559286, 0.0143129, -0.973198, 0.222606, 0.733333, 1, 0.0564061, 0.0149062, -0.97244, 0.225735, 0.766667, 1, 0.0562275, 0.015573, -0.971477, 0.229845, 0.8, 1, 0.0546071, 0.0162132, -0.970306, 0.235078, 0.833333, 1, 0.0513728, 0.0169367, -0.968876, 0.241563, 0.866667, 1, 0.0490668, 0.0176013, -0.967122, 0.248912, 0.9, 1, 0.0483413, 0.0181497, -0.965051, 0.256925, 0.933333, 1, 0.0470649, 0.0187324, -0.962809, 0.26539, 1, 1, 0.0451962, 0.0199215, -0.957884, 0.282877, 1.03333, 1, 0.0440762, 0.0205072, -0.955356, 0.29143, 1.06667, 1, 0.0435311, 0.0210707, -0.952835, 0.299612, 1.1, 1, 0.0428961, 0.0215874, -0.950457, 0.307124, 1.13333, 1, 0.0421706, 0.022049, -0.948279, 0.313852, 1.16667, 1, 0.0416824, 0.0224405, -0.946363, 0.31962, 1.2, 1, 0.0409702, 0.022754, -0.944809, 0.324253, 1.23333, 1, 0.0402831, 0.0229463, -0.943828, 0.327169, 1.26667, 1, 0.0399419, 0.0230445, -0.943358, 0.328556)
tracks/79/type = "position_3d"
tracks/79/imported = true
tracks/79/enabled = true
tracks/79/path = NodePath("Skeleton_01/Skeleton3D:RightFoot")
tracks/79/interp = 1
tracks/79/loop_wrap = true
tracks/79/keys = PackedFloat32Array()
tracks/80/type = "rotation_3d"
tracks/80/imported = true
tracks/80/enabled = true
tracks/80/path = NodePath("Skeleton_01/Skeleton3D:RightFoot")
tracks/80/interp = 1
tracks/80/loop_wrap = true
tracks/80/keys = PackedFloat32Array(0, 1, 0.258534, -0.640917, 0.527709, 0.493871, 0.0333333, 1, 0.246243, -0.642052, 0.530399, 0.495793, 0.0666667, 1, 0.235788, -0.643151, 0.532882, 0.496787, 0.1, 1, 0.227015, -0.644105, 0.534969, 0.497395, 0.133333, 1, 0.219991, -0.644577, 0.536503, 0.498286, 0.166667, 1, 0.215094, -0.643965, 0.53794, 0.499664, 0.233333, 1, 0.206705, -0.642537, 0.540146, 0.502656, 0.266667, 1, 0.20141, -0.642764, 0.541618, 0.502929, 0.3, 1, 0.194462, -0.644028, 0.542904, 0.502661, 0.333333, 1, 0.187805, -0.644669, 0.543658, 0.503555, 0.366667, 1, 0.18295, -0.643698, 0.54344, 0.506809, 0.4, 1, 0.179656, -0.641649, 0.542677, 0.511382, 0.433333, 1, 0.177694, -0.639258, 0.541795, 0.515977, 0.466667, 1, 0.177479, -0.636688, 0.541235, 0.519802, 0.5, 1, 0.178719, -0.633856, 0.541079, 0.522991, 0.533333, 1, 0.180566, -0.632128, 0.54055, 0.52499, 0.566667, 1, 0.182126, -0.631102, 0.54007, 0.52618, 0.666667, 1, 0.183669, -0.631072, 0.539676, 0.526083, 0.7, 1, 0.184959, -0.631737, 0.539138, 0.525385, 0.733333, 1, 0.186384, -0.632611, 0.538519, 0.524463, 0.766667, 1, 0.188729, -0.633502, 0.537487, 0.523607, 0.8, 1, 0.192306, -0.634048, 0.535926, 0.523245, 0.833333, 1, 0.197678, -0.633757, 0.534916, 0.522628, 0.866667, 1, 0.203489, -0.633694, 0.533775, 0.52164, 0.9, 1, 0.209537, -0.633893, 0.532796, 0.520002, 0.933333, 1, 0.21606, -0.63397, 0.53181, 0.518245, 1.03333, 1, 0.236206, -0.634095, 0.529715, 0.511402, 1.06667, 1, 0.242551, -0.634106, 0.529422, 0.508714, 1.1, 1, 0.247816, -0.634701, 0.528417, 0.506476, 1.13333, 1, 0.252572, -0.635193, 0.527533, 0.504426, 1.16667, 1, 0.256563, -0.635701, 0.526753, 0.502584, 1.2, 1, 0.259969, -0.635907, 0.526239, 0.501109, 1.23333, 1, 0.262094, -0.636089, 0.5257, 0.500337, 1.26667, 1, 0.263003, -0.636288, 0.525291, 0.500037)
tracks/81/type = "position_3d"
tracks/81/imported = true
tracks/81/enabled = true
tracks/81/path = NodePath("Skeleton_01/Skeleton3D:RightToeBase")
tracks/81/interp = 1
tracks/81/loop_wrap = true
tracks/81/keys = PackedFloat32Array()
tracks/82/type = "rotation_3d"
tracks/82/imported = true
tracks/82/enabled = true
tracks/82/path = NodePath("Skeleton_01/Skeleton3D:RightToeBase")
tracks/82/interp = 1
tracks/82/loop_wrap = true
tracks/82/keys = PackedFloat32Array(0, 1, -0.0035225, 0.999923, 8.6598e-05, 0.0118776, 0.0333333, 1, -0.00291823, 0.999943, -0.00330956, 0.00976268, 0.0666667, 1, -0.00248735, 0.999946, -0.00605796, 0.0080323, 0.1, 1, -0.00229961, 0.99994, -0.00826099, 0.00688384, 0.133333, 1, -0.00209481, 0.999931, -0.0100196, 0.00574411, 0.166667, 1, -0.00189629, 0.999922, -0.0113963, 0.00470598, 0.2, 1, -0.00181158, 0.999911, -0.012573, 0.00403194, 0.233333, 1, -0.00168443, 0.9999, -0.0136434, 0.00337651, 0.266667, 1, -0.00150793, 0.999887, -0.0147104, 0.00274221, 0.3, 1, -0.00126756, 0.999871, -0.0158711, 0.00213501, 0.333333, 1, -0.000976889, 0.999855, -0.0169025, 0.00154915, 0.4, 1, -0.000828822, 0.999832, -0.018219, 0.00167988, 0.533333, 1, -0.000784766, 0.999821, -0.0187514, 0.00257708, 0.7, 1, -0.000957864, 0.999833, -0.0179126, 0.00360315, 0.766667, 1, -0.00102597, 0.999852, -0.0167171, 0.00401466, 0.8, 1, -0.00105259, 0.999865, -0.0158501, 0.00422372, 0.833333, 1, -0.00107268, 0.99988, -0.0147739, 0.00443596, 0.866667, 1, -0.00108888, 0.999897, -0.0135536, 0.00464989, 0.9, 1, -0.00110185, 0.999913, -0.0122179, 0.00486596, 0.933333, 1, -0.00111391, 0.999928, -0.0108005, 0.00508213, 0.966667, 1, -0.00142077, 0.999937, -0.00933562, 0.00598907, 1, 1, -0.00173187, 0.999944, -0.00785936, 0.00689464, 1.03333, 1, -0.00204742, 0.999947, -0.00640767, 0.00779839, 1.06667, 1, -0.00236917, 0.999947, -0.00500712, 0.00869972, 1.1, 1, -0.00255191, 0.999947, -0.00372436, 0.0092645, 1.13333, 1, -0.00274236, 0.999945, -0.00256698, 0.00982607, 1.16667, 1, -0.00294, 0.999941, -0.00157085, 0.0103844, 1.23333, 1, -0.00336443, 0.999928, -0.000232807, 0.0114875, 1.26667, 1, -0.0035108, 0.999924, 3.59271e-05, 0.0118461)

[sub_resource type="Animation" id="Animation_wlh02"]
resource_name = "Jump_Bomb"
length = 1.2667
loop_mode = 1
tracks/0/type = "position_3d"
tracks/0/imported = true
tracks/0/enabled = false
tracks/0/path = NodePath("Skeleton_01/Skeleton3D:Root")
tracks/0/interp = 0
tracks/0/loop_wrap = true
tracks/0/keys = PackedFloat32Array()
tracks/1/type = "position_3d"
tracks/1/imported = true
tracks/1/enabled = false
tracks/1/path = NodePath("Skeleton_01/Skeleton3D:Hips")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = PackedFloat32Array(0, 1, -0.0731411, 1.26425, -0.101048, 0.0333333, 1, -0.0762938, 1.26512, -0.100908, 0.0666667, 1, -0.0792626, 1.26552, -0.100774, 0.1, 1, -0.0820513, 1.26524, -0.100647, 0.133333, 1, -0.0846631, 1.26496, -0.10052, 0.166667, 1, -0.0870673, 1.26467, -0.100378, 0.2, 1, -0.0892946, 1.26439, -0.100225, 0.233333, 1, -0.0913433, 1.26369, -0.100058, 0.266667, 1, -0.0932111, 1.2627, -0.0998617, 0.3, 1, -0.0948446, 1.26148, -0.0996464, 0.333333, 1, -0.0961271, 1.25979, -0.0993627, 0.366667, 1, -0.0970173, 1.25757, -0.0989687, 0.4, 1, -0.0975801, 1.25494, -0.0983892, 0.433333, 1, -0.097832, 1.25203, -0.0976682, 0.466667, 1, -0.0978766, 1.24901, -0.0969312, 0.5, 1, -0.0979212, 1.24603, -0.0962714, 0.566667, 1, -0.0958946, 1.24088, -0.0955819, 0.6, 1, -0.0935341, 1.23908, -0.0956507, 0.633333, 1, -0.0906576, 1.23816, -0.0958777, 0.666667, 1, -0.0875191, 1.23762, -0.0963291, 0.7, 1, -0.0844196, 1.23726, -0.0969619, 0.733333, 1, -0.0816446, 1.23764, -0.0977157, 0.766667, 1, -0.0794523, 1.23803, -0.0985609, 0.8, 1, -0.0777751, 1.23842, -0.0994692, 0.833333, 1, -0.0762031, 1.2388, -0.100383, 0.866667, 1, -0.0746951, 1.23962, -0.101258, 0.9, 1, -0.0732786, 1.24121, -0.102073, 0.933333, 1, -0.0719821, 1.24302, -0.102814, 1.03333, 1, -0.0692118, 1.2494, -0.104355, 1.06667, 1, -0.0687341, 1.25164, -0.10443, 1.1, 1, -0.0685761, 1.25385, -0.104437, 1.13333, 1, -0.0688161, 1.25598, -0.104312, 1.16667, 1, -0.0696831, 1.258, -0.103912, 1.2, 1, -0.0709751, 1.26018, -0.103345, 1.23333, 1, -0.0722248, 1.26248, -0.102544, 1.2667, 1, -0.0729826, 1.26394, -0.101511)
tracks/2/type = "rotation_3d"
tracks/2/imported = true
tracks/2/enabled = true
tracks/2/path = NodePath("Skeleton_01/Skeleton3D:Hips")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = PackedFloat32Array(0, 1, -0.214301, -0.331054, 0.0588823, 0.917067, 0.0333333, 1, -0.224035, -0.328166, 0.0561403, 0.915949, 0.0666667, 1, -0.23211, -0.325662, 0.0539258, 0.914965, 0.1, 1, -0.238813, -0.323472, 0.0521249, 0.914121, 0.133333, 1, -0.244426, -0.321522, 0.0506332, 0.913409, 0.166667, 1, -0.24915, -0.31975, 0.049371, 0.912823, 0.2, 1, -0.253435, -0.318038, 0.0482041, 0.912304, 0.233333, 1, -0.257568, -0.316305, 0.0470468, 0.91181, 0.266667, 1, -0.26183, -0.314467, 0.0458164, 0.911294, 0.3, 1, -0.266094, -0.312425, 0.0445347, 0.910825, 0.333333, 1, -0.269905, -0.310236, 0.0431774, 0.910517, 0.366667, 1, -0.273132, -0.307998, 0.0417484, 0.910381, 0.4, 1, -0.275674, -0.305838, 0.0403624, 0.910405, 0.433333, 1, -0.276764, -0.303917, 0.0393494, 0.910762, 0.466667, 1, -0.2777, -0.302401, 0.0383803, 0.911023, 0.5, 1, -0.277599, -0.301475, 0.0377868, 0.911386, 0.533333, 1, -0.276253, -0.300817, 0.0377738, 0.912012, 0.566667, 1, -0.27377, -0.300717, 0.0379938, 0.912785, 0.6, 1, -0.270603, -0.300619, 0.0385237, 0.913739, 0.633333, 1, -0.266916, -0.30053, 0.0393126, 0.914818, 0.666667, 1, -0.262861, -0.300466, 0.0403463, 0.915968, 0.7, 1, -0.258531, -0.300825, 0.0413623, 0.917036, 0.733333, 1, -0.254153, -0.30139, 0.0425442, 0.91802, 0.766667, 1, -0.249976, -0.302098, 0.0436054, 0.918884, 0.8, 1, -0.246152, -0.30298, 0.0444868, 0.919583, 0.833333, 1, -0.242679, -0.304192, 0.0454489, 0.920059, 0.866667, 1, -0.239509, -0.305624, 0.0463859, 0.920368, 0.9, 1, -0.236605, -0.307245, 0.0473215, 0.920532, 0.933333, 1, -0.23393, -0.309023, 0.0482783, 0.920571, 1.1, 1, -0.222737, -0.319092, 0.0543035, 0.919576, 1.13333, 1, -0.220626, -0.321243, 0.0558325, 0.919244, 1.16667, 1, -0.218584, -0.32392, 0.0570958, 0.918715, 1.2, 1, -0.216621, -0.326862, 0.0580391, 0.918078, 1.23333, 1, -0.215359, -0.329356, 0.0585311, 0.917453, 1.26667, 1, -0.214525, -0.330771, 0.0588102, 0.917121)
tracks/3/type = "position_3d"
tracks/3/imported = true
tracks/3/enabled = true
tracks/3/path = NodePath("Skeleton_01/Skeleton3D:LeftUpLeg")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = PackedFloat32Array()
tracks/4/type = "rotation_3d"
tracks/4/imported = true
tracks/4/enabled = true
tracks/4/path = NodePath("Skeleton_01/Skeleton3D:LeftUpLeg")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = PackedFloat32Array(0, 1, 0.702759, 0.443967, 0.42249, 0.361283, 0.0333333, 1, 0.697665, 0.450916, 0.423067, 0.361874, 0.0666667, 1, 0.692047, 0.458174, 0.424617, 0.361729, 0.1, 1, 0.68619, 0.465473, 0.42672, 0.361093, 0.133333, 1, 0.680529, 0.47246, 0.428741, 0.360336, 0.166667, 1, 0.675148, 0.478983, 0.430711, 0.359497, 0.2, 1, 0.670112, 0.485053, 0.432548, 0.358575, 0.233333, 1, 0.665926, 0.490233, 0.433564, 0.358102, 0.266667, 1, 0.662729, 0.49436, 0.433648, 0.358257, 0.3, 1, 0.660387, 0.498051, 0.433378, 0.357796, 0.333333, 1, 0.657648, 0.50176, 0.434294, 0.356544, 0.366667, 1, 0.654872, 0.505058, 0.436142, 0.354738, 0.4, 1, 0.652885, 0.507106, 0.438835, 0.352148, 0.433333, 1, 0.651319, 0.508573, 0.44145, 0.349654, 0.466667, 1, 0.650406, 0.509031, 0.443497, 0.348095, 0.5, 1, 0.650072, 0.509026, 0.444687, 0.347206, 0.533333, 1, 0.650291, 0.508585, 0.445076, 0.346945, 0.566667, 1, 0.650997, 0.508078, 0.445068, 0.346373, 0.6, 1, 0.652377, 0.506514, 0.445483, 0.345532, 0.633333, 1, 0.654274, 0.504425, 0.445452, 0.345042, 0.666667, 1, 0.656706, 0.501834, 0.444832, 0.344998, 0.7, 1, 0.659297, 0.49893, 0.444351, 0.344888, 0.733333, 1, 0.66194, 0.495927, 0.443871, 0.344776, 0.766667, 1, 0.664967, 0.492524, 0.442941, 0.345025, 0.8, 1, 0.668357, 0.488765, 0.441518, 0.345643, 0.833333, 1, 0.672059, 0.484688, 0.439586, 0.346668, 0.866667, 1, 0.675557, 0.480712, 0.4379, 0.347538, 0.9, 1, 0.678731, 0.476956, 0.43657, 0.3482, 0.933333, 1, 0.681961, 0.473167, 0.435045, 0.348967, 0.966667, 1, 0.684772, 0.469253, 0.433836, 0.350251, 1, 1, 0.687412, 0.465543, 0.432681, 0.351457, 1.03333, 1, 0.689895, 0.462072, 0.43147, 0.352658, 1.06667, 1, 0.692143, 0.458875, 0.430328, 0.353821, 1.1, 1, 0.694246, 0.455826, 0.429272, 0.354925, 1.13333, 1, 0.696169, 0.452979, 0.428257, 0.356027, 1.16667, 1, 0.697752, 0.450563, 0.427409, 0.357011, 1.2, 1, 0.699098, 0.448444, 0.426688, 0.357907, 1.23333, 1, 0.699971, 0.447093, 0.426349, 0.358294, 1.26667, 1, 0.700427, 0.446163, 0.426132, 0.35882)
tracks/5/type = "position_3d"
tracks/5/imported = true
tracks/5/enabled = true
tracks/5/path = NodePath("Skeleton_01/Skeleton3D:LeftLeg")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = PackedFloat32Array()
tracks/6/type = "rotation_3d"
tracks/6/imported = true
tracks/6/enabled = true
tracks/6/path = NodePath("Skeleton_01/Skeleton3D:LeftLeg")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = PackedFloat32Array(0, 1, 0.043682, -0.0098565, 0.918206, 0.393564, 0.0333333, 1, 0.0406338, -0.00905339, 0.925663, 0.376052, 0.0666667, 1, 0.0358795, -0.00876976, 0.931399, 0.36212, 0.1, 1, 0.0305251, -0.00885857, 0.935763, 0.351193, 0.133333, 1, 0.0261026, -0.00917485, 0.939049, 0.342667, 0.166667, 1, 0.0222658, -0.00952468, 0.94151, 0.336113, 0.2, 1, 0.0186501, -0.0097055, 0.943566, 0.330517, 0.233333, 1, 0.0185022, -0.0101369, 0.945403, 0.325219, 0.266667, 1, 0.022563, -0.0108704, 0.947234, 0.319562, 0.3, 1, 0.0279792, -0.0116488, 0.94934, 0.312786, 0.333333, 1, 0.0304604, -0.0121107, 0.951564, 0.305699, 0.366667, 1, 0.0306172, -0.0121934, 0.953651, 0.299104, 0.4, 1, 0.0303584, -0.0119725, 0.955466, 0.293291, 0.433333, 1, 0.0291326, -0.0114973, 0.956887, 0.288768, 0.466667, 1, 0.0278643, -0.0112044, 0.957774, 0.285949, 0.5, 1, 0.0261817, -0.0108587, 0.958289, 0.284391, 0.533333, 1, 0.025053, -0.0106603, 0.958263, 0.284587, 0.566667, 1, 0.0253972, -0.010817, 0.958078, 0.285176, 0.6, 1, 0.0269773, -0.0112674, 0.957852, 0.285772, 0.633333, 1, 0.0292907, -0.0115809, 0.957598, 0.286381, 0.666667, 1, 0.0318265, -0.0115526, 0.957328, 0.287013, 0.7, 1, 0.0330412, -0.0113897, 0.956566, 0.289412, 0.733333, 1, 0.0330412, -0.0110804, 0.955706, 0.292253, 0.766667, 1, 0.0330226, -0.0105961, 0.954484, 0.296239, 0.8, 1, 0.0334442, -0.0100867, 0.952889, 0.301302, 0.833333, 1, 0.0341664, -0.00951925, 0.950864, 0.307569, 0.866667, 1, 0.0346625, -0.00929456, 0.948519, 0.314681, 0.9, 1, 0.0351111, -0.00953546, 0.945893, 0.322432, 0.933333, 1, 0.0358385, -0.00979764, 0.943022, 0.330649, 1.03333, 1, 0.0366826, -0.010583, 0.933665, 0.356106, 1.06667, 1, 0.0367737, -0.0108281, 0.930545, 0.364166, 1.1, 1, 0.037016, -0.0110592, 0.927592, 0.371592, 1.13333, 1, 0.037424, -0.0112727, 0.924873, 0.378261, 1.16667, 1, 0.0376263, -0.0114529, 0.922498, 0.383994, 1.2, 1, 0.0379702, -0.0116033, 0.920547, 0.388609, 1.23333, 1, 0.0383125, -0.0117021, 0.919284, 0.39155, 1.26667, 1, 0.0385053, -0.0117524, 0.918674, 0.39296)
tracks/7/type = "rotation_3d"
tracks/7/imported = true
tracks/7/enabled = true
tracks/7/path = NodePath("Skeleton_01/Skeleton3D:LeftFoot")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = PackedFloat32Array(0, 1, 0.461023, 0.427677, -0.569389, 0.529477, 0.0333333, 1, 0.463055, 0.422307, -0.571019, 0.530259, 0.0666667, 1, 0.464508, 0.417453, -0.572687, 0.531032, 0.1, 1, 0.465093, 0.413507, -0.574423, 0.531733, 0.133333, 1, 0.464687, 0.410683, -0.576574, 0.531947, 0.166667, 1, 0.463703, 0.408756, -0.578712, 0.531969, 0.2, 1, 0.462908, 0.407173, -0.580669, 0.531742, 0.233333, 1, 0.460935, 0.407048, -0.583527, 0.530422, 0.266667, 1, 0.458375, 0.40911, -0.586772, 0.527466, 0.3, 1, 0.455566, 0.411784, -0.590181, 0.524004, 0.333333, 1, 0.452977, 0.414345, -0.592596, 0.521497, 0.366667, 1, 0.451821, 0.417023, -0.593864, 0.518919, 0.4, 1, 0.451888, 0.419861, -0.594573, 0.515749, 0.433333, 1, 0.452842, 0.422056, -0.594679, 0.512991, 0.466667, 1, 0.453859, 0.423781, -0.594645, 0.510705, 0.5, 1, 0.454888, 0.424376, -0.593984, 0.510064, 0.533333, 1, 0.455786, 0.42457, -0.592997, 0.51025, 0.566667, 1, 0.455915, 0.425281, -0.592614, 0.509986, 0.633333, 1, 0.454215, 0.427275, -0.59337, 0.508957, 0.7, 1, 0.454027, 0.42797, -0.592965, 0.509012, 0.733333, 1, 0.454976, 0.427421, -0.59196, 0.509796, 0.766667, 1, 0.456275, 0.426565, -0.590683, 0.510831, 0.8, 1, 0.457635, 0.425652, -0.589354, 0.511911, 0.833333, 1, 0.459063, 0.424692, -0.587852, 0.513155, 0.866667, 1, 0.45991, 0.424369, -0.586052, 0.51472, 0.9, 1, 0.460228, 0.424587, -0.584265, 0.516285, 0.933333, 1, 0.46046, 0.424868, -0.58254, 0.517794, 0.966667, 1, 0.460721, 0.425127, -0.580646, 0.519474, 1, 1, 0.461002, 0.425355, -0.57873, 0.521174, 1.03333, 1, 0.461173, 0.425669, -0.576892, 0.5228, 1.06667, 1, 0.461433, 0.425873, -0.575159, 0.524313, 1.1, 1, 0.461486, 0.426089, -0.57367, 0.52572, 1.13333, 1, 0.461436, 0.426387, -0.572387, 0.526919, 1.16667, 1, 0.461333, 0.426728, -0.571144, 0.528081, 1.2, 1, 0.461407, 0.426862, -0.570385, 0.528728, 1.26667, 1, 0.461324, 0.427321, -0.569363, 0.529531)
tracks/8/type = "position_3d"
tracks/8/imported = true
tracks/8/enabled = true
tracks/8/path = NodePath("Skeleton_01/Skeleton3D:LeftToeBase")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = PackedFloat32Array()
tracks/9/type = "rotation_3d"
tracks/9/imported = true
tracks/9/enabled = true
tracks/9/path = NodePath("Skeleton_01/Skeleton3D:LeftToeBase")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = PackedFloat32Array(0, 1, 0.00503226, 0.9998, 0.0151207, 0.0120585, 0.0333333, 1, 0.00456518, 0.999806, 0.0159201, 0.0107121, 0.0666667, 1, 0.00419986, 0.999809, 0.0166225, 0.00940985, 0.1, 1, 0.00392066, 0.999811, 0.0172311, 0.00814534, 0.133333, 1, 0.00359141, 0.99981, 0.0177436, 0.00716356, 0.166667, 1, 0.00328885, 0.99981, 0.018149, 0.00626942, 0.266667, 1, 0.00247236, 0.999813, 0.0188112, 0.00362349, 0.333333, 1, 0.00107391, 0.999817, 0.0188236, 0.00332402, 0.4, 1, -0.000451976, 0.999821, 0.0187068, 0.00296531, 0.466667, 1, -0.00158448, 0.999823, 0.0185383, 0.00278902, 0.666667, 1, -0.00195598, 0.99983, 0.0179741, 0.00365966, 0.766667, 1, -0.0013871, 0.999834, 0.0176112, 0.00444306, 0.833333, 1, -0.000574308, 0.999837, 0.017289, 0.00516479, 0.9, 1, 0.000537714, 0.999839, 0.0168977, 0.00602312, 0.966667, 1, 0.00181211, 0.999838, 0.0164793, 0.00695472, 1.03333, 1, 0.0031073, 0.999835, 0.0160565, 0.00789477, 1.1, 1, 0.00409576, 0.999827, 0.015674, 0.00919401, 1.16667, 1, 0.0048051, 0.999816, 0.0153654, 0.0104205, 1.23333, 1, 0.00511648, 0.999806, 0.0151711, 0.0114652, 1.26667, 1, 0.00507663, 0.999802, 0.01513, 0.0118983)
tracks/10/type = "position_3d"
tracks/10/imported = true
tracks/10/enabled = true
tracks/10/path = NodePath("Skeleton_01/Skeleton3D:Spine")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = PackedFloat32Array()
tracks/11/type = "rotation_3d"
tracks/11/imported = true
tracks/11/enabled = true
tracks/11/path = NodePath("Skeleton_01/Skeleton3D:Spine")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = PackedFloat32Array(0, 1, 0.198236, 0.00648596, -0.0328207, 0.979583, 0.0333333, 1, 0.201098, 0.00631666, -0.0324027, 0.979015, 0.0666667, 1, 0.203389, 0.00617666, -0.0320353, 0.978554, 0.1, 1, 0.205204, 0.00605933, -0.0317077, 0.978187, 0.133333, 1, 0.206641, 0.00595819, -0.0314105, 0.977894, 0.166667, 1, 0.207766, 0.00586803, -0.0311352, 0.977665, 0.2, 1, 0.208733, 0.00578014, -0.0308659, 0.977468, 0.233333, 1, 0.209639, 0.00568946, -0.0305933, 0.977284, 0.266667, 1, 0.21058, 0.00559043, -0.0303065, 0.977091, 0.3, 1, 0.211663, 0.00547063, -0.0299892, 0.976867, 0.366667, 1, 0.213722, 0.00511792, -0.0292612, 0.976443, 0.433333, 1, 0.215143, 0.00472394, -0.0285265, 0.976155, 0.566667, 1, 0.215581, 0.0044478, -0.0276354, 0.976085, 0.733333, 1, 0.214498, 0.00456563, -0.0273593, 0.97633, 0.8, 1, 0.213087, 0.00466181, -0.0273762, 0.976638, 0.833333, 1, 0.212088, 0.00474892, -0.0275469, 0.976851, 0.866667, 1, 0.210952, 0.00485545, -0.0278234, 0.977088, 0.9, 1, 0.209711, 0.00497995, -0.0281879, 0.977344, 1.06667, 1, 0.202996, 0.0058018, -0.0307198, 0.97868, 1.1, 1, 0.201795, 0.00596821, -0.0312314, 0.978911, 1.13333, 1, 0.200714, 0.00612095, -0.031702, 0.979118, 1.16667, 1, 0.199784, 0.00625562, -0.032115, 0.979294, 1.23333, 1, 0.198556, 0.00644006, -0.0326823, 0.979523, 1.26667, 1, 0.198328, 0.00647721, -0.0327988, 0.979565)
tracks/12/type = "position_3d"
tracks/12/imported = true
tracks/12/enabled = true
tracks/12/path = NodePath("Skeleton_01/Skeleton3D:Spine1")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = PackedFloat32Array()
tracks/13/type = "rotation_3d"
tracks/13/imported = true
tracks/13/enabled = true
tracks/13/path = NodePath("Skeleton_01/Skeleton3D:Spine1")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = PackedFloat32Array()
tracks/14/type = "position_3d"
tracks/14/imported = true
tracks/14/enabled = true
tracks/14/path = NodePath("Skeleton_01/Skeleton3D:Neck")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = PackedFloat32Array()
tracks/15/type = "rotation_3d"
tracks/15/imported = true
tracks/15/enabled = true
tracks/15/path = NodePath("Skeleton_01/Skeleton3D:Neck")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = PackedFloat32Array(0, 1, 0.301597, 0.0700628, -0.149428, 0.939043, 0.0333333, 1, 0.303664, 0.0702859, -0.151441, 0.938037, 0.0666667, 1, 0.305766, 0.0705252, -0.153175, 0.937054, 0.1, 1, 0.307881, 0.070773, -0.154681, 0.936095, 0.133333, 1, 0.30999, 0.0710301, -0.156012, 0.935159, 0.166667, 1, 0.312067, 0.0713096, -0.157205, 0.934246, 0.2, 1, 0.314094, 0.0716214, -0.158341, 0.93335, 0.233333, 1, 0.316054, 0.0719829, -0.159471, 0.932468, 0.3, 1, 0.319694, 0.0729003, -0.161909, 0.930734, 0.333333, 1, 0.321455, 0.0730639, -0.163126, 0.929902, 0.366667, 1, 0.323178, 0.072657, -0.164139, 0.929158, 0.4, 1, 0.324786, 0.072069, -0.164981, 0.928494, 0.433333, 1, 0.326123, 0.0715781, -0.16563, 0.927948, 0.466667, 1, 0.32708, 0.0712206, -0.16603, 0.927567, 0.7, 1, 0.326348, 0.0708093, -0.165514, 0.927948, 0.733333, 1, 0.325628, 0.0707144, -0.165098, 0.928283, 0.766667, 1, 0.324681, 0.0706033, -0.164523, 0.928725, 0.8, 1, 0.323477, 0.0704715, -0.163787, 0.929285, 0.833333, 1, 0.321984, 0.0703254, -0.162865, 0.929976, 0.866667, 1, 0.320292, 0.0701795, -0.161809, 0.930755, 0.9, 1, 0.318446, 0.0700441, -0.160646, 0.9316, 0.933333, 1, 0.316491, 0.0699282, -0.1594, 0.932489, 1, 1, 0.31244, 0.069791, -0.156769, 0.934309, 1.03333, 1, 0.310449, 0.0697691, -0.155454, 0.935194, 1.06667, 1, 0.308539, 0.0697765, -0.154178, 0.936036, 1.1, 1, 0.306781, 0.0698129, -0.152992, 0.936806, 1.13333, 1, 0.305203, 0.0698658, -0.151917, 0.937492, 1.16667, 1, 0.303847, 0.0699273, -0.150987, 0.938078, 1.2, 1, 0.302756, 0.0699873, -0.150233, 0.938547, 1.26667, 1, 0.301728, 0.0700381, -0.149517, 0.938989)
tracks/16/type = "scale_3d"
tracks/16/imported = true
tracks/16/enabled = true
tracks/16/path = NodePath("Skeleton_01/Skeleton3D:Neck")
tracks/16/interp = 0
tracks/16/loop_wrap = true
tracks/16/keys = PackedFloat32Array()
tracks/17/type = "position_3d"
tracks/17/imported = true
tracks/17/enabled = true
tracks/17/path = NodePath("Skeleton_01/Skeleton3D:Head")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = PackedFloat32Array()
tracks/18/type = "rotation_3d"
tracks/18/imported = true
tracks/18/enabled = true
tracks/18/path = NodePath("Skeleton_01/Skeleton3D:Head")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = PackedFloat32Array(0, 1, -0.161071, 0.0732174, -0.0355586, 0.983581, 0.0333333, 1, -0.155896, 0.0704141, -0.0305068, 0.984788, 0.0666667, 1, -0.151715, 0.0682584, -0.0264364, 0.98571, 0.1, 1, -0.148355, 0.0666485, -0.0231651, 0.986414, 0.133333, 1, -0.145637, 0.0654979, -0.0205065, 0.986955, 0.166667, 1, -0.14342, 0.0647707, -0.0183046, 0.98737, 0.233333, 1, -0.139425, 0.0641346, -0.0142029, 0.988052, 0.266667, 1, -0.137261, 0.0640385, -0.0119082, 0.988391, 0.3, 1, -0.134649, 0.0639768, -0.0090752, 0.988784, 0.333333, 1, -0.131621, 0.0641796, -0.00560882, 0.989204, 0.366667, 1, -0.128422, 0.0646209, -0.00175927, 0.989611, 0.4, 1, -0.125325, 0.0652222, 0.00208389, 0.989967, 0.433333, 1, -0.12271, 0.0658892, 0.00540446, 0.990238, 0.466667, 1, -0.12082, 0.0665063, 0.00783743, 0.990413, 0.5, 1, -0.119925, 0.0669726, 0.00899845, 0.990481, 0.633333, 1, -0.120409, 0.0677838, 0.00848889, 0.990371, 0.666667, 1, -0.120943, 0.0679031, 0.00791305, 0.990303, 0.7, 1, -0.121776, 0.0680232, 0.00701558, 0.990199, 0.733333, 1, -0.12292, 0.0681586, 0.00578437, 0.990056, 0.766667, 1, -0.124422, 0.0683204, 0.00416744, 0.989866, 0.8, 1, -0.12633, 0.068529, 0.00211142, 0.989616, 0.833333, 1, -0.128697, 0.0687829, -0.000441008, 0.989296, 0.866667, 1, -0.131381, 0.069075, -0.00333698, 0.988917, 0.9, 1, -0.134309, 0.0694054, -0.00649981, 0.988485, 0.933333, 1, -0.137411, 0.0697702, -0.00985287, 0.988005, 1, 1, -0.143841, 0.0705872, -0.0168171, 0.986937, 1.03333, 1, -0.147003, 0.0710207, -0.0202477, 0.986375, 1.06667, 1, -0.150036, 0.0714564, -0.0235424, 0.985814, 1.1, 1, -0.15283, 0.071876, -0.0265791, 0.985277, 1.13333, 1, -0.155338, 0.0722629, -0.0293093, 0.984779, 1.16667, 1, -0.157493, 0.0726139, -0.0316572, 0.984338, 1.2, 1, -0.159229, 0.0728932, -0.033549, 0.983975, 1.23333, 1, -0.160383, 0.0730965, -0.0348078, 0.983729, 1.26667, 1, -0.160962, 0.073192, -0.03544, 0.983605)
tracks/19/type = "scale_3d"
tracks/19/imported = true
tracks/19/enabled = true
tracks/19/path = NodePath("Skeleton_01/Skeleton3D:Head")
tracks/19/interp = 0
tracks/19/loop_wrap = true
tracks/19/keys = PackedFloat32Array()
tracks/20/type = "position_3d"
tracks/20/imported = true
tracks/20/enabled = true
tracks/20/path = NodePath("Skeleton_01/Skeleton3D:LeftShoulder")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = PackedFloat32Array()
tracks/21/type = "rotation_3d"
tracks/21/imported = true
tracks/21/enabled = true
tracks/21/path = NodePath("Skeleton_01/Skeleton3D:LeftShoulder")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = PackedFloat32Array()
tracks/22/type = "position_3d"
tracks/22/imported = true
tracks/22/enabled = true
tracks/22/path = NodePath("Skeleton_01/Skeleton3D:LeftArm")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = PackedFloat32Array()
tracks/23/type = "rotation_3d"
tracks/23/imported = true
tracks/23/enabled = true
tracks/23/path = NodePath("Skeleton_01/Skeleton3D:LeftArm")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = PackedFloat32Array(0, 1, -0.0462848, -0.062983, 0.376154, 0.923254, 0.0333333, 1, -0.0395201, -0.0573204, 0.356638, 0.931645, 0.0666667, 1, -0.033937, -0.0525586, 0.340824, 0.938043, 0.1, 1, -0.0292662, -0.0485462, 0.328057, 0.942956, 0.133333, 1, -0.0252428, -0.0451404, 0.317639, 0.9468, 0.166667, 1, -0.0216327, -0.042249, 0.309007, 0.949875, 0.233333, 1, -0.014317, -0.0372335, 0.293035, 0.955269, 0.266667, 1, -0.0101145, -0.0348424, 0.284191, 0.958081, 0.3, 1, -0.00495214, -0.032342, 0.273273, 0.96138, 0.333333, 1, 0.00197588, -0.0300545, 0.259453, 0.965286, 0.366667, 1, 0.0103545, -0.0280574, 0.243541, 0.96943, 0.4, 1, 0.0191313, -0.0262792, 0.227236, 0.973297, 0.433333, 1, 0.0270102, -0.0249512, 0.212839, 0.976395, 0.466667, 1, 0.0329229, -0.0241255, 0.20214, 0.978506, 0.5, 1, 0.0358012, -0.023797, 0.196972, 0.979466, 0.6, 1, 0.0356618, -0.0240349, 0.197499, 0.979359, 0.633333, 1, 0.0350291, -0.0243507, 0.198977, 0.979075, 0.666667, 1, 0.0340102, -0.0248655, 0.201354, 0.978612, 0.7, 1, 0.0324187, -0.0256837, 0.205064, 0.977874, 0.733333, 1, 0.0302272, -0.0268053, 0.210153, 0.976833, 0.766667, 1, 0.0273413, -0.0282766, 0.216827, 0.975417, 0.8, 1, 0.0236554, -0.0301474, 0.225302, 0.973535, 0.833333, 1, 0.0190465, -0.0324491, 0.2358, 0.971073, 0.866667, 1, 0.0137882, -0.0350535, 0.247676, 0.96811, 0.9, 1, 0.0080136, -0.0378908, 0.260599, 0.96467, 0.933333, 1, 0.00185818, -0.0408896, 0.274239, 0.96079, 1, 1, -0.0110573, -0.0470097, 0.30235, 0.951973, 1.03333, 1, -0.0174751, -0.0499436, 0.316075, 0.947257, 1.06667, 1, -0.0236266, -0.0528578, 0.329171, 0.942494, 1.1, 1, -0.0293977, -0.0552668, 0.341177, 0.937912, 1.13333, 1, -0.0345506, -0.0575582, 0.351894, 0.93363, 1.16667, 1, -0.038954, -0.0596156, 0.361052, 0.929823, 1.2, 1, -0.0424832, -0.0613188, 0.368387, 0.926675, 1.23333, 1, -0.0448421, -0.0624225, 0.373253, 0.92454, 1.26667, 1, -0.0460475, -0.0629182, 0.375694, 0.923458)
tracks/24/type = "position_3d"
tracks/24/imported = true
tracks/24/enabled = true
tracks/24/path = NodePath("Skeleton_01/Skeleton3D:LeftForeArm")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = PackedFloat32Array()
tracks/25/type = "rotation_3d"
tracks/25/imported = true
tracks/25/enabled = true
tracks/25/path = NodePath("Skeleton_01/Skeleton3D:LeftForeArm")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = PackedFloat32Array(0, 1, 0.0401569, 0.644167, -0.265824, 0.716082, 0.0333333, 1, 0.0293914, 0.647635, -0.254708, 0.717515, 0.0666667, 1, 0.0209656, 0.650367, -0.24572, 0.718474, 0.1, 1, 0.0146457, 0.652558, -0.238778, 0.718985, 0.133333, 1, 0.00945413, 0.654492, -0.232811, 0.71927, 0.166667, 1, 0.00592706, 0.656264, -0.22852, 0.71907, 0.2, 1, 0.00256959, 0.658155, -0.224261, 0.718701, 0.233333, 1, -0.00031164, 0.660258, -0.220407, 0.71797, 0.266667, 1, -0.00341623, 0.662663, -0.216371, 0.716972, 0.3, 1, -0.00707575, 0.665782, -0.211563, 0.715489, 0.333333, 1, -0.0107143, 0.670242, -0.206373, 0.712791, 0.366667, 1, -0.0138434, 0.675859, -0.201239, 0.70889, 0.4, 1, -0.0163981, 0.681841, -0.196427, 0.704444, 0.433333, 1, -0.0182852, 0.687265, -0.192487, 0.700201, 0.466667, 1, -0.0196516, 0.691276, -0.189797, 0.696942, 0.5, 1, -0.0201063, 0.693275, -0.188382, 0.695325, 0.6, 1, -0.0206334, 0.693068, -0.188782, 0.695408, 0.633333, 1, -0.0200025, 0.692733, -0.189781, 0.695488, 0.666667, 1, -0.0191052, 0.692165, -0.19097, 0.695754, 0.7, 1, -0.0177886, 0.691324, -0.19248, 0.696208, 0.733333, 1, -0.0158904, 0.690195, -0.19446, 0.696824, 0.766667, 1, -0.013678, 0.688608, -0.197333, 0.697632, 0.8, 1, -0.0109236, 0.686546, -0.201036, 0.698656, 0.833333, 1, -0.00760593, 0.683911, -0.205714, 0.699921, 0.866667, 1, -0.00374831, 0.680905, -0.210906, 0.701337, 0.9, 1, 0.000474404, 0.677559, -0.216539, 0.702868, 0.933333, 1, 0.00497639, 0.673948, -0.222451, 0.704475, 1, 1, 0.0143658, 0.666243, -0.234568, 0.707737, 1.03333, 1, 0.0190323, 0.662345, -0.240436, 0.709315, 1.06667, 1, 0.0235402, 0.658539, -0.24601, 0.710811, 1.1, 1, 0.0277216, 0.65499, -0.251084, 0.712163, 1.13333, 1, 0.0314997, 0.651758, -0.255599, 0.713364, 1.16667, 1, 0.0347822, 0.64896, -0.259429, 0.714379, 1.2, 1, 0.0373562, 0.646625, -0.262567, 0.715219, 1.23333, 1, 0.0394029, 0.645232, -0.264518, 0.715648, 1.26667, 1, 0.040279, 0.644371, -0.265735, 0.715925)
tracks/26/type = "rotation_3d"
tracks/26/imported = true
tracks/26/enabled = true
tracks/26/path = NodePath("Skeleton_01/Skeleton3D:LeftHand")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = PackedFloat32Array(0, 1, 0.107554, -0.0497513, 0.294738, 0.948202, 0.0333333, 1, 0.109514, -0.0431884, 0.290857, 0.949497, 0.0666667, 1, 0.110704, -0.0383865, 0.288163, 0.950386, 0.1, 1, 0.111429, -0.0350245, 0.286505, 0.950932, 0.133333, 1, 0.111873, -0.0327274, 0.285731, 0.951195, 0.2, 1, 0.112232, -0.029657, 0.286363, 0.951063, 0.233333, 1, 0.112282, -0.0278775, 0.287323, 0.950821, 0.266667, 1, 0.112226, -0.025364, 0.288506, 0.95054, 0.3, 1, 0.11174, -0.0213969, 0.289957, 0.950253, 0.333333, 1, 0.109412, -0.0158222, 0.292869, 0.94974, 0.366667, 1, 0.104923, -0.00949724, 0.297391, 0.948925, 0.4, 1, 0.0991047, -0.00357255, 0.302677, 0.94792, 0.433333, 1, 0.0931611, 0.000896476, 0.30773, 0.946902, 0.466667, 1, 0.0884264, 0.00375802, 0.311638, 0.94607, 0.5, 1, 0.0860477, 0.00499018, 0.313576, 0.945643, 0.766667, 1, 0.0875217, 0.00544733, 0.313596, 0.945499, 0.8, 1, 0.0884315, 0.0052067, 0.31337, 0.94549, 0.833333, 1, 0.0900216, 0.00387729, 0.312715, 0.945564, 0.866667, 1, 0.0920818, 0.00161711, 0.311724, 0.945699, 0.9, 1, 0.0944549, -0.00150292, 0.310452, 0.945884, 0.933333, 1, 0.0969749, -0.00540133, 0.308955, 0.946105, 0.966667, 1, 0.0994573, -0.0100667, 0.307266, 0.946359, 1, 1, 0.101741, -0.0152361, 0.305479, 0.946625, 1.06667, 1, 0.105304, -0.0263992, 0.30183, 0.94716, 1.1, 1, 0.10641, -0.0319445, 0.3001, 0.947416, 1.13333, 1, 0.107119, -0.0371399, 0.29851, 0.947649, 1.16667, 1, 0.107492, -0.041759, 0.297116, 0.947852, 1.2, 1, 0.107619, -0.0455785, 0.295975, 0.948019, 1.23333, 1, 0.107542, -0.0481773, 0.295197, 0.948142, 1.26667, 1, 0.107539, -0.0495002, 0.294809, 0.948195)
tracks/27/type = "position_3d"
tracks/27/imported = true
tracks/27/enabled = true
tracks/27/path = NodePath("Skeleton_01/Skeleton3D:LeftHandIndex1")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = PackedFloat32Array()
tracks/28/type = "rotation_3d"
tracks/28/imported = true
tracks/28/enabled = true
tracks/28/path = NodePath("Skeleton_01/Skeleton3D:LeftHandIndex1")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = PackedFloat32Array()
tracks/29/type = "position_3d"
tracks/29/imported = true
tracks/29/enabled = true
tracks/29/path = NodePath("Skeleton_01/Skeleton3D:LeftHandIndex2")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = PackedFloat32Array()
tracks/30/type = "rotation_3d"
tracks/30/imported = true
tracks/30/enabled = true
tracks/30/path = NodePath("Skeleton_01/Skeleton3D:LeftHandIndex2")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = PackedFloat32Array()
tracks/31/type = "position_3d"
tracks/31/imported = true
tracks/31/enabled = true
tracks/31/path = NodePath("Skeleton_01/Skeleton3D:LeftHandMiddle1")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = PackedFloat32Array()
tracks/32/type = "rotation_3d"
tracks/32/imported = true
tracks/32/enabled = true
tracks/32/path = NodePath("Skeleton_01/Skeleton3D:LeftHandMiddle1")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = PackedFloat32Array()
tracks/33/type = "position_3d"
tracks/33/imported = true
tracks/33/enabled = true
tracks/33/path = NodePath("Skeleton_01/Skeleton3D:LeftHandMiddle2")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/keys = PackedFloat32Array()
tracks/34/type = "rotation_3d"
tracks/34/imported = true
tracks/34/enabled = true
tracks/34/path = NodePath("Skeleton_01/Skeleton3D:LeftHandMiddle2")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/keys = PackedFloat32Array()
tracks/35/type = "position_3d"
tracks/35/imported = true
tracks/35/enabled = true
tracks/35/path = NodePath("Skeleton_01/Skeleton3D:LeftHandPinky1")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/keys = PackedFloat32Array()
tracks/36/type = "rotation_3d"
tracks/36/imported = true
tracks/36/enabled = true
tracks/36/path = NodePath("Skeleton_01/Skeleton3D:LeftHandPinky1")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/keys = PackedFloat32Array()
tracks/37/type = "rotation_3d"
tracks/37/imported = true
tracks/37/enabled = true
tracks/37/path = NodePath("Skeleton_01/Skeleton3D:LeftHandPinky2")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/keys = PackedFloat32Array()
tracks/38/type = "position_3d"
tracks/38/imported = true
tracks/38/enabled = true
tracks/38/path = NodePath("Skeleton_01/Skeleton3D:LeftHandProp")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/keys = PackedFloat32Array()
tracks/39/type = "rotation_3d"
tracks/39/imported = true
tracks/39/enabled = true
tracks/39/path = NodePath("Skeleton_01/Skeleton3D:LeftHandProp")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/keys = PackedFloat32Array()
tracks/40/type = "position_3d"
tracks/40/imported = true
tracks/40/enabled = true
tracks/40/path = NodePath("Skeleton_01/Skeleton3D:LeftHandRing1")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/keys = PackedFloat32Array()
tracks/41/type = "rotation_3d"
tracks/41/imported = true
tracks/41/enabled = true
tracks/41/path = NodePath("Skeleton_01/Skeleton3D:LeftHandRing1")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/keys = PackedFloat32Array()
tracks/42/type = "position_3d"
tracks/42/imported = true
tracks/42/enabled = true
tracks/42/path = NodePath("Skeleton_01/Skeleton3D:LeftHandRing2")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/keys = PackedFloat32Array()
tracks/43/type = "rotation_3d"
tracks/43/imported = true
tracks/43/enabled = true
tracks/43/path = NodePath("Skeleton_01/Skeleton3D:LeftHandRing2")
tracks/43/interp = 1
tracks/43/loop_wrap = true
tracks/43/keys = PackedFloat32Array()
tracks/44/type = "position_3d"
tracks/44/imported = true
tracks/44/enabled = true
tracks/44/path = NodePath("Skeleton_01/Skeleton3D:LeftHandThumb1")
tracks/44/interp = 1
tracks/44/loop_wrap = true
tracks/44/keys = PackedFloat32Array()
tracks/45/type = "rotation_3d"
tracks/45/imported = true
tracks/45/enabled = true
tracks/45/path = NodePath("Skeleton_01/Skeleton3D:LeftHandThumb1")
tracks/45/interp = 1
tracks/45/loop_wrap = true
tracks/45/keys = PackedFloat32Array()
tracks/46/type = "position_3d"
tracks/46/imported = true
tracks/46/enabled = true
tracks/46/path = NodePath("Skeleton_01/Skeleton3D:LeftHandThumb2")
tracks/46/interp = 1
tracks/46/loop_wrap = true
tracks/46/keys = PackedFloat32Array()
tracks/47/type = "rotation_3d"
tracks/47/imported = true
tracks/47/enabled = true
tracks/47/path = NodePath("Skeleton_01/Skeleton3D:LeftHandThumb2")
tracks/47/interp = 1
tracks/47/loop_wrap = true
tracks/47/keys = PackedFloat32Array()
tracks/48/type = "position_3d"
tracks/48/imported = true
tracks/48/enabled = true
tracks/48/path = NodePath("Skeleton_01/Skeleton3D:RightShoulder")
tracks/48/interp = 1
tracks/48/loop_wrap = true
tracks/48/keys = PackedFloat32Array()
tracks/49/type = "rotation_3d"
tracks/49/imported = true
tracks/49/enabled = true
tracks/49/path = NodePath("Skeleton_01/Skeleton3D:RightShoulder")
tracks/49/interp = 1
tracks/49/loop_wrap = true
tracks/49/keys = PackedFloat32Array()
tracks/50/type = "position_3d"
tracks/50/imported = true
tracks/50/enabled = true
tracks/50/path = NodePath("Skeleton_01/Skeleton3D:RightArm")
tracks/50/interp = 1
tracks/50/loop_wrap = true
tracks/50/keys = PackedFloat32Array()
tracks/51/type = "rotation_3d"
tracks/51/imported = true
tracks/51/enabled = true
tracks/51/path = NodePath("Skeleton_01/Skeleton3D:RightArm")
tracks/51/interp = 1
tracks/51/loop_wrap = true
tracks/51/keys = PackedFloat32Array(0, 1, 0.189305, 0.0545081, -0.338254, 0.920205, 0.0333333, 1, 0.192087, 0.045161, -0.335233, 0.921239, 0.0666667, 1, 0.194315, 0.0374018, -0.332713, 0.922033, 0.1, 1, 0.19611, 0.0309581, -0.330589, 0.922656, 0.133333, 1, 0.197579, 0.0255593, -0.328752, 0.923164, 0.166667, 1, 0.198793, 0.0210165, -0.327107, 0.923602, 0.2, 1, 0.199888, 0.0168985, -0.325494, 0.92402, 0.233333, 1, 0.200941, 0.0129346, -0.323802, 0.92445, 0.266667, 1, 0.202024, 0.00885347, -0.32192, 0.924919, 0.3, 1, 0.203321, 0.00439116, -0.319625, 0.925463, 0.333333, 1, 0.205423, 0.000182552, -0.316635, 0.926037, 0.366667, 1, 0.208313, -0.00335832, -0.313083, 0.926592, 0.4, 1, 0.211519, -0.00623353, -0.309382, 0.927094, 0.433333, 1, 0.214485, -0.00831551, -0.306074, 0.927494, 0.466667, 1, 0.216743, -0.00967664, -0.303597, 0.92777, 0.5, 1, 0.217906, -0.010069, -0.302434, 0.927873, 0.666667, 1, 0.217338, -0.00858438, -0.303233, 0.927761, 0.7, 1, 0.216853, -0.00727883, -0.303963, 0.927647, 0.733333, 1, 0.216181, -0.00548657, -0.304964, 0.927487, 0.766667, 1, 0.215287, -0.00313361, -0.306279, 0.927273, 0.8, 1, 0.214135, -0.000141727, -0.307948, 0.926992, 0.833333, 1, 0.212669, 0.00357077, -0.31002, 0.926632, 0.866667, 1, 0.210972, 0.00778135, -0.312367, 0.926206, 0.9, 1, 0.209073, 0.0123786, -0.314925, 0.92572, 0.933333, 1, 0.207009, 0.0172509, -0.317632, 0.925181, 1, 1, 0.202534, 0.027357, -0.323245, 0.923983, 1.03333, 1, 0.200245, 0.0323321, -0.326001, 0.923352, 1.06667, 1, 0.197994, 0.0371094, -0.328643, 0.922721, 1.1, 1, 0.195864, 0.041509, -0.331078, 0.922118, 1.13333, 1, 0.193912, 0.045462, -0.333263, 0.921557, 1.16667, 1, 0.192205, 0.0488617, -0.33514, 0.92106, 1.2, 1, 0.19081, 0.0515994, -0.336649, 0.92065, 1.23333, 1, 0.18987, 0.0534217, -0.337655, 0.920372, 1.26667, 1, 0.189395, 0.0543362, -0.338159, 0.920231)
tracks/52/type = "position_3d"
tracks/52/imported = true
tracks/52/enabled = true
tracks/52/path = NodePath("Skeleton_01/Skeleton3D:RightForeArm")
tracks/52/interp = 1
tracks/52/loop_wrap = true
tracks/52/keys = PackedFloat32Array()
tracks/53/type = "rotation_3d"
tracks/53/imported = true
tracks/53/enabled = true
tracks/53/path = NodePath("Skeleton_01/Skeleton3D:RightForeArm")
tracks/53/interp = 1
tracks/53/loop_wrap = true
tracks/53/keys = PackedFloat32Array(0, 1, -0.102316, -0.695302, 0.0425969, 0.710121, 0.0333333, 1, -0.103003, -0.70319, 0.0366517, 0.702546, 0.0666667, 1, -0.103545, -0.709215, 0.0319377, 0.696615, 0.1, 1, -0.103934, -0.713785, 0.0283246, 0.692031, 0.133333, 1, -0.104145, -0.717278, 0.0256352, 0.688483, 0.166667, 1, -0.104203, -0.719991, 0.0238643, 0.6857, 0.2, 1, -0.103794, -0.722401, 0.0223254, 0.683276, 0.233333, 1, -0.10309, -0.724795, 0.0210147, 0.680884, 0.266667, 1, -0.102077, -0.727508, 0.0197326, 0.678177, 0.3, 1, -0.100377, -0.730987, 0.0179667, 0.674729, 0.333333, 1, -0.0979086, -0.735194, 0.0159853, 0.670559, 0.366667, 1, -0.0946886, -0.739779, 0.013897, 0.666009, 0.4, 1, -0.0906135, -0.743722, 0.0113118, 0.662223, 0.433333, 1, -0.0872434, -0.747362, 0.00962087, 0.658594, 0.466667, 1, -0.0844747, -0.749861, 0.00842548, 0.656126, 0.5, 1, -0.0829145, -0.751193, 0.00815049, 0.654804, 0.6, 1, -0.0816998, -0.750621, 0.00901012, 0.655601, 0.666667, 1, -0.081913, -0.749748, 0.0100569, 0.656557, 0.7, 1, -0.0823744, -0.748976, 0.0109497, 0.657366, 0.733333, 1, -0.0828745, -0.747495, 0.0119481, 0.658969, 0.766667, 1, -0.0835701, -0.745599, 0.0132704, 0.661001, 0.8, 1, -0.0844281, -0.74313, 0.0148956, 0.663633, 0.833333, 1, -0.0856084, -0.740073, 0.0169638, 0.66684, 0.866667, 1, -0.0869566, -0.736566, 0.0192624, 0.670476, 0.9, 1, -0.0887004, -0.732808, 0.0220041, 0.67427, 0.966667, 1, -0.09149, -0.724151, 0.0267249, 0.683024, 1, 1, -0.0931566, -0.719751, 0.0293105, 0.68733, 1.03333, 1, -0.0947849, -0.715357, 0.031795, 0.69157, 1.06667, 1, -0.0963445, -0.711081, 0.0341361, 0.69564, 1.1, 1, -0.0978536, -0.707157, 0.0363257, 0.69931, 1.13333, 1, -0.0992035, -0.703586, 0.0382579, 0.702611, 1.16667, 1, -0.100364, -0.700487, 0.0398969, 0.705445, 1.2, 1, -0.101301, -0.697976, 0.0412057, 0.707722, 1.23333, 1, -0.101883, -0.696346, 0.0420233, 0.709194, 1.26667, 1, -0.102147, -0.695547, 0.0424032, 0.709917)
tracks/54/type = "rotation_3d"
tracks/54/imported = true
tracks/54/enabled = true
tracks/54/path = NodePath("Skeleton_01/Skeleton3D:RightHand")
tracks/54/interp = 1
tracks/54/loop_wrap = true
tracks/54/keys = PackedFloat32Array(0, 1, 0.0251612, 0.0162702, -0.296692, 0.954503, 0.0333333, 1, 0.0268823, 0.0222006, -0.301409, 0.952857, 0.0666667, 1, 0.022301, 0.0278057, -0.305348, 0.951573, 0.1, 1, 0.0132864, 0.0299117, -0.308592, 0.950631, 0.133333, 1, 0.00254851, 0.0271844, -0.31123, 0.949942, 0.166667, 1, -0.00656625, 0.0189242, -0.313261, 0.949456, 0.2, 1, -0.0114488, 0.00716735, -0.314887, 0.949033, 0.233333, 1, -0.00996295, -0.00599489, -0.3162, 0.948621, 0.266667, 1, -0.000762098, -0.01729, -0.317177, 0.948209, 0.3, 1, 0.0163548, -0.0191406, -0.318128, 0.947714, 0.333333, 1, 0.0314198, -0.00195179, -0.318186, 0.947505, 0.366667, 1, 0.0240053, 0.0234349, -0.317351, 0.947715, 0.4, 1, -0.0048798, 0.0263366, -0.315744, 0.948466, 0.433333, 1, -0.0208277, -0.00605522, -0.313919, 0.949202, 0.466667, 1, -0.0123656, -0.0407457, -0.31224, 0.949048, 0.5, 1, -0.000261458, -0.057594, -0.311184, 0.948603, 0.533333, 1, 0.00147565, -0.0594675, -0.311097, 0.948515, 0.566667, 1, 0.000667494, -0.0592221, -0.311133, 0.948519, 0.6, 1, -0.00135364, -0.0585692, -0.311119, 0.948564, 0.633333, 1, -0.00507653, -0.0572295, -0.311027, 0.948663, 0.666667, 1, -0.0108952, -0.0547727, -0.310812, 0.948829, 0.7, 1, -0.0193939, -0.0500931, -0.310434, 0.949076, 0.733333, 1, -0.0299172, -0.0423516, -0.310061, 0.949302, 0.766667, 1, -0.0414926, -0.0303077, -0.30954, 0.949497, 0.8, 1, -0.0522064, -0.0127736, -0.308825, 0.949599, 0.833333, 1, -0.0576358, 0.0107508, -0.307949, 0.949595, 0.866667, 1, -0.054953, 0.0363955, -0.306895, 0.949458, 0.9, 1, -0.0429906, 0.0598715, -0.305757, 0.949252, 0.933333, 1, -0.0231542, 0.0766976, -0.304617, 0.9491, 0.966667, 1, 0.000678609, 0.0819112, -0.303481, 0.94931, 1, 1, 0.0223332, 0.07719, -0.302404, 0.949787, 1.03333, 1, 0.0379689, 0.0653047, -0.301355, 0.950515, 1.06667, 1, 0.0458978, 0.0503048, -0.300356, 0.951393, 1.1, 1, 0.0459192, 0.0366136, -0.299462, 0.952299, 1.13333, 1, 0.0418349, 0.0264955, -0.298659, 0.953074, 1.16667, 1, 0.0361821, 0.0203043, -0.297945, 0.953681, 1.2, 1, 0.0308663, 0.0172983, -0.29734, 0.954116, 1.23333, 1, 0.0272564, 0.0164435, -0.29694, 0.954365, 1.26667, 1, 0.0254882, 0.0162798, -0.296735, 0.954481)
tracks/55/type = "position_3d"
tracks/55/imported = true
tracks/55/enabled = true
tracks/55/path = NodePath("Skeleton_01/Skeleton3D:RightHandIndex1")
tracks/55/interp = 1
tracks/55/loop_wrap = true
tracks/55/keys = PackedFloat32Array()
tracks/56/type = "rotation_3d"
tracks/56/imported = true
tracks/56/enabled = true
tracks/56/path = NodePath("Skeleton_01/Skeleton3D:RightHandIndex1")
tracks/56/interp = 1
tracks/56/loop_wrap = true
tracks/56/keys = PackedFloat32Array()
tracks/57/type = "position_3d"
tracks/57/imported = true
tracks/57/enabled = true
tracks/57/path = NodePath("Skeleton_01/Skeleton3D:RightHandIndex2")
tracks/57/interp = 1
tracks/57/loop_wrap = true
tracks/57/keys = PackedFloat32Array()
tracks/58/type = "rotation_3d"
tracks/58/imported = true
tracks/58/enabled = true
tracks/58/path = NodePath("Skeleton_01/Skeleton3D:RightHandIndex2")
tracks/58/interp = 1
tracks/58/loop_wrap = true
tracks/58/keys = PackedFloat32Array()
tracks/59/type = "position_3d"
tracks/59/imported = true
tracks/59/enabled = true
tracks/59/path = NodePath("Skeleton_01/Skeleton3D:RightHandMiddle1")
tracks/59/interp = 1
tracks/59/loop_wrap = true
tracks/59/keys = PackedFloat32Array()
tracks/60/type = "rotation_3d"
tracks/60/imported = true
tracks/60/enabled = true
tracks/60/path = NodePath("Skeleton_01/Skeleton3D:RightHandMiddle1")
tracks/60/interp = 1
tracks/60/loop_wrap = true
tracks/60/keys = PackedFloat32Array()
tracks/61/type = "position_3d"
tracks/61/imported = true
tracks/61/enabled = true
tracks/61/path = NodePath("Skeleton_01/Skeleton3D:RightHandMiddle2")
tracks/61/interp = 1
tracks/61/loop_wrap = true
tracks/61/keys = PackedFloat32Array()
tracks/62/type = "rotation_3d"
tracks/62/imported = true
tracks/62/enabled = true
tracks/62/path = NodePath("Skeleton_01/Skeleton3D:RightHandMiddle2")
tracks/62/interp = 1
tracks/62/loop_wrap = true
tracks/62/keys = PackedFloat32Array()
tracks/63/type = "position_3d"
tracks/63/imported = true
tracks/63/enabled = true
tracks/63/path = NodePath("Skeleton_01/Skeleton3D:RightHandPinky1")
tracks/63/interp = 1
tracks/63/loop_wrap = true
tracks/63/keys = PackedFloat32Array()
tracks/64/type = "rotation_3d"
tracks/64/imported = true
tracks/64/enabled = true
tracks/64/path = NodePath("Skeleton_01/Skeleton3D:RightHandPinky1")
tracks/64/interp = 1
tracks/64/loop_wrap = true
tracks/64/keys = PackedFloat32Array()
tracks/65/type = "rotation_3d"
tracks/65/imported = true
tracks/65/enabled = true
tracks/65/path = NodePath("Skeleton_01/Skeleton3D:RightHandPinky2")
tracks/65/interp = 1
tracks/65/loop_wrap = true
tracks/65/keys = PackedFloat32Array()
tracks/66/type = "position_3d"
tracks/66/imported = true
tracks/66/enabled = true
tracks/66/path = NodePath("Skeleton_01/Skeleton3D:RightHandProp")
tracks/66/interp = 1
tracks/66/loop_wrap = true
tracks/66/keys = PackedFloat32Array()
tracks/67/type = "position_3d"
tracks/67/imported = true
tracks/67/enabled = true
tracks/67/path = NodePath("Skeleton_01/Skeleton3D:RightHandRing1")
tracks/67/interp = 1
tracks/67/loop_wrap = true
tracks/67/keys = PackedFloat32Array()
tracks/68/type = "rotation_3d"
tracks/68/imported = true
tracks/68/enabled = true
tracks/68/path = NodePath("Skeleton_01/Skeleton3D:RightHandRing1")
tracks/68/interp = 1
tracks/68/loop_wrap = true
tracks/68/keys = PackedFloat32Array()
tracks/69/type = "position_3d"
tracks/69/imported = true
tracks/69/enabled = true
tracks/69/path = NodePath("Skeleton_01/Skeleton3D:RightHandRing2")
tracks/69/interp = 1
tracks/69/loop_wrap = true
tracks/69/keys = PackedFloat32Array()
tracks/70/type = "rotation_3d"
tracks/70/imported = true
tracks/70/enabled = true
tracks/70/path = NodePath("Skeleton_01/Skeleton3D:RightHandRing2")
tracks/70/interp = 1
tracks/70/loop_wrap = true
tracks/70/keys = PackedFloat32Array()
tracks/71/type = "position_3d"
tracks/71/imported = true
tracks/71/enabled = true
tracks/71/path = NodePath("Skeleton_01/Skeleton3D:RightHandThumb1")
tracks/71/interp = 1
tracks/71/loop_wrap = true
tracks/71/keys = PackedFloat32Array()
tracks/72/type = "rotation_3d"
tracks/72/imported = true
tracks/72/enabled = true
tracks/72/path = NodePath("Skeleton_01/Skeleton3D:RightHandThumb1")
tracks/72/interp = 1
tracks/72/loop_wrap = true
tracks/72/keys = PackedFloat32Array()
tracks/73/type = "position_3d"
tracks/73/imported = true
tracks/73/enabled = true
tracks/73/path = NodePath("Skeleton_01/Skeleton3D:RightHandThumb2")
tracks/73/interp = 1
tracks/73/loop_wrap = true
tracks/73/keys = PackedFloat32Array()
tracks/74/type = "rotation_3d"
tracks/74/imported = true
tracks/74/enabled = true
tracks/74/path = NodePath("Skeleton_01/Skeleton3D:RightHandThumb2")
tracks/74/interp = 1
tracks/74/loop_wrap = true
tracks/74/keys = PackedFloat32Array()
tracks/75/type = "position_3d"
tracks/75/imported = true
tracks/75/enabled = true
tracks/75/path = NodePath("Skeleton_01/Skeleton3D:RightUpLeg")
tracks/75/interp = 1
tracks/75/loop_wrap = true
tracks/75/keys = PackedFloat32Array()
tracks/76/type = "rotation_3d"
tracks/76/imported = true
tracks/76/enabled = true
tracks/76/path = NodePath("Skeleton_01/Skeleton3D:RightUpLeg")
tracks/76/interp = 1
tracks/76/loop_wrap = true
tracks/76/keys = PackedFloat32Array(0, 1, 0.667174, -0.545904, -0.459788, 0.21322, 0.0333333, 1, 0.659698, -0.547262, -0.467899, 0.215346, 0.0666667, 1, 0.653778, -0.547825, -0.473889, 0.218842, 0.1, 1, 0.649408, -0.547602, -0.477914, 0.223608, 0.133333, 1, 0.645786, -0.54701, -0.481111, 0.228633, 0.166667, 1, 0.642717, -0.546164, -0.483834, 0.233506, 0.2, 1, 0.63983, -0.545313, -0.486495, 0.237853, 0.233333, 1, 0.636954, -0.544546, -0.489413, 0.241315, 0.266667, 1, 0.634133, -0.543984, -0.492248, 0.24423, 0.3, 1, 0.63152, -0.543557, -0.495152, 0.246074, 0.333333, 1, 0.62936, -0.54306, -0.497976, 0.247007, 0.4, 1, 0.625453, -0.543019, -0.504423, 0.243919, 0.433333, 1, 0.623883, -0.543509, -0.506916, 0.241668, 0.466667, 1, 0.622637, -0.543833, -0.508997, 0.239773, 0.5, 1, 0.621882, -0.543836, -0.510534, 0.238454, 0.533333, 1, 0.621853, -0.544154, -0.511014, 0.236771, 0.566667, 1, 0.622506, -0.544416, -0.510913, 0.234661, 0.6, 1, 0.624237, -0.543557, -0.511115, 0.231594, 0.633333, 1, 0.626065, -0.54262, -0.511466, 0.228054, 0.7, 1, 0.630084, -0.540736, -0.512051, 0.220005, 0.733333, 1, 0.633274, -0.539391, -0.510877, 0.216855, 0.766667, 1, 0.636684, -0.538056, -0.509297, 0.213882, 0.8, 1, 0.640268, -0.536707, -0.507343, 0.211202, 0.833333, 1, 0.643401, -0.536294, -0.505125, 0.208022, 0.866667, 1, 0.647104, -0.535681, -0.501906, 0.205896, 0.9, 1, 0.651409, -0.534883, -0.497483, 0.205126, 0.933333, 1, 0.65554, -0.534047, -0.493098, 0.204732, 0.966667, 1, 0.659492, -0.533326, -0.488285, 0.205457, 1, 1, 0.663138, -0.532727, -0.483247, 0.207176, 1.03333, 1, 0.665686, -0.533211, -0.478224, 0.209405, 1.06667, 1, 0.66762, -0.534401, -0.473433, 0.211095, 1.1, 1, 0.668986, -0.536074, -0.469097, 0.212202, 1.16667, 1, 0.67026, -0.539837, -0.461988, 0.214232, 1.2, 1, 0.670232, -0.541993, -0.459006, 0.215282, 1.23333, 1, 0.670169, -0.543388, -0.457216, 0.21577, 1.26667, 1, 0.670026, -0.544248, -0.45614, 0.216323)
tracks/77/type = "position_3d"
tracks/77/imported = true
tracks/77/enabled = true
tracks/77/path = NodePath("Skeleton_01/Skeleton3D:RightLeg")
tracks/77/interp = 1
tracks/77/loop_wrap = true
tracks/77/keys = PackedFloat32Array()
tracks/78/type = "rotation_3d"
tracks/78/imported = true
tracks/78/enabled = true
tracks/78/path = NodePath("Skeleton_01/Skeleton3D:RightLeg")
tracks/78/interp = 1
tracks/78/loop_wrap = true
tracks/78/keys = PackedFloat32Array(0, 1, 0.0430863, 0.0280434, -0.943006, 0.32878, 0.0333333, 1, 0.0423959, 0.0262051, -0.949326, 0.310315, 0.0666667, 1, 0.0439255, 0.0244071, -0.953981, 0.295627, 0.1, 1, 0.0472928, 0.022598, -0.957363, 0.284093, 0.133333, 1, 0.0503276, 0.0209876, -0.959882, 0.275053, 0.166667, 1, 0.0523078, 0.0195724, -0.961779, 0.268069, 0.2, 1, 0.0536068, 0.0183611, -0.963374, 0.262106, 0.233333, 1, 0.0553649, 0.0175837, -0.964797, 0.256501, 0.266667, 1, 0.0589119, 0.0171514, -0.96614, 0.250615, 0.3, 1, 0.0645479, 0.0167119, -0.96754, 0.243764, 0.333333, 1, 0.0704746, 0.0160784, -0.968844, 0.236888, 0.366667, 1, 0.0735445, 0.015351, -0.970112, 0.230734, 0.4, 1, 0.0741296, 0.01475, -0.971322, 0.225434, 0.433333, 1, 0.0734308, 0.0143826, -0.972315, 0.221371, 0.466667, 1, 0.071259, 0.0142035, -0.972868, 0.219654, 0.5, 1, 0.0670797, 0.0139831, -0.973053, 0.220166, 0.533333, 1, 0.0626221, 0.0136361, -0.973256, 0.220602, 0.566667, 1, 0.0596215, 0.0133806, -0.973357, 0.221003, 0.633333, 1, 0.0584727, 0.0135748, -0.973274, 0.221663, 0.666667, 1, 0.0566879, 0.0139225, -0.97332, 0.221904, 0.7, 1, 0.0559286, 0.0143129, -0.973198, 0.222606, 0.733333, 1, 0.0564061, 0.0149062, -0.97244, 0.225735, 0.766667, 1, 0.0562275, 0.015573, -0.971477, 0.229845, 0.8, 1, 0.0546071, 0.0162132, -0.970306, 0.235078, 0.833333, 1, 0.0513728, 0.0169367, -0.968876, 0.241563, 0.866667, 1, 0.0490668, 0.0176013, -0.967122, 0.248912, 0.9, 1, 0.0483413, 0.0181497, -0.965051, 0.256925, 0.933333, 1, 0.0470649, 0.0187324, -0.962809, 0.26539, 1, 1, 0.0451962, 0.0199215, -0.957884, 0.282877, 1.03333, 1, 0.0440762, 0.0205072, -0.955356, 0.29143, 1.06667, 1, 0.0435311, 0.0210707, -0.952835, 0.299612, 1.1, 1, 0.0428961, 0.0215874, -0.950457, 0.307124, 1.13333, 1, 0.0421706, 0.022049, -0.948279, 0.313852, 1.16667, 1, 0.0416824, 0.0224405, -0.946363, 0.31962, 1.2, 1, 0.0409702, 0.022754, -0.944809, 0.324253, 1.23333, 1, 0.0402831, 0.0229463, -0.943828, 0.327169, 1.26667, 1, 0.0399419, 0.0230445, -0.943358, 0.328556)
tracks/79/type = "position_3d"
tracks/79/imported = true
tracks/79/enabled = true
tracks/79/path = NodePath("Skeleton_01/Skeleton3D:RightFoot")
tracks/79/interp = 1
tracks/79/loop_wrap = true
tracks/79/keys = PackedFloat32Array()
tracks/80/type = "rotation_3d"
tracks/80/imported = true
tracks/80/enabled = true
tracks/80/path = NodePath("Skeleton_01/Skeleton3D:RightFoot")
tracks/80/interp = 1
tracks/80/loop_wrap = true
tracks/80/keys = PackedFloat32Array(0, 1, 0.258534, -0.640917, 0.527709, 0.493871, 0.0333333, 1, 0.246243, -0.642052, 0.530399, 0.495793, 0.0666667, 1, 0.235788, -0.643151, 0.532882, 0.496787, 0.1, 1, 0.227015, -0.644105, 0.534969, 0.497395, 0.133333, 1, 0.219991, -0.644577, 0.536503, 0.498286, 0.166667, 1, 0.215094, -0.643965, 0.53794, 0.499664, 0.233333, 1, 0.206705, -0.642537, 0.540146, 0.502656, 0.266667, 1, 0.20141, -0.642764, 0.541618, 0.502929, 0.3, 1, 0.194462, -0.644028, 0.542904, 0.502661, 0.333333, 1, 0.187805, -0.644669, 0.543658, 0.503555, 0.366667, 1, 0.18295, -0.643698, 0.54344, 0.506809, 0.4, 1, 0.179656, -0.641649, 0.542677, 0.511382, 0.433333, 1, 0.177694, -0.639258, 0.541795, 0.515977, 0.466667, 1, 0.177479, -0.636688, 0.541235, 0.519802, 0.5, 1, 0.178719, -0.633856, 0.541079, 0.522991, 0.533333, 1, 0.180566, -0.632128, 0.54055, 0.52499, 0.566667, 1, 0.182126, -0.631102, 0.54007, 0.52618, 0.666667, 1, 0.183669, -0.631072, 0.539676, 0.526083, 0.7, 1, 0.184959, -0.631737, 0.539138, 0.525385, 0.733333, 1, 0.186384, -0.632611, 0.538519, 0.524463, 0.766667, 1, 0.188729, -0.633502, 0.537487, 0.523607, 0.8, 1, 0.192306, -0.634048, 0.535926, 0.523245, 0.833333, 1, 0.197678, -0.633757, 0.534916, 0.522628, 0.866667, 1, 0.203489, -0.633694, 0.533775, 0.52164, 0.9, 1, 0.209537, -0.633893, 0.532796, 0.520002, 0.933333, 1, 0.21606, -0.63397, 0.53181, 0.518245, 1.03333, 1, 0.236206, -0.634095, 0.529715, 0.511402, 1.06667, 1, 0.242551, -0.634106, 0.529422, 0.508714, 1.1, 1, 0.247816, -0.634701, 0.528417, 0.506476, 1.13333, 1, 0.252572, -0.635193, 0.527533, 0.504426, 1.16667, 1, 0.256563, -0.635701, 0.526753, 0.502584, 1.2, 1, 0.259969, -0.635907, 0.526239, 0.501109, 1.23333, 1, 0.262094, -0.636089, 0.5257, 0.500337, 1.26667, 1, 0.263003, -0.636288, 0.525291, 0.500037)
tracks/81/type = "position_3d"
tracks/81/imported = true
tracks/81/enabled = true
tracks/81/path = NodePath("Skeleton_01/Skeleton3D:RightToeBase")
tracks/81/interp = 1
tracks/81/loop_wrap = true
tracks/81/keys = PackedFloat32Array()
tracks/82/type = "rotation_3d"
tracks/82/imported = true
tracks/82/enabled = true
tracks/82/path = NodePath("Skeleton_01/Skeleton3D:RightToeBase")
tracks/82/interp = 1
tracks/82/loop_wrap = true
tracks/82/keys = PackedFloat32Array(0, 1, -0.0035225, 0.999923, 8.6598e-05, 0.0118776, 0.0333333, 1, -0.00291823, 0.999943, -0.00330956, 0.00976268, 0.0666667, 1, -0.00248735, 0.999946, -0.00605796, 0.0080323, 0.1, 1, -0.00229961, 0.99994, -0.00826099, 0.00688384, 0.133333, 1, -0.00209481, 0.999931, -0.0100196, 0.00574411, 0.166667, 1, -0.00189629, 0.999922, -0.0113963, 0.00470598, 0.2, 1, -0.00181158, 0.999911, -0.012573, 0.00403194, 0.233333, 1, -0.00168443, 0.9999, -0.0136434, 0.00337651, 0.266667, 1, -0.00150793, 0.999887, -0.0147104, 0.00274221, 0.3, 1, -0.00126756, 0.999871, -0.0158711, 0.00213501, 0.333333, 1, -0.000976889, 0.999855, -0.0169025, 0.00154915, 0.4, 1, -0.000828822, 0.999832, -0.018219, 0.00167988, 0.533333, 1, -0.000784766, 0.999821, -0.0187514, 0.00257708, 0.7, 1, -0.000957864, 0.999833, -0.0179126, 0.00360315, 0.766667, 1, -0.00102597, 0.999852, -0.0167171, 0.00401466, 0.8, 1, -0.00105259, 0.999865, -0.0158501, 0.00422372, 0.833333, 1, -0.00107268, 0.99988, -0.0147739, 0.00443596, 0.866667, 1, -0.00108888, 0.999897, -0.0135536, 0.00464989, 0.9, 1, -0.00110185, 0.999913, -0.0122179, 0.00486596, 0.933333, 1, -0.00111391, 0.999928, -0.0108005, 0.00508213, 0.966667, 1, -0.00142077, 0.999937, -0.00933562, 0.00598907, 1, 1, -0.00173187, 0.999944, -0.00785936, 0.00689464, 1.03333, 1, -0.00204742, 0.999947, -0.00640767, 0.00779839, 1.06667, 1, -0.00236917, 0.999947, -0.00500712, 0.00869972, 1.1, 1, -0.00255191, 0.999947, -0.00372436, 0.0092645, 1.13333, 1, -0.00274236, 0.999945, -0.00256698, 0.00982607, 1.16667, 1, -0.00294, 0.999941, -0.00157085, 0.0103844, 1.23333, 1, -0.00336443, 0.999928, -0.000232807, 0.0114875, 1.26667, 1, -0.0035108, 0.999924, 3.59271e-05, 0.0118461)

[sub_resource type="Animation" id="Animation_07qfg"]
length = 0.001

[sub_resource type="Animation" id="Animation_ai2pb"]
resource_name = "bomb_hold_run"
length = 1.3
loop_mode = 1
tracks/0/type = "position_3d"
tracks/0/imported = true
tracks/0/enabled = true
tracks/0/path = NodePath("Skeleton_01/Skeleton3D:Root")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = PackedFloat32Array(0, 1, -0.0358974, -0.0849952, -0.00570391, 0.0333333, 1, -0.0358974, -0.0849952, -0.00570391, 0.0666667, 1, -0.02917, -0.079758, -0.00443298, 0.1, 1, -0.0222564, -0.0717905, -0.00432734, 0.133333, 1, -0.0180192, -0.0628507, -0.00636184, 0.166667, 1, -0.0148511, -0.0597209, -0.0106837, 0.2, 1, -0.0145615, -0.0623763, -0.0133975, 0.233333, 1, -0.0148967, -0.0728797, -0.0159127, 0.266667, 1, -0.0104826, -0.0803, -0.00876819, 0.3, 1, -0.00244311, -0.0841128, -0.00370242, 0.333333, 1, 0.0049384, -0.0848079, -0.00361713, 0.366667, 1, 0.00864353, -0.082606, -0.00416358, 0.4, 1, 0.00810362, -0.0768207, -0.0184652, 0.433333, 1, 0.00986537, -0.0682736, -0.0452078, 0.466667, 1, 0.0118917, -0.0617555, -0.0642006, 0.5, 1, 0.00355134, -0.0659241, -0.0640828, 0.533333, 1, -0.00466835, -0.0785818, -0.0442557, 0.566667, 1, -0.013747, -0.0887992, -0.0207085, 0.6, 1, -0.0262761, -0.0921902, -0.010056, 0.633333, 1, -0.0354477, -0.0902537, -0.00819892, 0.666667, 1, -0.0358974, -0.0849951, -0.00570397, 0.7, 1, -0.0291615, -0.0798722, -0.00441491, 0.733333, 1, -0.0221733, -0.0718738, -0.00429106, 0.766667, 1, -0.0179514, -0.0628365, -0.00636351, 0.8, 1, -0.0148513, -0.0597211, -0.0106835, 0.833333, 1, -0.014059, -0.0620683, -0.0130194, 0.866667, 1, -0.0149748, -0.0724429, -0.0152964, 0.9, 1, -0.0107986, -0.0800608, -0.0083965, 0.933333, 1, -0.00261473, -0.084028, -0.00357338, 0.966667, 1, 0.0050414, -0.0848709, -0.00370539, 1, 1, 0.00872238, -0.0826311, -0.0042212, 1.03333, 1, 0.00810486, -0.0768198, -0.0184716, 1.06667, 1, 0.00986278, -0.0682734, -0.0452218, 1.1, 1, 0.011891, -0.0617503, -0.064221, 1.13333, 1, 0.00373521, -0.0662761, -0.0645236, 1.16667, 1, -0.00478395, -0.0787767, -0.04474, 1.2, 1, -0.013904, -0.08901, -0.0210701, 1.23333, 1, -0.0264421, -0.0923287, -0.0103132, 1.26667, 1, -0.0355343, -0.0903099, -0.0082896, 1.3, 1, -0.0358974, -0.0849951, -0.0057039)
tracks/1/type = "rotation_3d"
tracks/1/imported = true
tracks/1/enabled = true
tracks/1/path = NodePath("Skeleton_01/Skeleton3D:Root")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = PackedFloat32Array(0, 1, -0.00709261, -0.0649041, -0.0486441, 0.99668, 0.0333333, 1, -0.00709261, -0.0649041, -0.0486441, 0.99668, 0.0666667, 1, -0.00662771, -0.0752932, -0.0381553, 0.996409, 0.1, 1, -0.00455908, -0.0998681, -0.029018, 0.994567, 0.133333, 1, 9.74348e-05, -0.126326, -0.0241268, 0.991695, 0.166667, 1, 0.00454276, -0.143719, -0.0207586, 0.98939, 0.2, 1, 0.0046658, -0.144585, -0.0205669, 0.989268, 0.233333, 1, -0.00407178, -0.129145, -0.0193898, 0.991428, 0.266667, 1, -0.0177519, -0.0988682, -0.0118185, 0.994872, 0.3, 1, -0.0257605, -0.0678947, -0.00106767, 0.997359, 0.333333, 1, -0.0250075, -0.0475292, 0.00564742, 0.998541, 0.366667, 1, -0.0209912, -0.0370422, 0.00772201, 0.999063, 0.4, 1, 0.00467688, -0.0317519, 0.00377378, 0.999478, 0.433333, 1, 0.0486728, -0.0316563, 0.00345726, 0.998307, 0.466667, 1, 0.0791944, -0.0312419, 0.00535533, 0.996355, 0.5, 1, 0.0762352, -0.0257188, -0.00384262, 0.996751, 0.533333, 1, 0.0477363, -0.0229609, -0.0134214, 0.998506, 0.566667, 1, 0.0101223, -0.0303416, -0.025441, 0.999165, 0.6, 1, -0.010448, -0.0449595, -0.0391037, 0.998169, 0.633333, 1, -0.0107336, -0.0592323, -0.0487086, 0.996997, 0.666667, 1, -0.00709261, -0.0649041, -0.0486442, 0.99668, 0.7, 1, -0.00661469, -0.0752639, -0.0383105, 0.996406, 0.733333, 1, -0.00455109, -0.0998331, -0.0290868, 0.994569, 0.766667, 1, 9.33678e-05, -0.12631, -0.0240924, 0.991698, 0.8, 1, 0.00454227, -0.143718, -0.0207588, 0.98939, 0.833333, 1, 0.0047047, -0.144463, -0.0208604, 0.989279, 0.866667, 1, -0.00401733, -0.128747, -0.0198516, 0.991471, 0.9, 1, -0.0177343, -0.0985622, -0.0121203, 0.994899, 0.933333, 1, -0.025756, -0.0677495, -0.00121422, 0.997369, 0.966667, 1, -0.025011, -0.0475372, 0.00577802, 0.99854, 1, 1, -0.0209916, -0.0370177, 0.0077869, 0.999064, 1.03333, 1, 0.00468592, -0.0316864, 0.00377437, 0.99948, 1.06667, 1, 0.0486962, -0.0315774, 0.0034619, 0.998308, 1.1, 1, 0.0792237, -0.0311665, 0.00535794, 0.996355, 1.13333, 1, 0.0762659, -0.0255492, -0.00416236, 0.996751, 1.16667, 1, 0.0477579, -0.0228165, -0.0137847, 0.998503, 1.2, 1, 0.0101395, -0.0300913, -0.0256575, 0.999166, 1.23333, 1, -0.0104321, -0.0447891, -0.0392893, 0.998169, 1.26667, 1, -0.0107247, -0.0591778, -0.0488141, 0.996996, 1.3, 1, -0.00709261, -0.0649041, -0.0486442, 0.99668)
tracks/2/type = "rotation_3d"
tracks/2/imported = true
tracks/2/enabled = true
tracks/2/path = NodePath("Skeleton_01/Skeleton3D:LeftUpLeg")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = PackedFloat32Array(0, 1, 0.695939, 0.261498, 0.643342, 0.182754, 0.0333333, 1, 0.695939, 0.261498, 0.643342, 0.182754, 0.0666667, 1, 0.690131, 0.306796, 0.605361, 0.251262, 0.1, 1, 0.683506, 0.331798, 0.588805, 0.27575, 0.133333, 1, 0.680318, 0.341829, 0.589467, 0.269904, 0.166667, 1, 0.676393, 0.348259, 0.593083, 0.263555, 0.2, 1, 0.675751, 0.343403, 0.602735, 0.249289, 0.233333, 1, 0.685812, 0.334033, 0.587792, 0.269415, 0.266667, 1, 0.708642, 0.302666, 0.581242, 0.26149, 0.3, 1, 0.734735, 0.247891, 0.587155, 0.232298, 0.333333, 1, 0.755549, 0.177882, 0.603277, 0.183194, 0.366667, 1, 0.768477, 0.0924638, 0.622975, 0.113118, 0.4, 1, 0.771913, 0.0157425, 0.634104, 0.0425959, 0.433333, 1, -0.769984, 0.0623763, -0.634673, 0.0205789, 0.466667, 1, -0.757929, 0.138257, -0.632343, 0.0810548, 0.5, 1, -0.746728, 0.147317, -0.64059, 0.101683, 0.533333, 1, -0.740828, 0.0869089, -0.661618, 0.0767025, 0.566667, 1, -0.729357, 0.00702098, -0.682689, 0.0438638, 0.6, 1, -0.731314, -0.0415367, -0.680728, 0.00794267, 0.633333, 1, 0.723601, 0.148143, 0.668968, 0.083286, 0.666667, 1, 0.695939, 0.261498, 0.643342, 0.182755, 0.7, 1, 0.687929, 0.309726, 0.607498, 0.248534, 0.733333, 1, 0.681954, 0.334721, 0.590641, 0.272109, 0.766667, 1, 0.68009, 0.342621, 0.590015, 0.268274, 0.8, 1, 0.676393, 0.348258, 0.593083, 0.263555, 0.833333, 1, 0.676228, 0.342006, 0.602868, 0.249595, 0.866667, 1, 0.686389, 0.332709, 0.588216, 0.268657, 0.9, 1, 0.708799, 0.302201, 0.5817, 0.260584, 0.933333, 1, 0.734715, 0.247795, 0.587384, 0.231885, 0.966667, 1, 0.755572, 0.178007, 0.603121, 0.183493, 1, 1, 0.768522, 0.0925174, 0.622876, 0.113313, 1.03333, 1, 0.771909, 0.0157705, 0.634107, 0.0426046, 1.06667, 1, -0.76998, 0.0623233, -0.634684, 0.0205691, 1.1, 1, -0.757932, 0.138196, -0.632356, 0.0810392, 1.13333, 1, -0.744826, 0.15167, -0.640758, 0.107984, 1.16667, 1, -0.739877, 0.0866326, -0.66246, 0.0788958, 1.2, 1, -0.729908, 0.00883223, -0.682016, 0.0448321, 1.23333, 1, -0.732015, -0.0400798, -0.680056, 0.00844046, 1.26667, 1, 0.723878, 0.147479, 0.668855, 0.082964, 1.3, 1, 0.695939, 0.261498, 0.643342, 0.182755)
tracks/3/type = "rotation_3d"
tracks/3/imported = true
tracks/3/enabled = true
tracks/3/path = NodePath("Skeleton_01/Skeleton3D:LeftLeg")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = PackedFloat32Array(0, 1, -0.0242027, -0.0154317, 0.868811, 0.494311, 0.0333333, 1, -0.0242027, -0.0154317, 0.868811, 0.494311, 0.0666667, 1, -0.0116867, -0.0228494, 0.816187, 0.577218, 0.1, 1, -0.0118796, -0.0220503, 0.721935, 0.691508, 0.133333, 1, -0.0171614, -0.012707, 0.610161, 0.79199, 0.166667, 1, -0.0192533, -0.00452846, 0.535496, 0.844306, 0.2, 1, -0.0242713, 0.00843755, 0.483266, 0.875096, 0.233333, 1, -0.0204615, -0.00781617, 0.59716, 0.801823, 0.266667, 1, -0.0280647, -0.00328388, 0.666631, 0.744852, 0.3, 1, -0.0321024, -0.00132506, 0.699306, 0.7141, 0.333333, 1, -0.0322812, -0.00133001, 0.702115, 0.71133, 0.366667, 1, -0.0266041, -0.0052002, 0.673495, 0.738695, 0.4, 1, -0.0207747, -0.00823001, 0.610489, 0.791709, 0.433333, 1, -0.0108748, -0.0179846, 0.503059, 0.863997, 0.466667, 1, -0.00636822, -0.0229969, 0.399902, 0.916247, 0.5, 1, -0.0126534, -0.0141994, 0.468116, 0.883462, 0.533333, 1, -0.0219715, -0.00809704, 0.616639, 0.786898, 0.566667, 1, -0.0282824, -0.0107763, 0.793391, 0.607959, 0.6, 1, -0.0282855, -0.0131117, 0.851385, 0.523614, 0.633333, 1, -0.0261603, -0.0148993, 0.87955, 0.474854, 0.666667, 1, -0.0242024, -0.0154319, 0.868811, 0.494311, 0.7, 1, -0.0158667, -0.0199294, 0.815992, 0.577502, 0.733333, 1, -0.0167927, -0.0173849, 0.721807, 0.691673, 0.766667, 1, -0.0191176, -0.0101817, 0.609701, 0.792336, 0.8, 1, -0.0192537, -0.00452737, 0.535494, 0.844307, 0.833333, 1, -0.0240164, 0.00801349, 0.482854, 0.875335, 0.866667, 1, -0.0205102, -0.00773544, 0.596705, 0.802161, 0.9, 1, -0.0283118, -0.00299168, 0.666364, 0.745083, 0.933333, 1, -0.0322364, -0.00117248, 0.699173, 0.714225, 0.966667, 1, -0.0322707, -0.00133005, 0.702272, 0.711176, 1, 1, -0.0266072, -0.00518111, 0.673586, 0.738612, 1.03333, 1, -0.0208022, -0.00817057, 0.610499, 0.791702, 1.06667, 1, -0.0109009, -0.0179056, 0.503087, 0.863982, 1.1, 1, -0.00638402, -0.0229229, 0.399932, 0.916236, 1.13333, 1, -0.0115404, -0.0160216, 0.460594, 0.887391, 1.16667, 1, -0.022058, -0.00790351, 0.615821, 0.787538, 1.2, 1, -0.0281614, -0.0108589, 0.792802, 0.608731, 1.23333, 1, -0.0282159, -0.0131587, 0.851048, 0.524165, 1.26667, 1, -0.0260773, -0.0149494, 0.879425, 0.475088, 1.3, 1, -0.0242025, -0.0154318, 0.868811, 0.49431)
tracks/4/type = "rotation_3d"
tracks/4/imported = true
tracks/4/enabled = true
tracks/4/path = NodePath("Skeleton_01/Skeleton3D:LeftFoot")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = PackedFloat32Array(0, 1, 0.601631, 0.390712, -0.540109, 0.440076, 0.0333333, 1, 0.601631, 0.390712, -0.540109, 0.440076, 0.0666667, 1, 0.548445, 0.336649, -0.585223, 0.493346, 0.1, 1, 0.567436, 0.320741, -0.576547, 0.492682, 0.133333, 1, 0.597343, 0.389665, -0.532699, 0.455604, 0.166667, 1, 0.55898, 0.46566, -0.513886, 0.454558, 0.2, 1, 0.479684, 0.532048, -0.448653, 0.534358, 0.233333, 1, 0.531622, 0.513316, -0.480769, 0.471959, 0.266667, 1, 0.588058, 0.475525, -0.521541, 0.395042, 0.3, 1, 0.627014, 0.443877, -0.559258, 0.31154, 0.333333, 1, 0.658578, 0.400873, -0.589368, 0.24129, 0.366667, 1, 0.683984, 0.353953, -0.609806, 0.187136, 0.433333, 1, 0.661409, 0.354222, -0.634058, 0.187181, 0.466667, 1, 0.633555, 0.415304, -0.611856, 0.227516, 0.5, 1, 0.603698, 0.53264, -0.517648, 0.289627, 0.533333, 1, 0.497554, 0.595232, -0.456528, 0.435569, 0.566667, 1, 0.517506, 0.561119, -0.469719, 0.443506, 0.6, 1, 0.590231, 0.458235, -0.526892, 0.405009, 0.633333, 1, 0.611202, 0.404829, -0.542413, 0.410285, 0.666667, 1, 0.601631, 0.390712, -0.540109, 0.440076, 0.7, 1, 0.583856, 0.335216, -0.557562, 0.485661, 0.733333, 1, 0.607892, 0.328356, -0.544412, 0.475674, 0.766667, 1, 0.609654, 0.396222, -0.520778, 0.447349, 0.8, 1, 0.558981, 0.46566, -0.513886, 0.454557, 0.833333, 1, 0.480591, 0.532527, -0.448266, 0.53339, 0.866667, 1, 0.532523, 0.513109, -0.480964, 0.470969, 0.9, 1, 0.588361, 0.475143, -0.521835, 0.394662, 0.933333, 1, 0.627065, 0.443735, -0.559356, 0.311464, 0.966667, 1, 0.658569, 0.400963, -0.589302, 0.241328, 1, 1, 0.684009, 0.354043, -0.609729, 0.187124, 1.03333, 1, 0.678624, 0.338154, -0.627903, 0.175667, 1.06667, 1, 0.661424, 0.354233, -0.634039, 0.187167, 1.1, 1, 0.633567, 0.415317, -0.611838, 0.227506, 1.13333, 1, 0.596245, 0.527892, -0.533515, 0.284927, 1.16667, 1, 0.472458, 0.590299, -0.480909, 0.443911, 1.2, 1, 0.500893, 0.55697, -0.487316, 0.448791, 1.23333, 1, 0.583986, 0.448959, -0.535402, 0.413209, 1.26667, 1, 0.612611, 0.396361, -0.54221, 0.41667, 1.3, 1, 0.601631, 0.390712, -0.540109, 0.440076)
tracks/5/type = "rotation_3d"
tracks/5/imported = true
tracks/5/enabled = true
tracks/5/path = NodePath("Skeleton_01/Skeleton3D:LeftToeBase")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = PackedFloat32Array(0, 1, -0.00244427, 0.948485, 0.31636, 0.0169229, 0.0333333, 1, -0.00244427, 0.948485, 0.31636, 0.0169229, 0.0666667, 1, 0.00512965, -0.978351, -0.186938, 0.08864, 0.1, 1, 0.000655724, -0.994301, -0.106478, 0.00527231, 0.133333, 1, 0.000845354, -0.991445, -0.130367, 0.00638542, 0.166667, 1, 0.000947872, -0.989658, -0.143273, 0.00698685, 0.2, 1, 0.000871882, -0.991023, -0.133527, 0.00653272, 0.233333, 1, -0.000187478, -0.999982, -0.00593686, 0.000282655, 0.333333, 1, -0.000187196, -0.999982, -0.00593687, 0.000282178, 0.366667, 1, -6.97313e-05, -0.999894, -0.0144962, 0.00100431, 0.4, 1, 0.000228455, -0.998691, -0.0510741, 0.00279305, 0.433333, 1, 0.000699097, -0.994036, -0.108904, 0.00562123, 0.466667, 1, 0.00164733, -0.973846, -0.226916, 0.0113985, 0.5, 1, 0.00545622, -0.906902, -0.420438, 0.027045, 0.533333, 1, -0.00012252, -0.741957, -0.663995, 0.0927954, 0.566667, 1, 0.016378, 0.915579, 0.401745, 0.00686326, 0.6, 1, -0.00226537, -0.949971, -0.3118, 0.0181578, 0.633333, 1, -0.00133241, 0.925485, 0.378673, 0.00905352, 0.666667, 1, -0.00244425, 0.948486, 0.316358, 0.0169229, 0.7, 1, 0.00303594, -0.984926, -0.167967, 0.0412178, 0.733333, 1, 0.000655992, -0.994301, -0.106478, 0.00527234, 0.766667, 1, 0.000845225, -0.991445, -0.130367, 0.0063854, 0.8, 1, 0.000947878, -0.989658, -0.143273, 0.00698685, 0.833333, 1, 0.000871868, -0.991023, -0.133527, 0.00653272, 0.866667, 1, -0.000187479, -0.999982, -0.00593699, 0.000282654, 0.966667, 1, -0.000187197, -0.999982, -0.00593673, 0.000282177, 1, 1, -6.97273e-05, -0.999894, -0.0144961, 0.00100431, 1.03333, 1, 0.000228456, -0.998691, -0.0510741, 0.00279305, 1.06667, 1, 0.000699096, -0.994036, -0.108904, 0.00562123, 1.1, 1, 0.00164734, -0.973846, -0.226916, 0.0113985, 1.13333, 1, 0.00974968, -0.91372, -0.404707, 0.0351109, 1.16667, 1, -6.95722e-05, -0.739519, -0.663811, 0.111654, 1.2, 1, 0.0194205, 0.926542, 0.375067, 0.0216279, 1.23333, 1, -0.00214503, -0.95004, -0.311654, 0.0170852, 1.26667, 1, -0.00332692, 0.925752, 0.377565, 0.0204351, 1.3, 1, -0.00244426, 0.948486, 0.316359, 0.0169229)
tracks/6/type = "rotation_3d"
tracks/6/imported = true
tracks/6/enabled = true
tracks/6/path = NodePath("Skeleton_01/Skeleton3D:Spine")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = PackedFloat32Array(0, 1, 0.100769, 0.00216593, 0.00932806, 0.994864, 0.0333333, 1, 0.100769, 0.00216593, 0.00932806, 0.994864, 0.0666667, 1, 0.0994257, 0.0152454, 0.00836501, 0.994893, 0.1, 1, 0.0994452, 0.032972, 0.00719559, 0.994471, 0.133333, 1, 0.0980092, 0.0487888, 0.00682059, 0.993965, 0.166667, 1, 0.094687, 0.0571423, 0.00573052, 0.993849, 0.2, 1, 0.0927696, 0.0543673, 0.00625249, 0.994183, 0.233333, 1, 0.0960919, 0.0439109, 0.00474738, 0.994392, 0.266667, 1, 0.102261, 0.0287607, -0.00286693, 0.994338, 0.3, 1, 0.104548, 0.0154303, -0.0120489, 0.994327, 0.333333, 1, 0.101835, 0.00589433, -0.0171522, 0.994636, 0.366667, 1, 0.0986201, -0.000478945, -0.0197412, 0.994929, 0.4, 1, 0.0913348, -0.00686295, -0.0195249, 0.995605, 0.433333, 1, 0.0780953, -0.0125426, -0.0172417, 0.996718, 0.466667, 1, 0.067679, -0.0161247, -0.0153192, 0.997459, 0.5, 1, 0.0687782, -0.0204047, -0.0101237, 0.997372, 0.533333, 1, 0.0795694, -0.0221136, -0.00898465, 0.996544, 0.566667, 1, 0.0947577, -0.020523, -0.0050088, 0.995276, 0.6, 1, 0.103365, -0.0167717, 0.00157962, 0.994501, 0.633333, 1, 0.102804, -0.00839662, 0.0072438, 0.99464, 0.666667, 1, 0.100769, 0.0021659, 0.00932809, 0.994864, 0.7, 1, 0.0994228, 0.015198, 0.00852149, 0.994893, 0.733333, 1, 0.0994432, 0.0329302, 0.00726348, 0.994472, 0.766667, 1, 0.0980109, 0.0487761, 0.00678405, 0.993966, 0.8, 1, 0.0932429, 0.0550048, 0.0055789, 0.994107, 0.833333, 1, 0.0896732, 0.0496313, 0.00622811, 0.994714, 0.866667, 1, 0.091264, 0.0363701, 0.0045841, 0.995152, 0.9, 1, 0.0956569, 0.0190189, -0.00365006, 0.995226, 0.933333, 1, 0.0963087, 0.00395283, -0.0133778, 0.995254, 0.966667, 1, 0.0924569, -0.00672977, -0.0188729, 0.995515, 1, 1, 0.0888685, -0.0136535, -0.0213769, 0.99572, 1.03333, 1, 0.0820392, -0.019691, -0.0208533, 0.996216, 1.06667, 1, 0.0697725, -0.0242251, -0.0180897, 0.997105, 1.1, 1, 0.0606293, -0.0260589, -0.0158134, 0.997695, 1.13333, 1, 0.0632887, -0.0283898, -0.0102964, 0.997538, 1.16667, 1, 0.0756912, -0.0278568, -0.00918675, 0.9967, 1.2, 1, 0.0924033, -0.0242484, -0.00532308, 0.995412, 1.23333, 1, 0.102251, -0.0186429, 0.0014603, 0.994583, 1.3, 1, 0.100769, 0.00216589, 0.00932812, 0.994864)
tracks/7/type = "rotation_3d"
tracks/7/imported = true
tracks/7/enabled = true
tracks/7/path = NodePath("Skeleton_01/Skeleton3D:Spine1")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = PackedFloat32Array(0, 1, -0.00345011, 0.00695261, 0.01713, 0.999823, 0.0333333, 1, -0.00345011, 0.00695261, 0.01713, 0.999823, 0.0666667, 1, -0.00607042, 0.0304666, 0.0100512, 0.999467, 0.1, 1, -0.00579491, 0.0627664, 0.00393717, 0.998004, 0.133333, 1, -0.00836217, 0.091897, 0.000257192, 0.995733, 0.166667, 1, -0.0150485, 0.105956, 0.000481477, 0.994257, 0.2, 1, -0.0189594, 0.0986638, 0.00177238, 0.994939, 0.233333, 1, -0.0123385, 0.0753787, -0.00302343, 0.997074, 0.266667, 1, -0.000134854, 0.0471948, -0.0159717, 0.998758, 0.3, 1, 0.00429985, 0.0241255, -0.0300923, 0.999247, 0.333333, 1, -0.00125339, 0.00841281, -0.0377399, 0.999251, 0.366667, 1, -0.0077402, -0.00340101, -0.0360876, 0.999313, 0.4, 1, -0.0223363, -0.0136444, -0.0271576, 0.999288, 0.433333, 1, -0.0489106, -0.0187341, -0.0210368, 0.998406, 0.466667, 1, -0.0698588, -0.0203078, -0.0215697, 0.997117, 0.5, 1, -0.0678241, -0.0217291, -0.0205677, 0.997249, 0.533333, 1, -0.0461423, -0.0222834, -0.0148304, 0.998576, 0.566667, 1, -0.0155202, -0.0204528, -0.00411525, 0.999662, 0.6, 1, 0.00185983, -0.0170514, 0.00800332, 0.999821, 0.633333, 1, 0.000686338, -0.00854191, 0.015836, 0.999838, 0.666667, 1, -0.00345011, 0.00695262, 0.01713, 0.999823, 0.7, 1, -0.00607064, 0.0304668, 0.0100459, 0.999467, 0.733333, 1, -0.00579444, 0.0627668, 0.00393429, 0.998004, 0.766667, 1, -0.00836222, 0.0918971, 0.000257638, 0.995733, 0.8, 1, -0.0179866, 0.101694, 0.000423505, 0.994653, 0.833333, 1, -0.0252307, 0.0895094, 0.00170888, 0.995665, 0.866667, 1, -0.0221331, 0.0611823, -0.0033732, 0.997876, 0.9, 1, -0.0135197, 0.0283008, -0.0169807, 0.999364, 0.933333, 1, -0.012331, 0.0013229, -0.0316757, 0.999421, 0.966667, 1, -0.0201139, -0.0170928, -0.0394187, 0.998874, 1, 1, -0.0272402, -0.0299904, -0.037637, 0.99847, 1.03333, 1, -0.0407882, -0.0395068, -0.0282363, 0.997987, 1.06667, 1, -0.0653678, -0.0422458, -0.0212416, 0.99674, 1.1, 1, -0.0838113, -0.0402351, -0.021257, 0.995442, 1.13333, 1, -0.0787817, -0.0374331, -0.0204625, 0.995979, 1.16667, 1, -0.0538923, -0.0335378, -0.0151722, 0.997868, 1.2, 1, -0.0202299, -0.0274528, -0.0046603, 0.999408, 1.23333, 1, -0.000373004, -0.0204592, 0.00765034, 0.999761, 1.26667, 1, 9.0143e-05, -0.00946893, 0.0157431, 0.999831, 1.3, 1, -0.0034501, 0.00695262, 0.01713, 0.999823)
tracks/8/type = "rotation_3d"
tracks/8/imported = true
tracks/8/enabled = true
tracks/8/path = NodePath("Skeleton_01/Skeleton3D:Neck")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = PackedFloat32Array(0, 1, 0.189547, 0.01077, 0.0442148, 0.980816, 0.0333333, 1, 0.189547, 0.01077, 0.0442148, 0.980816, 0.0666667, 1, 0.18249, 0.0105505, 0.0406099, 0.982312, 0.1, 1, 0.174795, 0.0107016, 0.0381332, 0.983808, 0.133333, 1, 0.166325, 0.0111018, 0.0372418, 0.985305, 0.166667, 1, 0.159184, 0.0119527, 0.0374694, 0.986465, 0.2, 1, 0.158347, 0.0130412, 0.0386227, 0.986542, 0.233333, 1, 0.167071, 0.0134534, 0.0391266, 0.985076, 0.266667, 1, 0.180731, 0.0127121, 0.0368896, 0.982758, 0.3, 1, 0.191218, 0.0115523, 0.0329357, 0.980927, 0.333333, 1, 0.194555, 0.0112312, 0.03008, 0.980366, 0.366667, 1, 0.191415, 0.01215, 0.0292271, 0.980999, 0.4, 1, 0.185031, 0.0137355, 0.0298672, 0.982183, 0.433333, 1, 0.178984, 0.0148771, 0.0315215, 0.983234, 0.5, 1, 0.178, 0.0141221, 0.0371454, 0.983228, 0.533333, 1, 0.186619, 0.0133473, 0.0410738, 0.981483, 0.566667, 1, 0.19671, 0.012984, 0.0449322, 0.979346, 0.6, 1, 0.201195, 0.0127907, 0.0475563, 0.978313, 0.633333, 1, 0.197778, 0.0120825, 0.047585, 0.979017, 0.666667, 1, 0.189547, 0.01077, 0.0442148, 0.980816, 0.7, 1, 0.182491, 0.0105515, 0.0406101, 0.982312, 0.733333, 1, 0.174795, 0.0107006, 0.0381334, 0.983808, 0.766667, 1, 0.166325, 0.0111019, 0.0372418, 0.985305, 0.8, 1, 0.159182, 0.0119549, 0.0374758, 0.986465, 0.833333, 1, 0.158343, 0.0130439, 0.0386364, 0.986542, 0.866667, 1, 0.167068, 0.0134581, 0.0391482, 0.985076, 0.9, 1, 0.180729, 0.0127172, 0.0369189, 0.982758, 0.933333, 1, 0.191221, 0.0115584, 0.032969, 0.980925, 0.966667, 1, 0.194558, 0.0112364, 0.0301192, 0.980364, 1, 1, 0.19142, 0.0121543, 0.0292672, 0.980996, 1.03333, 1, 0.185037, 0.0137407, 0.0299163, 0.98218, 1.06667, 1, 0.178993, 0.0148832, 0.0315795, 0.983231, 1.13333, 1, 0.178005, 0.0141248, 0.0371844, 0.983225, 1.16667, 1, 0.186623, 0.0133498, 0.0410981, 0.981481, 1.2, 1, 0.19671, 0.0129862, 0.0449445, 0.979345, 1.23333, 1, 0.201197, 0.0127898, 0.0475626, 0.978312, 1.26667, 1, 0.197778, 0.0120828, 0.0475864, 0.979016, 1.3, 1, 0.189547, 0.01077, 0.0442148, 0.980816)
tracks/9/type = "rotation_3d"
tracks/9/imported = true
tracks/9/enabled = true
tracks/9/path = NodePath("Skeleton_01/Skeleton3D:Head")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = PackedFloat32Array(0, 1, -0.071418, 0.0365028, -0.0373793, 0.996077, 0.0333333, 1, -0.071418, 0.0365028, -0.0373793, 0.996077, 0.0666667, 1, -0.0805841, 0.0302519, -0.0341223, 0.995704, 0.1, 1, -0.0886453, 0.0272078, -0.0325257, 0.99516, 0.133333, 1, -0.0914558, 0.0271358, -0.0348494, 0.994829, 0.166667, 1, -0.0829815, 0.030757, -0.0396291, 0.995288, 0.2, 1, -0.076415, 0.0372405, -0.042543, 0.995472, 0.233333, 1, -0.0832685, 0.0409825, -0.0402113, 0.994872, 0.266667, 1, -0.0916507, 0.0370758, -0.0342407, 0.994512, 0.3, 1, -0.0841016, 0.0291516, -0.0288389, 0.995613, 0.333333, 1, -0.0707112, 0.0252832, -0.0261611, 0.996833, 0.366667, 1, -0.0700912, 0.0185483, -0.025236, 0.997049, 0.4, 1, -0.0826491, 0.0110295, -0.0274085, 0.996141, 0.433333, 1, -0.0942372, 0.00693248, -0.0331308, 0.994974, 0.466667, 1, -0.091962, 0.006886, -0.0385197, 0.994993, 0.5, 1, -0.0789649, 0.00828532, -0.0405256, 0.996019, 0.533333, 1, -0.0702379, 0.0102356, -0.0405651, 0.996653, 0.566667, 1, -0.0673373, 0.0146057, -0.0398696, 0.996826, 0.6, 1, -0.066819, 0.0171193, -0.0378743, 0.996899, 0.633333, 1, -0.0692388, 0.0098652, -0.0354938, 0.99692, 0.666667, 1, -0.0723229, 0.0117423, -0.0355384, 0.996679, 0.7, 1, -0.0810477, 0.0164747, -0.0329736, 0.996028, 0.733333, 1, -0.088807, 0.0222237, -0.0320702, 0.995284, 0.766667, 1, -0.0914558, 0.0271357, -0.0348494, 0.994829, 0.8, 1, -0.0762311, 0.0302238, -0.03398, 0.996053, 0.833333, 1, -0.0582507, 0.0343058, -0.0301072, 0.997258, 0.866667, 1, -0.040028, 0.0346635, -0.0201947, 0.998393, 0.9, 1, -0.0236519, 0.0263527, -0.00750305, 0.999345, 0.933333, 1, -0.0109863, 0.0135602, 0.00235683, 0.999845, 0.966667, 1, -0.0043946, 0.00594937, 0.00786947, 0.999942, 1, 1, 0.00227899, 0.00779669, 0.00958935, 0.999921, 1.03333, 1, 0.0136651, 0.0167346, 0.00609268, 0.999748, 1.06667, 1, 0.0186704, 0.0264552, -0.00266646, 0.999472, 1.1, 1, 0.0179168, 0.0319218, -0.0124472, 0.999252, 1.13333, 1, 0.0117614, 0.0347866, -0.0199181, 0.999127, 1.16667, 1, -0.00501232, 0.0395113, -0.0260925, 0.998866, 1.2, 1, -0.0263379, 0.046535, -0.0314723, 0.998073, 1.23333, 1, -0.0462091, 0.051098, -0.0349464, 0.997012, 1.26667, 1, -0.0626942, 0.0451905, -0.0366033, 0.996337, 1.3, 1, -0.0714181, 0.0365028, -0.0373793, 0.996077)
tracks/10/type = "rotation_3d"
tracks/10/imported = true
tracks/10/enabled = true
tracks/10/path = NodePath("Skeleton_01/Skeleton3D:LeftShoulder")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = PackedFloat32Array(0, 1, -0.536966, -0.414773, -0.487342, 0.549662, 0.0333333, 1, -0.536966, -0.414773, -0.487342, 0.549662, 0.0666667, 1, -0.541657, -0.409004, -0.487301, 0.549419, 0.1, 1, -0.545579, -0.404086, -0.487784, 0.548748, 0.133333, 1, -0.550463, -0.397609, -0.491375, 0.545388, 0.166667, 1, -0.556841, -0.388756, -0.498171, 0.539093, 0.2, 1, -0.560213, -0.383779, -0.503376, 0.534311, 0.233333, 1, -0.555729, -0.38949, -0.503935, 0.534334, 0.266667, 1, -0.545877, -0.402087, -0.503032, 0.536006, 0.3, 1, -0.538685, -0.41085, -0.504446, 0.535308, 0.333333, 1, -0.538556, -0.410586, -0.507797, 0.532465, 0.366667, 1, -0.544036, -0.403333, -0.511206, 0.529166, 0.4, 1, -0.55081, -0.394507, -0.513148, 0.526926, 0.433333, 1, -0.556049, -0.387564, -0.51425, 0.525501, 0.466667, 1, -0.559444, -0.382739, -0.516222, 0.523497, 0.5, 1, -0.558907, -0.383281, -0.516631, 0.52327, 0.533333, 1, -0.550818, -0.394744, -0.510026, 0.529765, 0.566667, 1, -0.537655, -0.413159, -0.496448, 0.542002, 0.6, 1, -0.535985, -0.418753, -0.489535, 0.545639, 0.633333, 1, -0.536564, -0.416769, -0.487567, 0.548344, 0.666667, 1, -0.536966, -0.414773, -0.487342, 0.549662, 0.7, 1, -0.541657, -0.409004, -0.487301, 0.54942, 0.733333, 1, -0.545578, -0.404087, -0.487785, 0.548747, 0.766667, 1, -0.550463, -0.397609, -0.491375, 0.545388, 0.8, 1, -0.556842, -0.388756, -0.498171, 0.539093, 0.833333, 1, -0.560212, -0.38378, -0.503376, 0.534311, 0.866667, 1, -0.555729, -0.38949, -0.503935, 0.534334, 0.9, 1, -0.545877, -0.402088, -0.503032, 0.536006, 0.933333, 1, -0.538686, -0.410847, -0.504448, 0.535308, 0.966667, 1, -0.538556, -0.410586, -0.507797, 0.532465, 1, 1, -0.544036, -0.403332, -0.511207, 0.529166, 1.03333, 1, -0.55081, -0.394508, -0.513147, 0.526927, 1.06667, 1, -0.556048, -0.387564, -0.514249, 0.525502, 1.1, 1, -0.559444, -0.382739, -0.516222, 0.523497, 1.13333, 1, -0.558907, -0.383282, -0.516631, 0.523269, 1.16667, 1, -0.550817, -0.394744, -0.510026, 0.529765, 1.2, 1, -0.537657, -0.413158, -0.496447, 0.542002, 1.23333, 1, -0.535983, -0.418753, -0.489537, 0.545639, 1.26667, 1, -0.536564, -0.416769, -0.487567, 0.548344, 1.3, 1, -0.536966, -0.414773, -0.487342, 0.549662)
tracks/11/type = "rotation_3d"
tracks/11/imported = true
tracks/11/enabled = true
tracks/11/path = NodePath("Skeleton_01/Skeleton3D:LeftArm")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = PackedFloat32Array(0, 1, -0.318263, 0.161324, -0.262705, 0.896476, 0.0333333, 1, -0.318263, 0.161324, -0.262705, 0.896476, 0.0666667, 1, -0.326271, 0.174893, -0.278876, 0.886108, 0.1, 1, -0.339889, 0.187812, -0.289754, 0.874783, 0.133333, 1, -0.346001, 0.199526, -0.290211, 0.869627, 0.166667, 1, -0.34122, 0.209226, -0.278779, 0.872969, 0.2, 1, -0.332652, 0.211318, -0.263018, 0.88063, 0.233333, 1, -0.324242, 0.198739, -0.256053, 0.888711, 0.266667, 1, -0.325512, 0.167055, -0.25317, 0.895567, 0.3, 1, -0.328708, 0.137292, -0.247931, 0.900907, 0.333333, 1, -0.325135, 0.12949, -0.23722, 0.906226, 0.366667, 1, -0.301131, 0.150764, -0.22908, 0.913298, 0.4, 1, -0.269386, 0.186301, -0.227338, 0.917083, 0.433333, 1, -0.253286, 0.211402, -0.224431, 0.916944, 0.466667, 1, -0.252649, 0.21382, -0.213617, 0.919139, 0.5, 1, -0.24081, 0.209979, -0.212696, 0.923407, 0.533333, 1, -0.262512, 0.183562, -0.221424, 0.921066, 0.566667, 1, -0.3109, 0.14678, -0.237154, 0.908601, 0.6, 1, -0.330884, 0.126802, -0.246255, 0.902106, 0.633333, 1, -0.324086, 0.136263, -0.254315, 0.900958, 0.666667, 1, -0.318263, 0.161325, -0.262705, 0.896476, 0.7, 1, -0.326277, 0.174894, -0.278877, 0.886105, 0.733333, 1, -0.339892, 0.187812, -0.289755, 0.874781, 0.766667, 1, -0.346, 0.199526, -0.290211, 0.869627, 0.8, 1, -0.339965, 0.21328, -0.270642, 0.875036, 0.833333, 1, -0.329961, 0.220014, -0.245331, 0.884608, 0.866667, 1, -0.320567, 0.212403, -0.228042, 0.894493, 0.9, 1, -0.321378, 0.185409, -0.214581, 0.90349, 0.933333, 1, -0.324263, 0.159387, -0.199745, 0.910797, 0.966667, 1, -0.320288, 0.154207, -0.182334, 0.916728, 1, 1, -0.296607, 0.177031, -0.171964, 0.922558, 1.03333, 1, -0.265765, 0.212548, -0.17292, 0.924279, 1.06667, 1, -0.250094, 0.235599, -0.17582, 0.922515, 1.1, 1, -0.24955, 0.234075, -0.172147, 0.923742, 1.13333, 1, -0.238614, 0.22565, -0.179708, 0.927281, 1.16667, 1, -0.260944, 0.194468, -0.197862, 0.92463, 1.2, 1, -0.309818, 0.153426, -0.222825, 0.911494, 1.23333, 1, -0.330321, 0.129935, -0.239361, 0.903721, 1.26667, 1, -0.323942, 0.137117, -0.252467, 0.901399, 1.3, 1, -0.318263, 0.161324, -0.262705, 0.896476)
tracks/12/type = "rotation_3d"
tracks/12/imported = true
tracks/12/enabled = true
tracks/12/path = NodePath("Skeleton_01/Skeleton3D:LeftForeArm")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = PackedFloat32Array(0, 1, 0.490496, 0.509261, -0.501576, 0.498486, 0.0333333, 1, 0.490496, 0.509261, -0.501576, 0.498486, 0.0666667, 1, 0.482773, 0.517013, -0.493547, 0.506003, 0.1, 1, 0.476119, 0.523507, -0.486613, 0.512308, 0.133333, 1, 0.472567, 0.526901, -0.482911, 0.515608, 0.166667, 1, 0.474175, 0.525393, -0.484549, 0.514132, 0.2, 1, 0.479466, 0.520352, -0.489965, 0.509196, 0.233333, 1, 0.482264, 0.517614, -0.492871, 0.506533, 0.266667, 1, 0.479761, 0.519966, -0.49042, 0.508875, 0.3, 1, 0.476554, 0.522984, -0.487231, 0.511849, 0.333333, 1, 0.478439, 0.521103, -0.489283, 0.510048, 0.366667, 1, 0.483842, 0.515774, -0.494931, 0.504893, 0.4, 1, 0.487431, 0.511945, -0.49889, 0.50143, 0.433333, 1, 0.489279, 0.509561, -0.501371, 0.499581, 0.466667, 1, 0.493744, 0.504633, -0.50642, 0.495077, 0.5, 1, 0.500152, 0.498072, -0.512849, 0.488629, 0.533333, 1, 0.501377, 0.497166, -0.513674, 0.487428, 0.566667, 1, 0.494919, 0.504313, -0.506628, 0.494016, 0.6, 1, 0.491367, 0.508182, -0.502726, 0.49757, 0.633333, 1, 0.491388, 0.508246, -0.502636, 0.497576, 0.666667, 1, 0.490493, 0.509264, -0.501573, 0.49849, 0.7, 1, 0.482768, 0.517021, -0.493536, 0.506011, 0.733333, 1, 0.476117, 0.52351, -0.486609, 0.51231, 0.766667, 1, 0.472565, 0.526903, -0.482909, 0.515611, 0.8, 1, 0.476441, 0.523246, -0.486867, 0.512033, 0.833333, 1, 0.484186, 0.515762, -0.49479, 0.504714, 0.866667, 1, 0.48938, 0.510585, -0.500144, 0.499666, 0.9, 1, 0.489246, 0.510646, -0.500113, 0.499767, 0.933333, 1, 0.488142, 0.511688, -0.499083, 0.500808, 0.966667, 1, 0.491245, 0.508488, -0.502391, 0.497716, 1, 1, 0.49673, 0.502798, -0.508127, 0.4922, 1.03333, 1, 0.49951, 0.499624, -0.511269, 0.489356, 1.06667, 1, 0.500053, 0.498517, -0.512431, 0.488715, 1.1, 1, 0.502846, 0.495161, -0.515776, 0.485733, 1.13333, 1, 0.507325, 0.490439, -0.520215, 0.48111, 1.16667, 1, 0.506563, 0.491642, -0.518993, 0.482004, 1.2, 1, 0.498185, 0.500939, -0.509976, 0.49071, 1.23333, 1, 0.492982, 0.506544, -0.504381, 0.495966, 1.26667, 1, 0.491827, 0.507802, -0.503084, 0.497142, 1.3, 1, 0.490494, 0.509262, -0.501574, 0.498488)
tracks/13/type = "rotation_3d"
tracks/13/imported = true
tracks/13/enabled = true
tracks/13/path = NodePath("Skeleton_01/Skeleton3D:LeftHand")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = PackedFloat32Array(0, 1, 0.131785, -0.713815, -0.0674892, 0.684505, 0.0333333, 1, 0.131785, -0.713815, -0.0674892, 0.684505, 0.0666667, 1, 0.102204, -0.689114, -0.0562221, 0.715203, 0.1, 1, 0.0691442, -0.688948, -0.0482936, 0.719887, 0.133333, 1, 0.0468731, -0.700889, -0.0492553, 0.710022, 0.166667, 1, 0.0379985, -0.710663, -0.0534502, 0.700469, 0.2, 1, 0.0439477, -0.717869, -0.0536556, 0.692714, 0.233333, 1, 0.06677, -0.724206, -0.0476693, 0.684687, 0.266667, 1, 0.0855073, -0.72154, -0.0464038, 0.685504, 0.3, 1, 0.0910254, -0.714843, -0.0538976, 0.691238, 0.333333, 1, 0.0876905, -0.709958, -0.0675049, 0.695495, 0.366667, 1, 0.0934981, -0.711646, -0.0682462, 0.692936, 0.4, 1, 0.112713, -0.713518, -0.0450349, 0.690044, 0.433333, 1, 0.123862, -0.711962, -0.0164028, 0.691013, 0.466667, 1, 0.1119, -0.711666, -0.0140849, 0.693405, 0.533333, 1, 0.0943771, -0.727277, -0.0420138, 0.678524, 0.566667, 1, 0.103561, -0.720317, -0.0567767, 0.683517, 0.6, 1, 0.117569, -0.715228, -0.0600497, 0.68631, 0.633333, 1, 0.131594, -0.716453, -0.0607523, 0.682413, 0.666667, 1, 0.131785, -0.713815, -0.0674895, 0.684505, 0.7, 1, 0.102209, -0.689111, -0.0562277, 0.715205, 0.733333, 1, 0.0691471, -0.688947, -0.048297, 0.719888, 0.766667, 1, 0.0468722, -0.700889, -0.0492551, 0.710022, 0.8, 1, 0.0389759, -0.711832, -0.0497879, 0.699498, 0.833333, 1, 0.0464171, -0.720209, -0.045942, 0.690676, 0.866667, 1, 0.0706129, -0.727258, -0.0362429, 0.68176, 0.9, 1, 0.0899907, -0.725142, -0.0321396, 0.681937, 0.933333, 1, 0.0958855, -0.719119, -0.0375986, 0.687212, 0.966667, 1, 0.0935975, -0.71487, -0.0497379, 0.691177, 1, 1, 0.100722, -0.716323, -0.0496536, 0.688674, 1.03333, 1, 0.120487, -0.717195, -0.0263907, 0.68587, 1.06667, 1, 0.131309, -0.714872, 0.00114647, 0.686815, 1.1, 1, 0.118367, -0.71426, 0.000821394, 0.689798, 1.13333, 1, 0.105946, -0.725693, -0.0109823, 0.679724, 1.16667, 1, 0.0971423, -0.728682, -0.0342399, 0.677062, 1.2, 1, 0.104825, -0.72132, -0.0520704, 0.682641, 1.23333, 1, 0.118014, -0.715717, -0.057789, 0.685917, 1.26667, 1, 0.131703, -0.716576, -0.0601301, 0.682318, 1.3, 1, 0.131785, -0.713814, -0.0674896, 0.684505)
tracks/14/type = "rotation_3d"
tracks/14/imported = true
tracks/14/enabled = true
tracks/14/path = NodePath("Skeleton_01/Skeleton3D:LeftHandIndex1")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = PackedFloat32Array(0, 1, 0.203807, 0.619633, -0.246372, 0.716812, 0.0333333, 1, 0.203807, 0.619633, -0.246372, 0.716812, 0.0666667, 1, 0.194281, 0.622461, -0.236094, 0.720456, 0.1, 1, 0.189061, 0.622934, -0.233788, 0.722186, 0.133333, 1, 0.193571, 0.620462, -0.242506, 0.720241, 0.166667, 1, 0.203256, 0.616631, -0.256156, 0.716126, 0.2, 1, 0.213053, 0.61387, -0.265898, 0.712089, 0.233333, 1, 0.221481, 0.611955, -0.272389, 0.708704, 0.266667, 1, 0.22674, 0.610956, -0.275615, 0.706653, 0.3, 1, 0.227217, 0.611322, -0.274386, 0.706661, 0.333333, 1, 0.217517, 0.613586, -0.266968, 0.710582, 0.366667, 1, 0.200982, 0.615892, -0.25836, 0.716612, 0.4, 1, 0.19795, 0.61455, -0.262428, 0.71713, 0.433333, 1, 0.201613, 0.611917, -0.271522, 0.714972, 0.466667, 1, 0.203279, 0.610372, -0.276829, 0.713785, 0.5, 1, 0.201829, 0.61099, -0.274686, 0.714496, 0.533333, 1, 0.207724, 0.612487, -0.270243, 0.713217, 0.566667, 1, 0.20874, 0.615134, -0.261552, 0.713882, 0.6, 1, 0.207448, 0.618419, -0.250609, 0.715345, 0.633333, 1, 0.207646, 0.618898, -0.249052, 0.715417, 0.666667, 1, 0.203808, 0.619632, -0.246373, 0.716811, 0.7, 1, 0.194281, 0.622461, -0.236094, 0.720456, 0.733333, 1, 0.189061, 0.622935, -0.233788, 0.722186, 0.766667, 1, 0.193571, 0.620462, -0.242506, 0.720241, 0.8, 1, 0.203256, 0.616631, -0.256157, 0.716126, 0.833333, 1, 0.213052, 0.61387, -0.265898, 0.712089, 0.866667, 1, 0.22148, 0.611956, -0.272388, 0.708704, 0.9, 1, 0.22674, 0.610956, -0.275615, 0.706653, 0.933333, 1, 0.227217, 0.611322, -0.274387, 0.706661, 0.966667, 1, 0.217518, 0.613586, -0.266968, 0.710582, 1, 1, 0.200981, 0.615892, -0.25836, 0.716612, 1.03333, 1, 0.19795, 0.61455, -0.262427, 0.71713, 1.06667, 1, 0.201613, 0.611917, -0.271522, 0.714972, 1.1, 1, 0.203279, 0.610371, -0.276829, 0.713786, 1.13333, 1, 0.201829, 0.61099, -0.274686, 0.714496, 1.16667, 1, 0.207724, 0.612488, -0.270243, 0.713217, 1.2, 1, 0.20874, 0.615134, -0.261552, 0.713882, 1.23333, 1, 0.207448, 0.618419, -0.250609, 0.715345, 1.26667, 1, 0.207646, 0.618898, -0.249053, 0.715417, 1.3, 1, 0.203807, 0.619632, -0.246372, 0.716812)
tracks/15/type = "rotation_3d"
tracks/15/imported = true
tracks/15/enabled = true
tracks/15/path = NodePath("Skeleton_01/Skeleton3D:LeftHandIndex2")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = PackedFloat32Array(0, 1, 0.452947, -0.00585606, 0.0281164, 0.891075, 0.0333333, 1, 0.452947, -0.00585606, 0.0281164, 0.891075, 0.0666667, 1, 0.456745, -0.00597505, 0.0280875, 0.889134, 0.1, 1, 0.458711, -0.0060368, 0.0280722, 0.888122, 0.133333, 1, 0.454951, -0.00591884, 0.0281011, 0.890053, 0.166667, 1, 0.450562, -0.00578158, 0.0281342, 0.892283, 0.2, 1, 0.447253, -0.0056777, 0.028159, 0.893946, 0.233333, 1, 0.447335, -0.0056804, 0.0281582, 0.893905, 0.266667, 1, 0.449918, -0.00576114, 0.0281392, 0.892608, 0.3, 1, 0.451226, -0.00580212, 0.0281293, 0.891948, 0.333333, 1, 0.452255, -0.00583444, 0.0281216, 0.891426, 0.366667, 1, 0.454791, -0.00591387, 0.0281023, 0.890135, 0.4, 1, 0.457715, -0.00600549, 0.0280801, 0.888635, 0.433333, 1, 0.453103, -0.00586102, 0.0281151, 0.890995, 0.466667, 1, 0.447165, -0.00567505, 0.0281595, 0.89399, 0.5, 1, 0.445114, -0.00561096, 0.0281744, 0.895013, 0.533333, 1, 0.443998, -0.00557621, 0.0281824, 0.895567, 0.566667, 1, 0.444913, -0.00560471, 0.0281759, 0.895113, 0.6, 1, 0.449916, -0.00576125, 0.0281391, 0.892609, 0.633333, 1, 0.452579, -0.00584461, 0.0281191, 0.891262, 0.666667, 1, 0.452947, -0.005856, 0.0281165, 0.891075, 0.7, 1, 0.456745, -0.00597528, 0.0280875, 0.889134, 0.733333, 1, 0.458711, -0.00603674, 0.0280721, 0.888122, 0.766667, 1, 0.454951, -0.00591901, 0.0281011, 0.890053, 0.8, 1, 0.450562, -0.00578136, 0.0281345, 0.892283, 0.833333, 1, 0.447253, -0.00567789, 0.0281588, 0.893946, 0.866667, 1, 0.447335, -0.00568047, 0.0281582, 0.893905, 0.9, 1, 0.449918, -0.0057612, 0.0281391, 0.892608, 0.933333, 1, 0.451225, -0.00580209, 0.0281294, 0.891948, 0.966667, 1, 0.452255, -0.00583433, 0.0281217, 0.891426, 1, 1, 0.454791, -0.00591375, 0.0281024, 0.890135, 1.03333, 1, 0.457715, -0.00600561, 0.02808, 0.888635, 1.06667, 1, 0.453103, -0.00586097, 0.0281152, 0.890996, 1.1, 1, 0.447165, -0.00567512, 0.0281594, 0.89399, 1.13333, 1, 0.445114, -0.00561094, 0.0281743, 0.895013, 1.16667, 1, 0.443998, -0.00557619, 0.0281824, 0.895567, 1.2, 1, 0.444912, -0.00560476, 0.0281759, 0.895113, 1.23333, 1, 0.449916, -0.00576112, 0.0281391, 0.892609, 1.26667, 1, 0.452579, -0.00584458, 0.0281192, 0.891262, 1.3, 1, 0.452947, -0.00585598, 0.0281164, 0.891075)
tracks/16/type = "rotation_3d"
tracks/16/imported = true
tracks/16/enabled = true
tracks/16/path = NodePath("Skeleton_01/Skeleton3D:LeftHandMiddle1")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = PackedFloat32Array(0, 1, 0.191403, 0.677604, -0.194062, 0.68305, 0.0333333, 1, 0.191403, 0.677604, -0.194062, 0.68305, 0.0666667, 1, 0.182833, 0.678755, -0.1887, 0.685752, 0.1, 1, 0.181696, 0.677621, -0.190507, 0.686676, 0.133333, 1, 0.187463, 0.67778, -0.192322, 0.684459, 0.166667, 1, 0.197931, 0.679057, -0.193355, 0.67994, 0.2, 1, 0.206485, 0.677845, -0.198423, 0.67714, 0.233333, 1, 0.212493, 0.673923, -0.208051, 0.676306, 0.266667, 1, 0.216741, 0.670044, -0.217073, 0.675977, 0.3, 1, 0.218261, 0.669155, -0.219281, 0.675655, 0.333333, 1, 0.208888, 0.670624, -0.213664, 0.678953, 0.366667, 1, 0.194937, 0.672679, -0.205076, 0.6837, 0.4, 1, 0.196808, 0.671118, -0.208823, 0.683564, 0.433333, 1, 0.20203, 0.671167, -0.210439, 0.681494, 0.466667, 1, 0.207912, 0.675637, -0.203295, 0.677465, 0.5, 1, 0.211106, 0.678995, -0.197499, 0.674829, 0.533333, 1, 0.213018, 0.67544, -0.205181, 0.675504, 0.566667, 1, 0.209187, 0.673792, -0.207377, 0.677673, 0.6, 1, 0.202951, 0.674095, -0.204857, 0.68003, 0.633333, 1, 0.200981, 0.674956, -0.202521, 0.680461, 0.666667, 1, 0.191407, 0.677605, -0.194064, 0.683048, 0.7, 1, 0.182842, 0.678751, -0.188712, 0.68575, 0.733333, 1, 0.181701, 0.67762, -0.190511, 0.686674, 0.766667, 1, 0.187463, 0.677779, -0.192323, 0.68446, 0.8, 1, 0.197933, 0.679056, -0.193361, 0.679939, 0.833333, 1, 0.206487, 0.677845, -0.198422, 0.67714, 0.866667, 1, 0.212496, 0.673922, -0.208049, 0.676307, 0.9, 1, 0.216743, 0.670044, -0.217075, 0.675975, 0.933333, 1, 0.21826, 0.669157, -0.219279, 0.675654, 0.966667, 1, 0.208887, 0.670626, -0.213661, 0.678952, 1, 1, 0.194936, 0.672679, -0.205075, 0.683701, 1.03333, 1, 0.196807, 0.671118, -0.208823, 0.683564, 1.06667, 1, 0.20203, 0.671167, -0.210439, 0.681494, 1.1, 1, 0.207907, 0.675638, -0.203292, 0.677466, 1.13333, 1, 0.211101, 0.678996, -0.197496, 0.674831, 1.16667, 1, 0.21302, 0.675439, -0.205183, 0.675503, 1.2, 1, 0.209197, 0.673792, -0.207391, 0.677665, 1.23333, 1, 0.202944, 0.674096, -0.204851, 0.680032, 1.26667, 1, 0.200983, 0.674952, -0.202525, 0.680463, 1.3, 1, 0.191402, 0.677605, -0.194061, 0.68305)
tracks/17/type = "rotation_3d"
tracks/17/imported = true
tracks/17/enabled = true
tracks/17/path = NodePath("Skeleton_01/Skeleton3D:LeftHandMiddle2")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = PackedFloat32Array(0, 1, 0.404219, 0.00171707, 0.0331771, 0.914059, 0.0333333, 1, 0.404219, 0.00171707, 0.0331771, 0.914059, 0.0666667, 1, 0.40893, 0.0017461, 0.0336379, 0.911944, 0.1, 1, 0.411477, 0.00176152, 0.0338871, 0.910788, 0.133333, 1, 0.406747, 0.00173272, 0.0334245, 0.912927, 0.166667, 1, 0.398021, 0.00167923, 0.032571, 0.916796, 0.2, 1, 0.395048, 0.00166116, 0.0322805, 0.918092, 0.233333, 1, 0.396796, 0.00167172, 0.0324515, 0.917331, 0.266667, 1, 0.398682, 0.00168335, 0.0326357, 0.916507, 0.3, 1, 0.398246, 0.00168057, 0.0325931, 0.916698, 0.333333, 1, 0.399594, 0.00168881, 0.0327249, 0.916106, 0.366667, 1, 0.40551, 0.00172503, 0.0333034, 0.913482, 0.4, 1, 0.411566, 0.00176215, 0.0338958, 0.910748, 0.433333, 1, 0.40088, 0.0016968, 0.0328506, 0.91554, 0.466667, 1, 0.386984, 0.00161187, 0.0314925, 0.921547, 0.5, 1, 0.38256, 0.00158486, 0.0310606, 0.923407, 0.533333, 1, 0.387788, 0.00161683, 0.0315712, 0.921206, 0.566667, 1, 0.397172, 0.00167415, 0.032488, 0.917168, 0.6, 1, 0.402239, 0.00170519, 0.0329835, 0.914939, 0.633333, 1, 0.402822, 0.00170876, 0.0330404, 0.91468, 0.666667, 1, 0.404219, 0.00171724, 0.0331771, 0.914059, 0.7, 1, 0.40893, 0.001746, 0.0336378, 0.911944, 0.733333, 1, 0.411477, 0.00176151, 0.0338871, 0.910788, 0.766667, 1, 0.406747, 0.00173275, 0.0334244, 0.912927, 0.8, 1, 0.398021, 0.00167935, 0.032571, 0.916796, 0.833333, 1, 0.395048, 0.00166114, 0.0322806, 0.918091, 0.866667, 1, 0.396796, 0.00167177, 0.0324515, 0.917331, 0.9, 1, 0.398682, 0.00168332, 0.0326357, 0.916507, 0.933333, 1, 0.398246, 0.00168071, 0.032593, 0.916698, 0.966667, 1, 0.399594, 0.00168894, 0.0327249, 0.916106, 1, 1, 0.40551, 0.00172506, 0.0333034, 0.913482, 1.03333, 1, 0.411566, 0.00176217, 0.033896, 0.910748, 1.06667, 1, 0.40088, 0.00169677, 0.0328506, 0.91554, 1.1, 1, 0.386984, 0.00161188, 0.0314926, 0.921547, 1.13333, 1, 0.38256, 0.00158484, 0.0310607, 0.923407, 1.16667, 1, 0.387788, 0.00161679, 0.0315711, 0.921206, 1.2, 1, 0.397172, 0.00167416, 0.0324881, 0.917168, 1.23333, 1, 0.40224, 0.00170508, 0.0329834, 0.914939, 1.26667, 1, 0.402822, 0.00170868, 0.0330405, 0.91468, 1.3, 1, 0.404219, 0.00171719, 0.0331771, 0.914059)
tracks/18/type = "rotation_3d"
tracks/18/imported = true
tracks/18/enabled = true
tracks/18/path = NodePath("Skeleton_01/Skeleton3D:LeftHandPinky1")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = PackedFloat32Array(0, 1, 0.269538, 0.70675, -0.176577, 0.629821, 0.0333333, 1, 0.269538, 0.70675, -0.176577, 0.629821, 0.0666667, 1, 0.264397, 0.708495, -0.172643, 0.631129, 0.1, 1, 0.262574, 0.711385, -0.163978, 0.63095, 0.133333, 1, 0.261803, 0.712947, -0.159216, 0.630726, 0.166667, 1, 0.263514, 0.712364, -0.160555, 0.630334, 0.2, 1, 0.268634, 0.710805, -0.163889, 0.629073, 0.233333, 1, 0.273533, 0.709604, -0.166039, 0.627753, 0.266667, 1, 0.275961, 0.709, -0.167093, 0.627093, 0.3, 1, 0.277646, 0.708462, -0.16819, 0.626662, 0.333333, 1, 0.274964, 0.710687, -0.162016, 0.626953, 0.366667, 1, 0.269216, 0.714821, -0.15074, 0.627559, 0.4, 1, 0.266221, 0.718698, -0.139275, 0.627058, 0.433333, 1, 0.260199, 0.722136, -0.130385, 0.627548, 0.466667, 1, 0.25902, 0.722176, -0.130973, 0.627866, 0.5, 1, 0.259449, 0.719715, -0.138969, 0.628795, 0.533333, 1, 0.263626, 0.715403, -0.151465, 0.629093, 0.566667, 1, 0.266557, 0.710831, -0.164748, 0.629702, 0.6, 1, 0.26876, 0.707142, -0.175305, 0.630069, 0.633333, 1, 0.271208, 0.705606, -0.179381, 0.629594, 0.666667, 1, 0.269538, 0.70675, -0.176576, 0.629821, 0.7, 1, 0.264397, 0.708495, -0.172643, 0.631129, 0.733333, 1, 0.262574, 0.711385, -0.163978, 0.63095, 0.766667, 1, 0.261803, 0.712947, -0.159216, 0.630726, 0.8, 1, 0.263514, 0.712364, -0.160555, 0.630334, 0.833333, 1, 0.268634, 0.710805, -0.163889, 0.629073, 0.866667, 1, 0.273533, 0.709604, -0.166039, 0.627753, 0.9, 1, 0.275962, 0.709, -0.167092, 0.627092, 0.933333, 1, 0.277646, 0.708462, -0.16819, 0.626662, 0.966667, 1, 0.274965, 0.710687, -0.162016, 0.626953, 1, 1, 0.269216, 0.714822, -0.15074, 0.627559, 1.03333, 1, 0.266221, 0.718698, -0.139274, 0.627058, 1.06667, 1, 0.260199, 0.722135, -0.130385, 0.627548, 1.1, 1, 0.259021, 0.722176, -0.130974, 0.627866, 1.13333, 1, 0.259449, 0.719715, -0.13897, 0.628795, 1.16667, 1, 0.263626, 0.715403, -0.151465, 0.629093, 1.2, 1, 0.266556, 0.710831, -0.164748, 0.629702, 1.23333, 1, 0.26876, 0.707142, -0.175305, 0.630069, 1.26667, 1, 0.271209, 0.705606, -0.179381, 0.629594, 1.3, 1, 0.269538, 0.70675, -0.176577, 0.629821)
tracks/19/type = "rotation_3d"
tracks/19/imported = true
tracks/19/enabled = true
tracks/19/path = NodePath("Skeleton_01/Skeleton3D:LeftHandPinky2")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = PackedFloat32Array(0, 1, 0.170974, 0.00345799, 0.0395821, 0.984474, 0.0333333, 1, 0.170974, 0.00345799, 0.0395821, 0.984474, 0.0666667, 1, 0.167747, 0.00350607, 0.0390954, 0.985048, 0.1, 1, 0.165763, 0.00353564, 0.038796, 0.985396, 0.133333, 1, 0.165889, 0.00353373, 0.0388149, 0.985374, 0.166667, 1, 0.16702, 0.00351684, 0.0389856, 0.985176, 0.2, 1, 0.170219, 0.00346933, 0.0394681, 0.984609, 0.233333, 1, 0.175193, 0.00339522, 0.0402181, 0.983706, 0.266667, 1, 0.180254, 0.0033196, 0.0409807, 0.98276, 0.3, 1, 0.182073, 0.00329235, 0.0412546, 0.982414, 0.333333, 1, 0.177479, 0.00336096, 0.0405623, 0.983283, 0.366667, 1, 0.166707, 0.00352154, 0.0389384, 0.985231, 0.4, 1, 0.159352, 0.00363062, 0.0378286, 0.98649, 0.433333, 1, 0.156286, 0.00367603, 0.0373656, 0.986998, 0.466667, 1, 0.156011, 0.00368019, 0.0373241, 0.987043, 0.5, 1, 0.16002, 0.0036209, 0.0379293, 0.986378, 0.533333, 1, 0.166114, 0.00353027, 0.038849, 0.985335, 0.566667, 1, 0.171398, 0.00345182, 0.039646, 0.984398, 0.6, 1, 0.175332, 0.00339308, 0.0402392, 0.983681, 0.633333, 1, 0.174778, 0.00340131, 0.0401556, 0.983783, 0.666667, 1, 0.170975, 0.0034581, 0.039582, 0.984474, 0.7, 1, 0.167747, 0.00350588, 0.0390952, 0.985048, 0.733333, 1, 0.165763, 0.00353557, 0.038796, 0.985396, 0.766667, 1, 0.165889, 0.00353368, 0.0388151, 0.985374, 0.8, 1, 0.16702, 0.00351685, 0.0389857, 0.985176, 0.833333, 1, 0.170219, 0.00346928, 0.0394681, 0.984609, 0.866667, 1, 0.175193, 0.00339519, 0.040218, 0.983706, 0.9, 1, 0.180254, 0.00331959, 0.0409806, 0.98276, 0.933333, 1, 0.182073, 0.00329238, 0.0412548, 0.982414, 0.966667, 1, 0.177479, 0.00336104, 0.0405627, 0.983283, 1, 1, 0.166707, 0.0035215, 0.0389384, 0.985231, 1.03333, 1, 0.159352, 0.00363064, 0.0378286, 0.98649, 1.06667, 1, 0.156286, 0.0036762, 0.0373654, 0.986998, 1.1, 1, 0.156011, 0.00368025, 0.037324, 0.987043, 1.13333, 1, 0.160019, 0.00362086, 0.0379291, 0.986378, 1.16667, 1, 0.166114, 0.00353031, 0.038849, 0.985335, 1.2, 1, 0.171398, 0.0034518, 0.0396461, 0.984398, 1.23333, 1, 0.175332, 0.00339307, 0.0402391, 0.983681, 1.26667, 1, 0.174778, 0.00340125, 0.0401554, 0.983783, 1.3, 1, 0.170974, 0.00345804, 0.0395821, 0.984474)
tracks/20/type = "rotation_3d"
tracks/20/imported = true
tracks/20/enabled = true
tracks/20/path = NodePath("Skeleton_01/Skeleton3D:LeftHandRing1")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = PackedFloat32Array(0, 1, 0.232376, 0.672599, -0.206479, 0.67155, 0.0333333, 1, 0.232376, 0.672599, -0.206479, 0.67155, 0.0666667, 1, 0.224229, 0.675034, -0.199592, 0.673953, 0.1, 1, 0.219123, 0.674983, -0.19901, 0.675854, 0.133333, 1, 0.218783, 0.673835, -0.201753, 0.676296, 0.166667, 1, 0.222544, 0.671956, -0.20688, 0.675388, 0.2, 1, 0.231069, 0.668779, -0.215718, 0.67291, 0.233333, 1, 0.239778, 0.66606, -0.22322, 0.670107, 0.266667, 1, 0.245296, 0.664525, -0.227377, 0.668234, 0.3, 1, 0.248793, 0.663099, -0.231083, 0.667085, 0.333333, 1, 0.243547, 0.664975, -0.226163, 0.668838, 0.366667, 1, 0.231769, 0.66962, -0.213731, 0.672467, 0.4, 1, 0.228384, 0.671624, -0.208443, 0.673285, 0.433333, 1, 0.225454, 0.671078, -0.209408, 0.674517, 0.466667, 1, 0.228063, 0.670844, -0.210294, 0.673596, 0.5, 1, 0.230565, 0.670675, -0.210986, 0.672697, 0.533333, 1, 0.232481, 0.669089, -0.21498, 0.672351, 0.566667, 1, 0.235544, 0.669043, -0.215381, 0.671202, 0.6, 1, 0.237056, 0.669479, -0.214455, 0.67053, 0.633333, 1, 0.237326, 0.670132, -0.212901, 0.670278, 0.666667, 1, 0.232376, 0.672599, -0.206479, 0.67155, 0.7, 1, 0.224229, 0.675034, -0.199591, 0.673953, 0.733333, 1, 0.219126, 0.674982, -0.199012, 0.675853, 0.766667, 1, 0.218787, 0.673833, -0.201757, 0.676295, 0.8, 1, 0.222545, 0.671956, -0.206881, 0.675388, 0.833333, 1, 0.23107, 0.668778, -0.215719, 0.672909, 0.866667, 1, 0.239779, 0.66606, -0.223219, 0.670107, 0.9, 1, 0.245296, 0.664525, -0.227377, 0.668234, 0.933333, 1, 0.248792, 0.663099, -0.231081, 0.667086, 0.966667, 1, 0.243547, 0.664974, -0.226163, 0.668838, 1, 1, 0.23177, 0.669619, -0.213732, 0.672466, 1.03333, 1, 0.22838, 0.671625, -0.20844, 0.673286, 1.06667, 1, 0.225454, 0.671078, -0.209406, 0.674518, 1.1, 1, 0.228063, 0.670844, -0.210295, 0.673596, 1.13333, 1, 0.230568, 0.670674, -0.210987, 0.672696, 1.16667, 1, 0.232479, 0.669089, -0.21498, 0.672351, 1.2, 1, 0.235542, 0.669044, -0.215381, 0.671202, 1.23333, 1, 0.237057, 0.669479, -0.214456, 0.67053, 1.26667, 1, 0.237329, 0.670131, -0.212903, 0.670277, 1.3, 1, 0.232375, 0.672599, -0.206478, 0.67155)
tracks/21/type = "rotation_3d"
tracks/21/imported = true
tracks/21/enabled = true
tracks/21/path = NodePath("Skeleton_01/Skeleton3D:LeftHandRing2")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = PackedFloat32Array(0, 1, 0.338446, -0.00372775, 0.0385843, 0.940187, 0.0333333, 1, 0.338446, -0.00372775, 0.0385843, 0.940187, 0.0666667, 1, 0.341615, -0.00375061, 0.0388693, 0.939028, 0.1, 1, 0.343411, -0.00376354, 0.0390306, 0.938366, 0.133333, 1, 0.34381, -0.0037665, 0.0390666, 0.938219, 0.166667, 1, 0.342502, -0.00375704, 0.038949, 0.938702, 0.2, 1, 0.337946, -0.00372405, 0.0385395, 0.940369, 0.233333, 1, 0.333892, -0.00369466, 0.0381748, 0.941831, 0.266667, 1, 0.332294, -0.00368306, 0.0380312, 0.942402, 0.3, 1, 0.329906, -0.00366597, 0.0378161, 0.943249, 0.333333, 1, 0.330092, -0.00366724, 0.037833, 0.943183, 0.366667, 1, 0.337581, -0.0037214, 0.0385066, 0.940501, 0.4, 1, 0.34705, -0.00378976, 0.0393577, 0.937013, 0.433333, 1, 0.351372, -0.00382099, 0.0397457, 0.935384, 0.466667, 1, 0.352093, -0.00382626, 0.0398103, 0.93511, 0.5, 1, 0.350162, -0.00381224, 0.0396372, 0.935842, 0.533333, 1, 0.344845, -0.00377401, 0.0391596, 0.937835, 0.566667, 1, 0.339827, -0.00373757, 0.0387086, 0.939683, 0.6, 1, 0.337143, -0.00371825, 0.0384672, 0.94066, 0.633333, 1, 0.336951, -0.00371687, 0.0384501, 0.940729, 0.666667, 1, 0.338446, -0.00372758, 0.0385843, 0.940187, 0.7, 1, 0.341615, -0.00375053, 0.0388693, 0.939028, 0.733333, 1, 0.343411, -0.00376357, 0.0390308, 0.938366, 0.766667, 1, 0.34381, -0.00376638, 0.0390665, 0.938219, 0.8, 1, 0.342502, -0.00375697, 0.0389489, 0.938702, 0.833333, 1, 0.337946, -0.00372403, 0.0385396, 0.940369, 0.866667, 1, 0.333892, -0.00369477, 0.0381748, 0.941831, 0.9, 1, 0.332294, -0.00368309, 0.038031, 0.942402, 0.933333, 1, 0.329905, -0.00366584, 0.0378162, 0.943249, 0.966667, 1, 0.330092, -0.00366728, 0.0378329, 0.943183, 1, 1, 0.337581, -0.00372141, 0.0385066, 0.940501, 1.03333, 1, 0.347049, -0.00378973, 0.0393577, 0.937013, 1.06667, 1, 0.351371, -0.00382103, 0.0397458, 0.935384, 1.1, 1, 0.352093, -0.00382632, 0.0398104, 0.93511, 1.13333, 1, 0.350162, -0.00381226, 0.0396373, 0.935842, 1.16667, 1, 0.344845, -0.00377392, 0.0391596, 0.937835, 1.2, 1, 0.339828, -0.00373777, 0.0387087, 0.939683, 1.23333, 1, 0.337143, -0.00371822, 0.0384672, 0.94066, 1.26667, 1, 0.336951, -0.00371697, 0.03845, 0.940729, 1.3, 1, 0.338446, -0.00372753, 0.0385843, 0.940187)
tracks/22/type = "rotation_3d"
tracks/22/imported = true
tracks/22/enabled = true
tracks/22/path = NodePath("Skeleton_01/Skeleton3D:LeftHandThumb1")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = PackedFloat32Array(0, 1, 0.243084, 0.759398, 0.331398, 0.504381, 0.1, 1, 0.243406, 0.759102, 0.331022, 0.504917, 0.133333, 1, 0.242606, 0.759657, 0.330426, 0.504858, 0.166667, 1, 0.241666, 0.760315, 0.329673, 0.504812, 0.2, 1, 0.240642, 0.761053, 0.328739, 0.504797, 0.266667, 1, 0.239511, 0.761651, 0.328503, 0.504587, 0.3, 1, 0.239687, 0.761401, 0.329154, 0.504457, 0.333333, 1, 0.240939, 0.760406, 0.330605, 0.504412, 0.366667, 1, 0.242873, 0.759313, 0.331164, 0.504764, 0.4, 1, 0.244359, 0.758929, 0.330263, 0.505215, 0.466667, 1, 0.244657, 0.758447, 0.331233, 0.50516, 0.533333, 1, 0.244075, 0.758676, 0.33138, 0.505, 0.566667, 1, 0.243029, 0.75926, 0.331097, 0.504814, 0.6, 1, 0.242101, 0.759774, 0.330866, 0.504637, 0.633333, 1, 0.241844, 0.759895, 0.330881, 0.504568, 0.666667, 1, 0.243085, 0.759398, 0.331398, 0.504381, 0.733333, 1, 0.243406, 0.759102, 0.331022, 0.504917, 0.766667, 1, 0.242605, 0.759657, 0.330426, 0.504858, 0.8, 1, 0.241666, 0.760315, 0.329673, 0.504812, 0.833333, 1, 0.240642, 0.761054, 0.328738, 0.504797, 0.9, 1, 0.239511, 0.761651, 0.328503, 0.504587, 0.933333, 1, 0.239687, 0.761401, 0.329154, 0.504457, 0.966667, 1, 0.240939, 0.760406, 0.330605, 0.504412, 1, 1, 0.242873, 0.759313, 0.331164, 0.504764, 1.03333, 1, 0.244359, 0.758929, 0.330263, 0.505215, 1.1, 1, 0.244657, 0.758447, 0.331232, 0.50516, 1.16667, 1, 0.244075, 0.758676, 0.33138, 0.505, 1.2, 1, 0.243029, 0.75926, 0.331097, 0.504814, 1.23333, 1, 0.242101, 0.759774, 0.330866, 0.504637, 1.26667, 1, 0.241844, 0.759895, 0.330881, 0.504568, 1.3, 1, 0.243084, 0.759398, 0.331398, 0.504381)
tracks/23/type = "rotation_3d"
tracks/23/imported = true
tracks/23/enabled = true
tracks/23/path = NodePath("Skeleton_01/Skeleton3D:LeftHandThumb2")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = PackedFloat32Array(0, 1, 0.141811, -0.123704, -0.106339, 0.97636, 0.0333333, 1, 0.141811, -0.123704, -0.106339, 0.97636, 0.0666667, 1, 0.143427, -0.120766, -0.103256, 0.976823, 0.1, 1, 0.142961, -0.121611, -0.104142, 0.976692, 0.133333, 1, 0.141171, -0.124876, -0.107569, 0.976169, 0.166667, 1, 0.138874, -0.129112, -0.112023, 0.975446, 0.2, 1, 0.136481, -0.133588, -0.116737, 0.974628, 0.233333, 1, 0.135566, -0.135317, -0.11856, 0.974297, 0.266667, 1, 0.136206, -0.134107, -0.117284, 0.97453, 0.3, 1, 0.138512, -0.129785, -0.112732, 0.975327, 0.333333, 1, 0.143838, -0.120024, -0.102478, 0.976936, 0.366667, 1, 0.146406, -0.115417, -0.0976541, 0.977603, 0.4, 1, 0.144662, -0.118544, -0.100927, 0.977156, 0.433333, 1, 0.144006, -0.119733, -0.102176, 0.976978, 0.466667, 1, 0.144485, -0.11889, -0.101296, 0.977102, 0.5, 1, 0.144255, -0.11932, -0.101749, 0.977037, 0.533333, 1, 0.142469, -0.122522, -0.105102, 0.976547, 0.566667, 1, 0.141038, -0.125112, -0.107824, 0.97613, 0.6, 1, 0.141151, -0.124906, -0.107606, 0.976164, 0.633333, 1, 0.140336, -0.126397, -0.109179, 0.975915, 0.666667, 1, 0.141811, -0.123705, -0.106339, 0.97636, 0.7, 1, 0.143427, -0.120766, -0.103256, 0.976823, 0.733333, 1, 0.142961, -0.121611, -0.104142, 0.976692, 0.766667, 1, 0.141171, -0.124876, -0.107569, 0.976169, 0.8, 1, 0.138875, -0.129112, -0.112023, 0.975446, 0.833333, 1, 0.136481, -0.133588, -0.116737, 0.974628, 0.866667, 1, 0.135566, -0.135317, -0.11856, 0.974297, 0.9, 1, 0.136206, -0.134107, -0.117284, 0.97453, 0.933333, 1, 0.138512, -0.129785, -0.112732, 0.975327, 0.966667, 1, 0.143838, -0.120024, -0.102478, 0.976936, 1, 1, 0.146405, -0.115417, -0.097654, 0.977603, 1.03333, 1, 0.144662, -0.118544, -0.100927, 0.977156, 1.06667, 1, 0.144006, -0.119733, -0.102176, 0.976978, 1.1, 1, 0.144485, -0.11889, -0.101296, 0.977102, 1.13333, 1, 0.144255, -0.11932, -0.101749, 0.977037, 1.16667, 1, 0.142469, -0.122522, -0.105102, 0.976547, 1.2, 1, 0.141038, -0.125112, -0.107824, 0.97613, 1.23333, 1, 0.141151, -0.124906, -0.107606, 0.976164, 1.26667, 1, 0.140336, -0.126397, -0.109179, 0.975915, 1.3, 1, 0.141811, -0.123705, -0.106339, 0.97636)
tracks/24/type = "rotation_3d"
tracks/24/imported = true
tracks/24/enabled = true
tracks/24/path = NodePath("Skeleton_01/Skeleton3D:RightShoulder")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = PackedFloat32Array(0, 1, -0.517307, 0.430731, 0.541877, 0.503224, 0.0333333, 1, -0.517307, 0.430731, 0.541877, 0.503224, 0.0666667, 1, -0.515422, 0.433354, 0.539773, 0.505164, 0.133333, 1, -0.513313, 0.43516, 0.543241, 0.50203, 0.166667, 1, -0.516175, 0.431336, 0.545564, 0.499874, 0.2, 1, -0.518751, 0.42823, 0.545845, 0.499569, 0.233333, 1, -0.517132, 0.431081, 0.541117, 0.50392, 0.3, 1, -0.50679, 0.446778, 0.519458, 0.52318, 0.333333, 1, -0.506345, 0.448205, 0.512869, 0.528859, 0.366667, 1, -0.50752, 0.446941, 0.512228, 0.529423, 0.4, 1, -0.507112, 0.446932, 0.515603, 0.526539, 0.433333, 1, -0.507084, 0.44598, 0.521904, 0.521136, 0.466667, 1, -0.511256, 0.439588, 0.531253, 0.512982, 0.5, 1, -0.518665, 0.42906, 0.541535, 0.50362, 0.533333, 1, -0.522602, 0.423474, 0.546511, 0.498882, 0.566667, 1, -0.519454, 0.427359, 0.545549, 0.499909, 0.6, 1, -0.515022, 0.432504, 0.545113, 0.500544, 0.633333, 1, -0.51506, 0.432478, 0.545182, 0.500453, 0.666667, 1, -0.517307, 0.430731, 0.541877, 0.503224, 0.7, 1, -0.515421, 0.433353, 0.539774, 0.505163, 0.766667, 1, -0.513312, 0.435161, 0.543241, 0.502031, 0.8, 1, -0.516174, 0.431336, 0.545564, 0.499874, 0.833333, 1, -0.518751, 0.428231, 0.545844, 0.49957, 0.866667, 1, -0.517132, 0.431082, 0.541116, 0.503921, 0.933333, 1, -0.506788, 0.446778, 0.519459, 0.523181, 0.966667, 1, -0.506344, 0.448206, 0.512869, 0.52886, 1, 1, -0.507519, 0.446942, 0.512227, 0.529425, 1.03333, 1, -0.507112, 0.446932, 0.515603, 0.526539, 1.06667, 1, -0.507084, 0.44598, 0.521906, 0.521135, 1.1, 1, -0.511256, 0.439588, 0.531253, 0.512981, 1.13333, 1, -0.518667, 0.42906, 0.541535, 0.50362, 1.16667, 1, -0.522602, 0.423474, 0.546511, 0.498882, 1.2, 1, -0.519454, 0.42736, 0.545549, 0.499908, 1.23333, 1, -0.515022, 0.432502, 0.545114, 0.500546, 1.26667, 1, -0.51506, 0.432478, 0.545182, 0.500453, 1.3, 1, -0.517307, 0.430731, 0.541877, 0.503224)
tracks/25/type = "rotation_3d"
tracks/25/imported = true
tracks/25/enabled = true
tracks/25/path = NodePath("Skeleton_01/Skeleton3D:RightArm")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = PackedFloat32Array(0, 1, -0.553765, 0.0601758, -0.0271847, 0.830051, 0.0333333, 1, -0.553765, 0.0601758, -0.0271847, 0.830051, 0.0666667, 1, -0.553972, 0.0499798, -0.0448237, 0.829824, 0.1, 1, -0.552504, 0.0433087, -0.0595658, 0.83025, 0.133333, 1, -0.548539, 0.0398822, -0.0686579, 0.832347, 0.166667, 1, -0.542314, 0.0396232, -0.0693633, 0.83637, 0.2, 1, -0.538824, 0.0421112, -0.0597703, 0.83924, 0.233333, 1, -0.547925, 0.0453057, -0.0400401, 0.83434, 0.266667, 1, -0.570065, 0.0446013, -0.0213855, 0.82011, 0.3, 1, -0.591762, 0.0376957, -0.0132125, 0.805123, 0.333333, 1, -0.602708, 0.028156, -0.0104811, 0.797396, 0.366667, 1, -0.604993, 0.0216855, -0.00593549, 0.795913, 0.4, 1, -0.602664, 0.0207561, -0.00137491, 0.797724, 0.433333, 1, -0.594436, 0.0238595, -0.00314798, 0.803783, 0.466667, 1, -0.579022, 0.0287469, -0.0116039, 0.814722, 0.5, 1, -0.559784, 0.0373163, -0.0198172, 0.827561, 0.533333, 1, -0.547217, 0.0530321, -0.0183271, 0.835108, 0.566667, 1, -0.547277, 0.0709428, -0.00919099, 0.833889, 0.6, 1, -0.551832, 0.0786096, -0.00499147, 0.830227, 0.633333, 1, -0.55377, 0.0715498, -0.0133116, 0.829483, 0.666667, 1, -0.553765, 0.0601757, -0.0271847, 0.830051, 0.7, 1, -0.553974, 0.049979, -0.0448234, 0.829823, 0.733333, 1, -0.552506, 0.0433084, -0.0595653, 0.830249, 0.766667, 1, -0.548538, 0.0398823, -0.0686579, 0.832347, 0.8, 1, -0.544516, 0.0372797, -0.0637578, 0.835492, 0.833333, 1, -0.543564, 0.0369644, -0.0478199, 0.837189, 0.866667, 1, -0.555279, 0.0373577, -0.0216083, 0.830544, 0.9, 1, -0.579667, 0.0346765, 0.00297788, 0.81411, 0.933333, 1, -0.602883, 0.0269173, 0.0158055, 0.797219, 0.966667, 1, -0.614598, 0.0171141, 0.0212124, 0.78837, 1, 1, -0.616988, 0.0106108, 0.026174, 0.786466, 1.03333, 1, -0.614223, 0.00997208, 0.0292793, 0.788526, 1.06667, 1, -0.605175, 0.0136639, 0.024858, 0.795587, 1.1, 1, -0.588481, 0.0193849, 0.0126045, 0.808181, 1.13333, 1, -0.567538, 0.0291237, -0.000325788, 0.822832, 1.16667, 1, -0.553039, 0.0465619, -0.00395973, 0.831844, 1.2, 1, -0.551062, 0.0666795, -4.4533e-05, 0.831796, 1.23333, 1, -0.553697, 0.0765143, -0.000547006, 0.829195, 1.26667, 1, -0.554264, 0.0709959, -0.012123, 0.829219, 1.3, 1, -0.553765, 0.0601758, -0.0271848, 0.830051)
tracks/26/type = "rotation_3d"
tracks/26/imported = true
tracks/26/enabled = true
tracks/26/path = NodePath("Skeleton_01/Skeleton3D:RightForeArm")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = PackedFloat32Array(0, 1, 0.539103, -0.454935, 0.551511, 0.445238, 0.0333333, 1, 0.539103, -0.454935, 0.551511, 0.445238, 0.0666667, 1, 0.540146, -0.453702, 0.552509, 0.443994, 0.1, 1, 0.538832, -0.455384, 0.551108, 0.445607, 0.133333, 1, 0.536363, -0.458445, 0.54856, 0.448582, 0.166667, 1, 0.53583, -0.459097, 0.548027, 0.449202, 0.2, 1, 0.539496, -0.454592, 0.551781, 0.444778, 0.233333, 1, 0.541108, -0.452586, 0.553425, 0.44282, 0.266667, 1, 0.535488, -0.459487, 0.547684, 0.44963, 0.3, 1, 0.525955, -0.470831, 0.537929, 0.460783, 0.333333, 1, 0.518186, -0.479698, 0.530032, 0.46951, 0.366667, 1, 0.513253, -0.485146, 0.525064, 0.474881, 0.4, 1, 0.511402, -0.486976, 0.523379, 0.476862, 0.433333, 1, 0.51505, -0.482477, 0.527593, 0.472848, 0.466667, 1, 0.524706, -0.471158, 0.537789, 0.462036, 0.5, 1, 0.537862, -0.455565, 0.551026, 0.446695, 0.533333, 1, 0.548133, -0.442926, 0.5612, 0.434075, 0.566667, 1, 0.55041, -0.44027, 0.563295, 0.431174, 0.6, 1, 0.547081, -0.444752, 0.559771, 0.435379, 0.633333, 1, 0.543, -0.449996, 0.555563, 0.440459, 0.666667, 1, 0.539103, -0.454936, 0.551511, 0.445239, 0.7, 1, 0.540142, -0.453708, 0.552504, 0.443998, 0.733333, 1, 0.53883, -0.455387, 0.551104, 0.44561, 0.766667, 1, 0.536363, -0.458443, 0.548562, 0.44858, 0.8, 1, 0.533505, -0.46192, 0.545648, 0.451963, 0.833333, 1, 0.534488, -0.460738, 0.546657, 0.450788, 0.866667, 1, 0.533087, -0.462435, 0.545215, 0.452452, 0.9, 1, 0.524166, -0.472945, 0.536095, 0.462791, 0.933333, 1, 0.511679, -0.487002, 0.523319, 0.476603, 0.966667, 1, 0.50205, -0.497314, 0.513519, 0.486747, 1, 1, 0.496488, -0.50306, 0.507901, 0.492409, 1.03333, 1, 0.495195, -0.504194, 0.506773, 0.493711, 1.06667, 1, 0.500576, -0.498181, 0.512726, 0.488213, 1.1, 1, 0.512901, -0.484589, 0.525652, 0.47518, 1.13333, 1, 0.529039, -0.466267, 0.541954, 0.457164, 1.16667, 1, 0.541963, -0.450808, 0.554865, 0.441784, 1.2, 1, 0.546496, -0.445343, 0.559281, 0.436138, 1.23333, 1, 0.545137, -0.447242, 0.557781, 0.437814, 1.26667, 1, 0.542475, -0.450658, 0.555026, 0.441105, 1.3, 1, 0.539103, -0.454936, 0.551511, 0.445238)
tracks/27/type = "rotation_3d"
tracks/27/imported = true
tracks/27/enabled = true
tracks/27/path = NodePath("Skeleton_01/Skeleton3D:RightHand")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = PackedFloat32Array(0, 1, 0.11168, 0.345083, 0.209673, 0.90801, 0.0333333, 1, 0.11168, 0.345083, 0.209673, 0.90801, 0.0666667, 1, 0.115928, 0.340622, 0.202329, 0.910824, 0.1, 1, 0.121623, 0.333517, 0.192874, 0.914754, 0.133333, 1, 0.119858, 0.329954, 0.196768, 0.915449, 0.166667, 1, 0.108225, 0.334209, 0.217339, 0.91069, 0.2, 1, 0.0610093, 0.335686, 0.208279, 0.916631, 0.233333, 1, 0.0305061, 0.332665, 0.212079, 0.918382, 0.266667, 1, 0.0448732, 0.334459, 0.214204, 0.916646, 0.3, 1, 0.0589105, 0.330096, 0.201226, 0.920366, 0.333333, 1, 0.0642999, 0.31234, 0.196384, 0.927223, 0.366667, 1, 0.0760041, 0.29146, 0.204755, 0.931316, 0.4, 1, 0.0842451, 0.277645, 0.207557, 0.934203, 0.433333, 1, 0.0865558, 0.276172, 0.21163, 0.933515, 0.466667, 1, 0.0759027, 0.308423, 0.194129, 0.928131, 0.5, 1, 0.0569593, 0.369213, 0.163407, 0.913091, 0.533333, 1, 0.048564, 0.423555, 0.15365, 0.891423, 0.566667, 1, 0.0505012, 0.4465, 0.154788, 0.879845, 0.6, 1, 0.0636294, 0.422477, 0.161591, 0.88958, 0.633333, 1, 0.0833104, 0.382746, 0.170946, 0.90407, 0.666667, 1, 0.11168, 0.345083, 0.209674, 0.90801, 0.7, 1, 0.115938, 0.340618, 0.202333, 0.910823, 0.733333, 1, 0.121628, 0.333515, 0.192875, 0.914753, 0.766667, 1, 0.119857, 0.329955, 0.196768, 0.915449, 0.8, 1, 0.106311, 0.332232, 0.21998, 0.911004, 0.833333, 1, 0.0570985, 0.331216, 0.213805, 0.917237, 0.866667, 1, 0.024915, 0.32556, 0.220661, 0.919075, 0.9, 1, 0.0378152, 0.325103, 0.226083, 0.917477, 0.933333, 1, 0.0503263, 0.318615, 0.216064, 0.921557, 0.966667, 1, 0.0542611, 0.299176, 0.213397, 0.928446, 1, 1, 0.0653628, 0.277878, 0.22289, 0.932111, 1.03333, 1, 0.0743139, 0.26476, 0.225317, 0.934672, 1.06667, 1, 0.0777064, 0.264736, 0.227734, 0.933817, 1.1, 1, 0.0680059, 0.298781, 0.207582, 0.928986, 1.13333, 1, 0.0502343, 0.361631, 0.173607, 0.914637, 1.16667, 1, 0.0436929, 0.418206, 0.160666, 0.892962, 1.2, 1, 0.0477744, 0.443171, 0.159077, 0.880915, 1.23333, 1, 0.0624662, 0.420846, 0.163696, 0.890051, 1.26667, 1, 0.0829958, 0.382308, 0.171524, 0.904175, 1.3, 1, 0.11168, 0.345083, 0.209673, 0.90801)
tracks/28/type = "rotation_3d"
tracks/28/imported = true
tracks/28/enabled = true
tracks/28/path = NodePath("Skeleton_01/Skeleton3D:RightHandIndex1")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = PackedFloat32Array(0, 1, 0.254599, -0.612514, 0.217054, 0.716166, 0.0333333, 1, 0.254599, -0.612514, 0.217054, 0.716166, 0.1, 1, 0.250542, -0.615822, 0.224358, 0.7125, 0.133333, 1, 0.241196, -0.619142, 0.217758, 0.714891, 0.166667, 1, 0.248223, -0.614682, 0.211562, 0.718187, 0.2, 1, 0.245722, -0.614044, 0.200951, 0.722626, 0.233333, 1, 0.232816, -0.618556, 0.191711, 0.725556, 0.266667, 1, 0.229563, -0.620141, 0.192283, 0.725089, 0.3, 1, 0.233903, -0.618256, 0.192898, 0.725148, 0.333333, 1, 0.244254, -0.613433, 0.19329, 0.725726, 0.366667, 1, 0.254235, -0.608847, 0.195172, 0.725657, 0.4, 1, 0.252922, -0.609104, 0.192534, 0.726604, 0.433333, 1, 0.241094, -0.613551, 0.183939, 0.729106, 0.466667, 1, 0.225723, -0.619714, 0.176696, 0.730604, 0.533333, 1, 0.186347, -0.636421, 0.170031, 0.728926, 0.566667, 1, 0.186632, -0.637736, 0.175974, 0.726288, 0.6, 1, 0.21135, -0.630466, 0.194218, 0.721196, 0.633333, 1, 0.235983, -0.621344, 0.209618, 0.71715, 0.666667, 1, 0.254599, -0.612514, 0.217054, 0.716166, 0.733333, 1, 0.250542, -0.615822, 0.224358, 0.7125, 0.766667, 1, 0.241195, -0.619142, 0.217758, 0.714891, 0.8, 1, 0.248223, -0.614682, 0.211562, 0.718187, 0.833333, 1, 0.245722, -0.614044, 0.200951, 0.722626, 0.866667, 1, 0.232815, -0.618556, 0.19171, 0.725556, 0.9, 1, 0.229563, -0.620141, 0.192284, 0.725088, 0.933333, 1, 0.233902, -0.618256, 0.192898, 0.725148, 0.966667, 1, 0.244254, -0.613433, 0.19329, 0.725726, 1, 1, 0.254235, -0.608847, 0.195172, 0.725657, 1.03333, 1, 0.252923, -0.609103, 0.192535, 0.726604, 1.06667, 1, 0.241094, -0.613551, 0.183939, 0.729106, 1.1, 1, 0.225723, -0.619714, 0.176696, 0.730604, 1.16667, 1, 0.186347, -0.636421, 0.170031, 0.728926, 1.2, 1, 0.186632, -0.637736, 0.175975, 0.726288, 1.23333, 1, 0.21135, -0.630466, 0.194219, 0.721195, 1.26667, 1, 0.235982, -0.621344, 0.209617, 0.717151, 1.3, 1, 0.2546, -0.612513, 0.217055, 0.716166)
tracks/29/type = "rotation_3d"
tracks/29/imported = true
tracks/29/enabled = true
tracks/29/path = NodePath("Skeleton_01/Skeleton3D:RightHandIndex2")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = PackedFloat32Array(0, 1, 0.355065, 0.00287351, -0.0230489, 0.934553, 0.0333333, 1, 0.355065, 0.00287351, -0.0230489, 0.934553, 0.0666667, 1, 0.360442, 0.00304823, -0.0229119, 0.932495, 0.1, 1, 0.362387, 0.00311146, -0.0228622, 0.931742, 0.133333, 1, 0.362788, 0.0031246, -0.0228518, 0.931586, 0.166667, 1, 0.365027, 0.00319763, -0.0227944, 0.930712, 0.2, 1, 0.36183, 0.00309332, -0.0228766, 0.931958, 0.233333, 1, 0.355334, 0.0028823, -0.0230421, 0.934451, 0.266667, 1, 0.352073, 0.00277629, -0.0231247, 0.935683, 0.3, 1, 0.351629, 0.002762, -0.023136, 0.93585, 0.333333, 1, 0.35265, 0.00279514, -0.0231101, 0.935466, 0.366667, 1, 0.357691, 0.00295874, -0.0229821, 0.933553, 0.4, 1, 0.361356, 0.0030779, -0.0228885, 0.932142, 0.433333, 1, 0.361193, 0.0030727, -0.0228928, 0.932205, 0.466667, 1, 0.359082, 0.0030039, -0.0229465, 0.933019, 0.5, 1, 0.355468, 0.00288668, -0.0230386, 0.9344, 0.533333, 1, 0.350044, 0.00271055, -0.0231758, 0.936443, 0.566667, 1, 0.345044, 0.0025486, -0.0233013, 0.938294, 0.6, 1, 0.344402, 0.0025277, -0.0233171, 0.938529, 0.633333, 1, 0.347736, 0.00263581, -0.0232339, 0.937301, 0.666667, 1, 0.355065, 0.00287351, -0.0230489, 0.934553, 0.7, 1, 0.360442, 0.00304825, -0.0229121, 0.932495, 0.733333, 1, 0.362387, 0.00311166, -0.0228622, 0.931742, 0.766667, 1, 0.362788, 0.00312453, -0.0228519, 0.931586, 0.8, 1, 0.365027, 0.00319749, -0.0227944, 0.930712, 0.833333, 1, 0.36183, 0.00309335, -0.0228766, 0.931958, 0.866667, 1, 0.355334, 0.00288233, -0.0230422, 0.934451, 0.9, 1, 0.352073, 0.00277622, -0.0231247, 0.935683, 0.933333, 1, 0.351629, 0.00276203, -0.0231359, 0.93585, 0.966667, 1, 0.35265, 0.00279501, -0.02311, 0.935466, 1, 1, 0.357691, 0.00295874, -0.0229821, 0.933553, 1.03333, 1, 0.361356, 0.003078, -0.0228887, 0.932142, 1.06667, 1, 0.361193, 0.00307264, -0.0228928, 0.932205, 1.1, 1, 0.359082, 0.00300394, -0.0229467, 0.933019, 1.13333, 1, 0.355468, 0.00288649, -0.0230386, 0.9344, 1.16667, 1, 0.350044, 0.0027106, -0.0231757, 0.936442, 1.2, 1, 0.345044, 0.00254859, -0.0233013, 0.938294, 1.23333, 1, 0.344402, 0.00252786, -0.0233175, 0.938529, 1.26667, 1, 0.347736, 0.00263587, -0.023234, 0.937301, 1.3, 1, 0.355065, 0.00287347, -0.0230489, 0.934553)
tracks/30/type = "rotation_3d"
tracks/30/imported = true
tracks/30/enabled = true
tracks/30/path = NodePath("Skeleton_01/Skeleton3D:RightHandMiddle1")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = PackedFloat32Array(0, 1, 0.231015, -0.657325, 0.170521, 0.696763, 0.0333333, 1, 0.231015, -0.657325, 0.170521, 0.696763, 0.0666667, 1, 0.228996, -0.658344, 0.172174, 0.69606, 0.1, 1, 0.216163, -0.66315, 0.171183, 0.695846, 0.133333, 1, 0.208973, -0.665356, 0.167729, 0.696778, 0.166667, 1, 0.224554, -0.659202, 0.165356, 0.698345, 0.2, 1, 0.230537, -0.656318, 0.161341, 0.700049, 0.233333, 1, 0.221226, -0.659445, 0.157215, 0.701053, 0.266667, 1, 0.216213, -0.661316, 0.156912, 0.700922, 0.3, 1, 0.218595, -0.660154, 0.154867, 0.701733, 0.333333, 1, 0.226682, -0.656477, 0.150698, 0.703523, 0.366667, 1, 0.234601, -0.652941, 0.14808, 0.704771, 0.4, 1, 0.233688, -0.653102, 0.145874, 0.705385, 0.433333, 1, 0.226387, -0.656039, 0.144281, 0.705369, 0.466667, 1, 0.216922, -0.660379, 0.146596, 0.703814, 0.5, 1, 0.201065, -0.667469, 0.150923, 0.700914, 0.533333, 1, 0.179577, -0.675736, 0.151294, 0.698744, 0.566667, 1, 0.174703, -0.678146, 0.154012, 0.697049, 0.6, 1, 0.194012, -0.672744, 0.164317, 0.69482, 0.633333, 1, 0.215817, -0.664955, 0.172309, 0.693951, 0.666667, 1, 0.231015, -0.657325, 0.170521, 0.696763, 0.7, 1, 0.228996, -0.658344, 0.172175, 0.69606, 0.733333, 1, 0.216164, -0.66315, 0.171184, 0.695846, 0.766667, 1, 0.208972, -0.665356, 0.167727, 0.696778, 0.8, 1, 0.224555, -0.659202, 0.165357, 0.698345, 0.833333, 1, 0.230537, -0.656318, 0.161341, 0.700049, 0.866667, 1, 0.221226, -0.659445, 0.157215, 0.701053, 0.9, 1, 0.216213, -0.661316, 0.156912, 0.700922, 0.933333, 1, 0.218594, -0.660154, 0.154867, 0.701733, 0.966667, 1, 0.226682, -0.656476, 0.150698, 0.703523, 1, 1, 0.234601, -0.652941, 0.148081, 0.704771, 1.03333, 1, 0.233689, -0.653102, 0.145875, 0.705385, 1.06667, 1, 0.226387, -0.656038, 0.144281, 0.705369, 1.1, 1, 0.216923, -0.660379, 0.146596, 0.703814, 1.13333, 1, 0.201065, -0.667469, 0.150923, 0.700914, 1.16667, 1, 0.179577, -0.675736, 0.151294, 0.698744, 1.2, 1, 0.174704, -0.678146, 0.154013, 0.697048, 1.23333, 1, 0.194011, -0.672744, 0.164316, 0.69482, 1.26667, 1, 0.215815, -0.664955, 0.172307, 0.693951, 1.3, 1, 0.231016, -0.657325, 0.170522, 0.696762)
tracks/31/type = "rotation_3d"
tracks/31/imported = true
tracks/31/enabled = true
tracks/31/path = NodePath("Skeleton_01/Skeleton3D:RightHandMiddle2")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = PackedFloat32Array(0, 1, 0.343065, -0.00110395, -0.0211528, 0.939073, 0.0333333, 1, 0.343065, -0.00110395, -0.0211528, 0.939073, 0.0666667, 1, 0.348355, -0.00113164, -0.021561, 0.937114, 0.1, 1, 0.349753, -0.00113894, -0.0216691, 0.93659, 0.133333, 1, 0.348807, -0.00113404, -0.0215959, 0.936945, 0.166667, 1, 0.347363, -0.00112645, -0.0214845, 0.937484, 0.2, 1, 0.339811, -0.00108692, -0.0209017, 0.940261, 0.233333, 1, 0.330768, -0.00103986, -0.0202042, 0.943495, 0.266667, 1, 0.3277, -0.00102374, -0.0199678, 0.94457, 0.3, 1, 0.32762, -0.00102323, -0.0199616, 0.944598, 0.333333, 1, 0.329192, -0.00103162, -0.0200828, 0.944049, 0.366667, 1, 0.333854, -0.00105572, -0.0204423, 0.942402, 0.4, 1, 0.335899, -0.00106624, -0.0205999, 0.941672, 0.433333, 1, 0.333604, -0.00105444, -0.020423, 0.942491, 0.466667, 1, 0.329693, -0.00103408, -0.0201214, 0.943873, 0.5, 1, 0.324625, -0.00100757, -0.019731, 0.945636, 0.533333, 1, 0.320914, -0.000988252, -0.019445, 0.946908, 0.566667, 1, 0.322355, -0.000995724, -0.019556, 0.946416, 0.633333, 1, 0.335449, -0.00106419, -0.0205652, 0.941833, 0.666667, 1, 0.343065, -0.00110395, -0.0211527, 0.939073, 0.7, 1, 0.348355, -0.00113172, -0.021561, 0.937114, 0.733333, 1, 0.349754, -0.00113881, -0.021669, 0.93659, 0.766667, 1, 0.348807, -0.00113405, -0.0215961, 0.936945, 0.8, 1, 0.347363, -0.00112643, -0.0214845, 0.937484, 0.833333, 1, 0.339811, -0.00108695, -0.0209017, 0.940261, 0.866667, 1, 0.330768, -0.00103964, -0.0202044, 0.943495, 0.9, 1, 0.3277, -0.00102361, -0.0199678, 0.94457, 0.933333, 1, 0.32762, -0.00102327, -0.0199616, 0.944598, 0.966667, 1, 0.329192, -0.00103144, -0.0200829, 0.944049, 1, 1, 0.333854, -0.00105579, -0.0204422, 0.942402, 1.03333, 1, 0.335899, -0.00106646, -0.0206, 0.941672, 1.06667, 1, 0.333604, -0.0010546, -0.020423, 0.942491, 1.1, 1, 0.329693, -0.00103398, -0.0201213, 0.943873, 1.13333, 1, 0.324625, -0.00100774, -0.0197308, 0.945636, 1.16667, 1, 0.320914, -0.000988302, -0.019445, 0.946908, 1.2, 1, 0.322355, -0.000995808, -0.019556, 0.946416, 1.23333, 1, 0.328945, -0.00103012, -0.0200639, 0.944135, 1.26667, 1, 0.335449, -0.00106417, -0.0205653, 0.941833, 1.3, 1, 0.343065, -0.00110402, -0.0211528, 0.939073)
tracks/32/type = "rotation_3d"
tracks/32/imported = true
tracks/32/enabled = true
tracks/32/path = NodePath("Skeleton_01/Skeleton3D:RightHandPinky1")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = PackedFloat32Array(0, 1, 0.196801, -0.706473, 0.0796504, 0.675145, 0.0333333, 1, 0.196801, -0.706473, 0.0796504, 0.675145, 0.0666667, 1, 0.189693, -0.708808, 0.0798052, 0.674714, 0.1, 1, 0.174826, -0.71329, 0.0789209, 0.674111, 0.133333, 1, 0.172069, -0.7136, 0.0744343, 0.675001, 0.166667, 1, 0.199031, -0.704014, 0.0652985, 0.678592, 0.2, 1, 0.215353, -0.696668, 0.0526739, 0.682278, 0.233333, 1, 0.211893, -0.696824, 0.0437506, 0.68383, 0.266667, 1, 0.208528, -0.698112, 0.0442471, 0.683519, 0.3, 1, 0.212906, -0.696956, 0.0477757, 0.683111, 0.333333, 1, 0.219067, -0.694984, 0.0503471, 0.682988, 0.366667, 1, 0.219382, -0.694781, 0.0496842, 0.683142, 0.4, 1, 0.21552, -0.696224, 0.0490687, 0.682946, 0.433333, 1, 0.211012, -0.698248, 0.0505824, 0.682177, 0.466667, 1, 0.204217, -0.701834, 0.0575284, 0.680011, 0.5, 1, 0.188672, -0.709061, 0.0686898, 0.675957, 0.533333, 1, 0.165648, -0.717571, 0.0743737, 0.6724, 0.566667, 1, 0.156117, -0.721106, 0.0781538, 0.670466, 0.6, 1, 0.167191, -0.718559, 0.084966, 0.669702, 0.633333, 1, 0.183723, -0.71273, 0.0858609, 0.671484, 0.666667, 1, 0.1968, -0.706473, 0.07965, 0.675146, 0.7, 1, 0.189692, -0.708808, 0.0798047, 0.674714, 0.733333, 1, 0.174825, -0.71329, 0.0789197, 0.674111, 0.766667, 1, 0.172069, -0.7136, 0.074434, 0.675001, 0.8, 1, 0.199031, -0.704014, 0.0652983, 0.678592, 0.833333, 1, 0.215353, -0.696668, 0.052674, 0.682277, 0.866667, 1, 0.211893, -0.696824, 0.0437504, 0.68383, 0.9, 1, 0.208528, -0.698112, 0.0442471, 0.683519, 0.933333, 1, 0.212906, -0.696956, 0.0477756, 0.683111, 0.966667, 1, 0.219067, -0.694984, 0.0503473, 0.682987, 1, 1, 0.219382, -0.694781, 0.0496844, 0.683142, 1.03333, 1, 0.21552, -0.696224, 0.0490686, 0.682946, 1.06667, 1, 0.211012, -0.698248, 0.0505823, 0.682177, 1.1, 1, 0.204217, -0.701834, 0.057528, 0.680011, 1.13333, 1, 0.188672, -0.709061, 0.0686902, 0.675957, 1.16667, 1, 0.165647, -0.717571, 0.074373, 0.6724, 1.2, 1, 0.156117, -0.721106, 0.078154, 0.670466, 1.23333, 1, 0.16719, -0.718559, 0.0849657, 0.669702, 1.26667, 1, 0.183723, -0.712729, 0.0858611, 0.671484, 1.3, 1, 0.196801, -0.706473, 0.0796505, 0.675145)
tracks/33/type = "rotation_3d"
tracks/33/imported = true
tracks/33/enabled = true
tracks/33/path = NodePath("Skeleton_01/Skeleton3D:RightHandPinky2")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/keys = PackedFloat32Array(0, 1, 0.158162, -0.00366929, -0.0446596, 0.986396, 0.0333333, 1, 0.158162, -0.00366929, -0.0446596, 0.986396, 0.0666667, 1, 0.15696, -0.00368475, -0.0444095, 0.986599, 0.1, 1, 0.154464, -0.00371656, -0.0438905, 0.987016, 0.133333, 1, 0.152159, -0.00374608, -0.0434111, 0.987395, 0.2, 1, 0.151523, -0.00375399, -0.0432787, 0.987499, 0.233333, 1, 0.150086, -0.00377251, -0.0429798, 0.987731, 0.266667, 1, 0.14862, -0.00379131, -0.0426746, 0.987966, 0.333333, 1, 0.148734, -0.00378966, -0.0426983, 0.987948, 0.366667, 1, 0.150222, -0.00377079, -0.043008, 0.987709, 0.4, 1, 0.151339, -0.00375633, -0.0432404, 0.987529, 0.466667, 1, 0.14992, -0.00377466, -0.0429451, 0.987758, 0.5, 1, 0.147816, -0.00380138, -0.0425074, 0.988094, 0.533333, 1, 0.145636, -0.00382923, -0.0420535, 0.988437, 0.566667, 1, 0.147209, -0.00380926, -0.042381, 0.98819, 0.6, 1, 0.150982, -0.00376119, -0.0431662, 0.987586, 0.633333, 1, 0.153832, -0.00372464, -0.043759, 0.987121, 0.666667, 1, 0.158162, -0.00366923, -0.0446597, 0.986396, 0.7, 1, 0.15696, -0.00368459, -0.0444095, 0.986599, 0.733333, 1, 0.154464, -0.00371651, -0.0438906, 0.987016, 0.766667, 1, 0.15216, -0.00374613, -0.0434112, 0.987395, 0.833333, 1, 0.151523, -0.0037541, -0.0432787, 0.987499, 0.9, 1, 0.14862, -0.00379122, -0.0426746, 0.987966, 0.966667, 1, 0.148734, -0.00378983, -0.0426983, 0.987948, 1, 1, 0.150222, -0.00377072, -0.043008, 0.987709, 1.03333, 1, 0.151338, -0.00375644, -0.0432404, 0.987529, 1.1, 1, 0.14992, -0.0037746, -0.0429451, 0.987758, 1.13333, 1, 0.147816, -0.00380158, -0.0425072, 0.988094, 1.16667, 1, 0.145636, -0.00382922, -0.0420536, 0.988437, 1.2, 1, 0.147209, -0.00380911, -0.042381, 0.98819, 1.23333, 1, 0.150982, -0.00376105, -0.0431662, 0.987586, 1.26667, 1, 0.153831, -0.00372456, -0.0437589, 0.987121, 1.3, 1, 0.158162, -0.00366932, -0.0446597, 0.986396)
tracks/34/type = "rotation_3d"
tracks/34/imported = true
tracks/34/enabled = true
tracks/34/path = NodePath("Skeleton_01/Skeleton3D:RightHandRing1")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/keys = PackedFloat32Array(0, 1, 0.212221, -0.679038, 0.132141, 0.690223, 0.0333333, 1, 0.212221, -0.679038, 0.132141, 0.690223, 0.0666667, 1, 0.205089, -0.681117, 0.130607, 0.690622, 0.1, 1, 0.189962, -0.68542, 0.130078, 0.690792, 0.133333, 1, 0.18558, -0.686468, 0.12742, 0.691437, 0.166667, 1, 0.208978, -0.679479, 0.122427, 0.692567, 0.2, 1, 0.222329, -0.674863, 0.115987, 0.694029, 0.233333, 1, 0.217681, -0.676121, 0.111691, 0.694982, 0.266667, 1, 0.214857, -0.677108, 0.113114, 0.69467, 0.3, 1, 0.219462, -0.675748, 0.115114, 0.694226, 0.333333, 1, 0.227236, -0.673085, 0.113618, 0.694558, 0.4, 1, 0.225517, -0.67322, 0.104951, 0.69635, 0.433333, 1, 0.220093, -0.675309, 0.106217, 0.69587, 0.466667, 1, 0.212335, -0.678652, 0.113877, 0.693814, 0.5, 1, 0.195846, -0.685047, 0.124735, 0.690504, 0.533333, 1, 0.173097, -0.692537, 0.130103, 0.688115, 0.566667, 1, 0.165833, -0.695117, 0.133833, 0.686586, 0.6, 1, 0.180545, -0.691463, 0.140583, 0.685214, 0.633333, 1, 0.198987, -0.685261, 0.14147, 0.686154, 0.666667, 1, 0.212221, -0.679038, 0.132141, 0.690223, 0.7, 1, 0.205089, -0.681117, 0.130607, 0.690622, 0.733333, 1, 0.189962, -0.68542, 0.130078, 0.690792, 0.766667, 1, 0.18558, -0.686468, 0.12742, 0.691437, 0.8, 1, 0.208978, -0.679479, 0.122427, 0.692567, 0.833333, 1, 0.222329, -0.674863, 0.115987, 0.694029, 0.866667, 1, 0.21768, -0.676121, 0.11169, 0.694982, 0.9, 1, 0.214857, -0.677108, 0.113114, 0.69467, 0.933333, 1, 0.219462, -0.675748, 0.115114, 0.694226, 0.966667, 1, 0.227235, -0.673085, 0.113617, 0.694558, 1.03333, 1, 0.225517, -0.673219, 0.104951, 0.69635, 1.06667, 1, 0.220093, -0.675309, 0.106217, 0.69587, 1.1, 1, 0.212335, -0.678652, 0.113877, 0.693814, 1.13333, 1, 0.195845, -0.685047, 0.124734, 0.690504, 1.16667, 1, 0.173097, -0.692537, 0.130103, 0.688115, 1.2, 1, 0.165834, -0.695117, 0.133834, 0.686586, 1.23333, 1, 0.180544, -0.691464, 0.140583, 0.685214, 1.26667, 1, 0.198987, -0.685261, 0.14147, 0.686154, 1.3, 1, 0.212221, -0.679038, 0.132142, 0.690223)
tracks/35/type = "rotation_3d"
tracks/35/imported = true
tracks/35/enabled = true
tracks/35/path = NodePath("Skeleton_01/Skeleton3D:RightHandRing2")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/keys = PackedFloat32Array(0, 1, 0.236516, 0.00439189, -0.0356195, 0.970964, 0.0333333, 1, 0.236516, 0.00439189, -0.0356195, 0.970964, 0.0666667, 1, 0.240781, 0.00442415, -0.0361095, 0.969898, 0.1, 1, 0.241911, 0.0044326, -0.0362394, 0.969611, 0.133333, 1, 0.238874, 0.00440967, -0.0358904, 0.970377, 0.166667, 1, 0.232406, 0.00436059, -0.0351468, 0.971974, 0.2, 1, 0.221347, 0.00427692, -0.0338745, 0.974597, 0.233333, 1, 0.209043, 0.00418307, -0.0324574, 0.977359, 0.266667, 1, 0.202793, 0.0041352, -0.0317374, 0.978698, 0.3, 1, 0.200487, 0.00411772, -0.0314716, 0.979182, 0.333333, 1, 0.201049, 0.0041219, -0.0315364, 0.979065, 0.366667, 1, 0.207142, 0.00416867, -0.0322386, 0.977771, 0.4, 1, 0.210345, 0.00419307, -0.0326078, 0.977074, 0.433333, 1, 0.208358, 0.00417785, -0.0323788, 0.977508, 0.466667, 1, 0.205431, 0.00415535, -0.0320414, 0.978138, 0.5, 1, 0.202209, 0.00413058, -0.0316701, 0.978822, 0.533333, 1, 0.203178, 0.00413821, -0.0317817, 0.978617, 0.566667, 1, 0.21291, 0.00421259, -0.032903, 0.976509, 0.6, 1, 0.226475, 0.00431568, -0.0344645, 0.973398, 0.633333, 1, 0.233004, 0.00436524, -0.0352157, 0.971828, 0.666667, 1, 0.236516, 0.00439187, -0.0356194, 0.970964, 0.7, 1, 0.240781, 0.00442401, -0.0361097, 0.969898, 0.733333, 1, 0.241911, 0.00443256, -0.0362394, 0.969611, 0.766667, 1, 0.238874, 0.00440955, -0.0358904, 0.970377, 0.8, 1, 0.232406, 0.00436074, -0.0351468, 0.971974, 0.833333, 1, 0.221347, 0.00427679, -0.0338742, 0.974597, 0.866667, 1, 0.209043, 0.00418333, -0.0324576, 0.977359, 0.9, 1, 0.202793, 0.0041353, -0.0317374, 0.978698, 0.933333, 1, 0.200487, 0.00411777, -0.0314717, 0.979182, 0.966667, 1, 0.201049, 0.00412186, -0.0315363, 0.979065, 1, 1, 0.207142, 0.00416858, -0.0322385, 0.977771, 1.03333, 1, 0.210345, 0.00419308, -0.0326077, 0.977074, 1.06667, 1, 0.208358, 0.00417789, -0.0323788, 0.977508, 1.1, 1, 0.205431, 0.00415554, -0.0320413, 0.978138, 1.13333, 1, 0.202209, 0.00413088, -0.0316701, 0.978821, 1.16667, 1, 0.203178, 0.00413805, -0.0317817, 0.978617, 1.2, 1, 0.21291, 0.00421247, -0.032903, 0.976509, 1.23333, 1, 0.226475, 0.00431575, -0.0344645, 0.973398, 1.26667, 1, 0.233004, 0.00436528, -0.0352155, 0.971828, 1.3, 1, 0.236516, 0.00439195, -0.0356194, 0.970964)
tracks/36/type = "rotation_3d"
tracks/36/imported = true
tracks/36/enabled = true
tracks/36/path = NodePath("Skeleton_01/Skeleton3D:RightHandThumb1")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/keys = PackedFloat32Array(0, 1, 0.388205, -0.741221, -0.197249, 0.510864, 0.0333333, 1, 0.388205, -0.741221, -0.197249, 0.510864, 0.0666667, 1, 0.393769, -0.741324, -0.194375, 0.507546, 0.1, 1, 0.395136, -0.741984, -0.195031, 0.505263, 0.133333, 1, 0.395588, -0.742317, -0.195221, 0.504346, 0.166667, 1, 0.399472, -0.742156, -0.196687, 0.500941, 0.2, 1, 0.400723, -0.746697, -0.199581, 0.491968, 0.233333, 1, 0.398531, -0.750547, -0.200652, 0.487433, 0.266667, 1, 0.398249, -0.750898, -0.200446, 0.487208, 0.3, 1, 0.394608, -0.754847, -0.199764, 0.48434, 0.333333, 1, 0.387901, -0.760836, -0.198144, 0.48104, 0.366667, 1, 0.389411, -0.758897, -0.198358, 0.482793, 0.4, 1, 0.396181, -0.749693, -0.198656, 0.491464, 0.433333, 1, 0.394017, -0.743975, -0.195135, 0.503165, 0.466667, 1, 0.384112, -0.742656, -0.192171, 0.5138, 0.5, 1, 0.37236, -0.744116, -0.190367, 0.52096, 0.533333, 1, 0.365687, -0.745765, -0.189481, 0.523646, 0.566667, 1, 0.364416, -0.746365, -0.189133, 0.523803, 0.6, 1, 0.366962, -0.745786, -0.189119, 0.522853, 0.633333, 1, 0.375432, -0.74395, -0.189902, 0.51916, 0.666667, 1, 0.388205, -0.74122, -0.197249, 0.510864, 0.7, 1, 0.39377, -0.741324, -0.194375, 0.507546, 0.733333, 1, 0.395137, -0.741984, -0.195032, 0.505262, 0.766667, 1, 0.395588, -0.742317, -0.195221, 0.504346, 0.8, 1, 0.399472, -0.742156, -0.196687, 0.500941, 0.833333, 1, 0.400723, -0.746697, -0.199581, 0.491968, 0.866667, 1, 0.398531, -0.750547, -0.200652, 0.487432, 0.9, 1, 0.398249, -0.750898, -0.200446, 0.487208, 0.933333, 1, 0.394608, -0.754847, -0.199764, 0.48434, 0.966667, 1, 0.387901, -0.760836, -0.198144, 0.481041, 1, 1, 0.389411, -0.758897, -0.198358, 0.482793, 1.03333, 1, 0.396181, -0.749693, -0.198656, 0.491464, 1.06667, 1, 0.394017, -0.743975, -0.195135, 0.503165, 1.1, 1, 0.384111, -0.742656, -0.192171, 0.5138, 1.13333, 1, 0.37236, -0.744116, -0.190367, 0.52096, 1.16667, 1, 0.365687, -0.745765, -0.189481, 0.523646, 1.2, 1, 0.364416, -0.746365, -0.189133, 0.523803, 1.23333, 1, 0.366962, -0.745786, -0.189119, 0.522853, 1.26667, 1, 0.375432, -0.74395, -0.189902, 0.51916, 1.3, 1, 0.388205, -0.74122, -0.197249, 0.510864)
tracks/37/type = "rotation_3d"
tracks/37/imported = true
tracks/37/enabled = true
tracks/37/path = NodePath("Skeleton_01/Skeleton3D:RightHandThumb2")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/keys = PackedFloat32Array(0, 1, 0.0508004, 0.139394, 0.292409, 0.944715, 0.0333333, 1, 0.0508004, 0.139394, 0.292409, 0.944715, 0.0666667, 1, 0.0538809, 0.129177, 0.284097, 0.948525, 0.1, 1, 0.0555733, 0.12392, 0.279775, 0.950411, 0.133333, 1, 0.0578986, 0.117036, 0.274072, 0.952804, 0.166667, 1, 0.0620212, 0.105627, 0.264514, 0.956571, 0.266667, 1, 0.0754479, 0.0733363, 0.236849, 0.965832, 0.3, 1, 0.0776619, 0.0685285, 0.232663, 0.967027, 0.333333, 1, 0.0769953, 0.0699637, 0.233914, 0.966675, 0.366667, 1, 0.0722606, 0.0804768, 0.243036, 0.963969, 0.4, 1, 0.0600104, 0.111216, 0.269198, 0.954758, 0.433333, 1, 0.0486117, 0.147192, 0.298707, 0.941672, 0.466667, 1, 0.0420764, 0.174977, 0.320655, 0.929942, 0.5, 1, 0.038331, 0.196453, 0.337151, 0.919927, 0.533333, 1, 0.037664, 0.199975, 0.340069, 0.91812, 0.566667, 1, 0.0389841, 0.189937, 0.332747, 0.922867, 0.6, 1, 0.0413818, 0.176336, 0.32235, 0.929131, 0.633333, 1, 0.0455799, 0.157934, 0.307684, 0.937182, 0.666667, 1, 0.0508004, 0.139394, 0.292409, 0.944715, 0.7, 1, 0.0538809, 0.129177, 0.284097, 0.948525, 0.733333, 1, 0.0555732, 0.12392, 0.279775, 0.950411, 0.766667, 1, 0.0578986, 0.117036, 0.274071, 0.952804, 0.8, 1, 0.0620213, 0.105627, 0.264514, 0.956571, 0.9, 1, 0.075448, 0.0733364, 0.236849, 0.965832, 0.933333, 1, 0.0776621, 0.0685285, 0.232663, 0.967027, 0.966667, 1, 0.0769954, 0.0699637, 0.233914, 0.966675, 1, 1, 0.0722607, 0.0804768, 0.243036, 0.963969, 1.03333, 1, 0.0600106, 0.111215, 0.269198, 0.954758, 1.06667, 1, 0.0486118, 0.147192, 0.298708, 0.941672, 1.1, 1, 0.0420764, 0.174976, 0.320655, 0.929942, 1.13333, 1, 0.0383311, 0.196453, 0.337151, 0.919927, 1.16667, 1, 0.037664, 0.199975, 0.340069, 0.91812, 1.2, 1, 0.0389841, 0.189938, 0.332747, 0.922867, 1.23333, 1, 0.0413818, 0.176336, 0.32235, 0.929131, 1.26667, 1, 0.0455799, 0.157934, 0.307684, 0.937182, 1.3, 1, 0.0508004, 0.139394, 0.292409, 0.944715)
tracks/38/type = "rotation_3d"
tracks/38/imported = true
tracks/38/enabled = true
tracks/38/path = NodePath("Skeleton_01/Skeleton3D:RightUpLeg")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/keys = PackedFloat32Array(0, 1, 0.683449, -0.140101, -0.679732, 0.226348, 0.0333333, 1, 0.683449, -0.140101, -0.679732, 0.226348, 0.0666667, 1, 0.685434, -0.0712906, -0.711471, 0.137502, 0.1, 1, 0.675622, -0.00417393, -0.736199, 0.0390862, 0.133333, 1, -0.658646, -0.0609913, 0.747373, 0.0624369, 0.166667, 1, -0.643356, -0.0806405, 0.752327, 0.116594, 0.2, 1, -0.629462, -0.125394, 0.743319, 0.188496, 0.233333, 1, -0.631164, -0.11826, 0.744377, 0.183166, 0.266667, 1, -0.644759, -0.0262147, 0.760112, 0.0763387, 0.3, 1, 0.641424, -0.081065, -0.762184, 0.0328683, 0.333333, 1, 0.629991, -0.180199, -0.741197, 0.145832, 0.366667, 1, 0.609277, -0.260949, -0.707135, 0.246267, 0.4, 1, 0.592006, -0.306755, -0.676291, 0.313149, 0.433333, 1, 0.568173, -0.344265, -0.659549, 0.351649, 0.466667, 1, 0.552441, -0.363232, -0.655352, 0.36522, 0.5, 1, 0.567052, -0.346722, -0.648751, 0.370619, 0.533333, 1, 0.5929, -0.332253, -0.625749, 0.382774, 0.566667, 1, 0.620263, -0.306735, -0.624476, 0.362239, 0.6, 1, 0.648945, -0.262874, -0.628264, 0.339194, 0.633333, 1, 0.672651, -0.204112, -0.647856, 0.293535, 0.666667, 1, 0.683449, -0.140101, -0.679731, 0.226349, 0.7, 1, 0.685389, -0.0714362, -0.711492, 0.137542, 0.733333, 1, 0.675536, -0.00431088, -0.736282, 0.0390055, 0.766667, 1, -0.658603, -0.0609228, 0.747413, 0.062481, 0.8, 1, -0.643357, -0.0806414, 0.752326, 0.116595, 0.833333, 1, -0.631877, -0.122899, 0.743772, 0.180083, 0.866667, 1, -0.631154, -0.114148, 0.746185, 0.178389, 0.9, 1, -0.642626, -0.0208807, 0.762308, 0.0740439, 0.933333, 1, 0.639286, -0.0839984, -0.763639, 0.0333697, 0.966667, 1, 0.631085, -0.177985, -0.741069, 0.144465, 1, 1, 0.610588, -0.258695, -0.707159, 0.245325, 1.03333, 1, 0.592003, -0.306758, -0.676301, 0.313127, 1.06667, 1, 0.568128, -0.344391, -0.659589, 0.351523, 1.1, 1, 0.552428, -0.363283, -0.655348, 0.365196, 1.13333, 1, 0.568313, -0.34899, -0.650158, 0.364034, 1.16667, 1, 0.594062, -0.336643, -0.628717, 0.372124, 1.2, 1, 0.620037, -0.307205, -0.624111, 0.362856, 1.23333, 1, 0.648852, -0.263159, -0.627925, 0.339778, 1.26667, 1, 0.672651, -0.204208, -0.647703, 0.293803, 1.3, 1, 0.68345, -0.140101, -0.679732, 0.226348)
tracks/39/type = "rotation_3d"
tracks/39/imported = true
tracks/39/enabled = true
tracks/39/path = NodePath("Skeleton_01/Skeleton3D:RightLeg")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/keys = PackedFloat32Array(0, 1, -0.0233754, 0.0103554, -0.712152, 0.701559, 0.0333333, 1, -0.0233754, 0.0103554, -0.712152, 0.701559, 0.0666667, 1, -0.0228228, 0.00900261, -0.68008, 0.732727, 0.1, 1, -0.0217534, 0.00624618, -0.616115, 0.787331, 0.133333, 1, -0.0184005, 0.00388221, -0.519933, 0.854, 0.166667, 1, -0.0145969, 0.00911669, -0.517592, 0.855454, 0.2, 1, -0.0111761, 0.0116281, -0.462473, 0.886487, 0.233333, 1, -0.0217973, 0.00127204, -0.545812, 0.837623, 0.266667, 1, -0.026684, 0.00839776, -0.740882, 0.671052, 0.3, 1, -0.0266433, 0.0125321, -0.835468, 0.54875, 0.333333, 1, -0.0279702, 0.0125578, -0.860889, 0.507868, 0.366667, 1, -0.0296586, 0.0110123, -0.841394, 0.539495, 0.4, 1, -0.0196867, 0.0163476, -0.774792, 0.631698, 0.433333, 1, -0.0130823, 0.0199941, -0.662736, 0.748472, 0.466667, 1, -0.00632394, 0.0254066, -0.541146, 0.840521, 0.5, 1, -0.000129092, 0.0344115, -0.432128, 0.901155, 0.533333, 1, -0.00269718, 0.0313163, -0.521439, 0.852709, 0.566667, 1, -0.0152317, 0.0165226, -0.629126, 0.776978, 0.6, 1, -0.0182542, 0.0150666, -0.687026, 0.726247, 0.633333, 1, -0.0213968, 0.012616, -0.7097, 0.704066, 0.666667, 1, -0.0233754, 0.0103554, -0.712153, 0.701559, 0.7, 1, -0.0228977, 0.00892406, -0.680395, 0.732433, 0.733333, 1, -0.0218062, 0.00618393, -0.616334, 0.787158, 0.766667, 1, -0.0183949, 0.00389025, -0.519911, 0.854014, 0.8, 1, -0.0145965, 0.00911767, -0.517591, 0.855455, 0.833333, 1, -0.00833176, 0.0172568, -0.478975, 0.87762, 0.866667, 1, -0.0223324, 0.000861023, -0.551584, 0.83382, 0.9, 1, -0.0285918, 0.00674469, -0.743139, 0.668491, 0.933333, 1, -0.0285046, 0.0113057, -0.835779, 0.54821, 0.966667, 1, -0.0268498, 0.0132353, -0.860734, 0.508174, 1, 1, -0.0276677, 0.012304, -0.841382, 0.539592, 1.03333, 1, -0.0196578, 0.0163866, -0.774795, 0.631695, 1.06667, 1, -0.0132239, 0.0198573, -0.662737, 0.748472, 1.1, 1, -0.00630487, 0.0254624, -0.541166, 0.840506, 1.13333, 1, -0.00316427, 0.0282186, -0.424442, 0.90501, 1.16667, 1, -0.0121629, 0.0157291, -0.511198, 0.859233, 1.2, 1, -0.0150782, 0.0167165, -0.629262, 0.776867, 1.23333, 1, -0.0181084, 0.0152209, -0.687192, 0.72609, 1.26667, 1, -0.0213481, 0.012664, -0.709824, 0.703942, 1.3, 1, -0.0233754, 0.0103553, -0.712153, 0.701559)
tracks/40/type = "rotation_3d"
tracks/40/imported = true
tracks/40/enabled = true
tracks/40/path = NodePath("Skeleton_01/Skeleton3D:RightFoot")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/keys = PackedFloat32Array(0, 1, 0.626428, -0.398994, 0.616222, 0.262035, 0.0333333, 1, 0.626428, -0.398994, 0.616222, 0.262035, 0.0666667, 1, 0.647248, -0.359676, 0.632367, 0.22763, 0.1, 1, 0.642818, -0.354002, 0.644326, 0.215202, 0.133333, 1, 0.618708, -0.372562, 0.64974, 0.237141, 0.166667, 1, 0.576809, -0.424092, 0.615567, 0.329416, 0.2, 1, 0.517629, -0.461839, 0.59073, 0.412072, 0.233333, 1, 0.421246, -0.536003, 0.518899, 0.515749, 0.266667, 1, 0.358369, -0.566074, 0.497741, 0.550804, 0.3, 1, 0.420986, -0.537344, 0.544252, 0.48767, 0.333333, 1, 0.49479, -0.549506, 0.519317, 0.428411, 0.366667, 1, 0.524501, -0.54854, 0.49657, 0.421214, 0.4, 1, 0.520831, -0.474548, 0.543279, 0.456495, 0.433333, 1, 0.544134, -0.398106, 0.57253, 0.466519, 0.466667, 1, 0.530276, -0.44511, 0.557114, 0.458594, 0.5, 1, 0.451073, -0.533854, 0.50591, 0.505558, 0.533333, 1, 0.459347, -0.557308, 0.488493, 0.489677, 0.566667, 1, 0.500127, -0.531177, 0.513654, 0.451535, 0.6, 1, 0.550118, -0.494826, 0.560145, 0.372499, 0.633333, 1, 0.59799, -0.445107, 0.596538, 0.297372, 0.666667, 1, 0.626428, -0.398994, 0.616222, 0.262035, 0.7, 1, 0.647274, -0.35938, 0.632549, 0.227519, 0.733333, 1, 0.642807, -0.353717, 0.644511, 0.215147, 0.766667, 1, 0.618658, -0.37254, 0.649775, 0.237212, 0.8, 1, 0.576809, -0.424092, 0.615567, 0.329417, 0.833333, 1, 0.557626, -0.442006, 0.570632, 0.409956, 0.866667, 1, 0.519875, -0.518635, 0.431884, 0.523664, 0.9, 1, 0.438853, -0.531062, 0.430658, 0.583022, 0.933333, 1, 0.46168, -0.52159, 0.508036, 0.506651, 0.966667, 1, 0.492789, -0.535519, 0.52198, 0.444877, 1, 1, 0.520266, -0.535692, 0.50418, 0.433774, 1.03333, 1, 0.520818, -0.474518, 0.543314, 0.456499, 1.06667, 1, 0.54517, -0.39887, 0.571507, 0.46591, 1.1, 1, 0.530273, -0.445085, 0.55714, 0.45859, 1.13333, 1, 0.442759, -0.537851, 0.495575, 0.518734, 1.16667, 1, 0.449923, -0.556533, 0.468853, 0.517704, 1.2, 1, 0.499726, -0.531428, 0.513412, 0.451959, 1.23333, 1, 0.549911, -0.495048, 0.559978, 0.37276, 1.26667, 1, 0.597954, -0.445167, 0.596501, 0.297427, 1.3, 1, 0.626428, -0.398994, 0.616222, 0.262035)
tracks/41/type = "rotation_3d"
tracks/41/imported = true
tracks/41/enabled = true
tracks/41/path = NodePath("Skeleton_01/Skeleton3D:RightToeBase")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/keys = PackedFloat32Array(0, 1, 0.000990037, 0.996454, 0.0841284, 0.00137463, 0.0333333, 1, 0.000990037, 0.996454, 0.0841284, 0.00137463, 0.0666667, 1, 0.00107195, 0.994945, 0.100393, 0.00216546, 0.1, 1, 0.00119771, 0.99211, 0.125318, 0.00337925, 0.133333, 1, 0.00142085, 0.985391, 0.170213, 0.00556427, 0.166667, 1, 0.00222397, 0.937689, 0.347175, 0.0142671, 0.2, 1, -0.00506809, 0.895672, 0.444684, 0.00148347, 0.233333, 1, -0.00336421, 0.7575, 0.652433, 0.0226735, 0.266667, 1, 0.0565148, -0.989377, 0.107076, 0.0804652, 0.3, 1, -0.00901901, 0.856498, 0.514416, 0.0412928, 0.333333, 1, -0.00801186, 0.859942, 0.507584, 0.0528502, 0.366667, 1, -0.00131855, -0.912792, -0.408224, 0.0127254, 0.4, 1, 0.00194132, 0.984968, 0.172573, 0.0072978, 0.433333, 1, 0.00122715, 0.991147, 0.132714, 0.0035686, 0.466667, 1, 0.00129112, 0.98934, 0.145559, 0.00417605, 0.5, 1, 0.00134798, 0.987595, 0.156944, 0.00471478, 0.533333, 1, 0.00126995, 0.989951, 0.141348, 0.00397686, 0.566667, 1, 0.00108356, 0.994572, 0.104019, 0.00221322, 0.6, 1, 0.000886786, 0.997909, 0.064634, 0.000363566, 0.633333, 1, -0.000813352, -0.998759, -0.049799, 0.000305865, 0.666667, 1, 0.00099004, 0.996454, 0.0841283, 0.00137463, 0.7, 1, 0.00107195, 0.994945, 0.100392, 0.00216546, 0.733333, 1, 0.00119758, 0.99211, 0.125318, 0.00337923, 0.766667, 1, 0.00142085, 0.985391, 0.170213, 0.00556427, 0.8, 1, 0.00222405, 0.937689, 0.347175, 0.0142668, 0.833333, 1, 0.0216851, -0.907946, -0.417214, 0.0330966, 0.866667, 1, -0.0051426, -0.764691, -0.630321, 0.133855, 0.9, 1, 0.188374, 0.937277, -0.139121, 0.258211, 0.933333, 1, -0.00312942, -0.850052, -0.526514, 0.0135917, 0.966667, 1, -0.00587083, 0.857965, 0.51222, 0.0386233, 1, 1, -0.00101461, -0.912906, -0.408091, 0.007999, 1.03333, 1, 0.00194139, 0.984967, 0.172575, 0.00729807, 1.06667, 1, 0.00122715, 0.991147, 0.132714, 0.0035686, 1.1, 1, 0.00129112, 0.98934, 0.145558, 0.00417605, 1.13333, 1, 0.00134798, 0.987595, 0.156944, 0.00471478, 1.16667, 1, 0.00127036, 0.989951, 0.141347, 0.0039768, 1.2, 1, 0.00108356, 0.994572, 0.104019, 0.00221322, 1.23333, 1, 0.000886786, 0.997909, 0.0646342, 0.000363567, 1.26667, 1, -0.000813485, -0.998759, -0.0497989, 0.000305859, 1.3, 1, 0.00099004, 0.996454, 0.0841284, 0.00137463)

[sub_resource type="AnimationLibrary" id="AnimationLibrary_0c5ky"]
_data = {
&"A-Pose": ExtResource("10_s5rxm"),
&"Attack_Kick": ExtResource("11_8udw1"),
&"Attack_Punch": ExtResource("12_hl27u"),
&"Block_With_Hands": ExtResource("13_frjm0"),
&"Climb_Ladder": ExtResource("14_yvxww"),
&"Crouch_Walk _Forward": ExtResource("15_24ehl"),
&"Crouch_Walk_Backward": ExtResource("16_kayyy"),
&"Crouch_Walk_Left": ExtResource("17_wl8gc"),
&"Crouch_Walk_Right": ExtResource("18_ny0gp"),
&"Death_Backward": ExtResource("19_2mj4c"),
&"Death_Forward": ExtResource("20_o1aa7"),
&"Dodge_Roll": ExtResource("21_0nk2r"),
&"Dodge_Sidestep": ExtResource("22_imtsj"),
&"Fall": ExtResource("23_gpoug"),
&"Hit_Reaction_Heavy": ExtResource("24_n2uid"),
&"Hit_Reaction_Light": ExtResource("25_w2sfv"),
&"Idle_Look_Around": ExtResource("26_jlwsj"),
&"Idle_Relaxed": ExtResource("27_vj7jt"),
&"Jump": ExtResource("28_c34mi"),
&"Jump_2": SubResource("Animation_wy4nw"),
&"Jump_Bomb": SubResource("Animation_wlh02"),
&"Jump_End": ExtResource("29_xdknx"),
&"Jump_Start": ExtResource("30_iftjx"),
&"Lose": ExtResource("38_oytuk"),
&"Lose2": ExtResource("39_ri00f"),
&"RESET": SubResource("Animation_07qfg"),
&"Run_Backward": ExtResource("31_36i3e"),
&"Run_Forward": ExtResource("32_golr0"),
&"Run_Left": ExtResource("33_qeqh6"),
&"Run_Right": ExtResource("34_6ompd"),
&"Victory": ExtResource("37_pxof6"),
&"Walk_Backward": ExtResource("35_vx3qb"),
&"Walk_Forward": ExtResource("36_sq613"),
&"Walk_Left": ExtResource("37_miudd"),
&"Walk_Right": ExtResource("38_4jv0p"),
&"bomb_hold": ExtResource("39_2gn6e"),
&"bomb_hold_run": SubResource("Animation_ai2pb")
}

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_s5rxm"]
animation = &"Lose2"

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_pxof6"]
animation = &"Victory"

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_cpq4e"]
animation = &"Death_Forward"

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_scw33"]
animation = &"Idle_Relaxed"

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_nrqdy"]
animation = &"bomb_hold"

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_q1t7d"]
animation = &"Jump_2"

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_blnwj"]
animation = &"Jump_Bomb"

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_teac0"]
animation = &"Run_Forward"

[sub_resource type="AnimationNodeTimeScale" id="AnimationNodeTimeScale_d5oha"]

[sub_resource type="AnimationNodeBlendTree" id="AnimationNodeBlendTree_dlx7i"]
nodes/Animation/node = SubResource("AnimationNodeAnimation_teac0")
nodes/Animation/position = Vector2(160, 120)
nodes/TimeScale/node = SubResource("AnimationNodeTimeScale_d5oha")
nodes/TimeScale/position = Vector2(360, 120)
nodes/output/position = Vector2(560, 140)
node_connections = [&"TimeScale", 0, &"Animation", &"output", 0, &"TimeScale"]

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_m717m"]
animation = &"bomb_hold_run"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_4tn7a"]
advance_mode = 2

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_4q8le"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_jumping"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_nrqdy"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_idle"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_blnwj"]
xfade_time = 0.2
advance_mode = 2
advance_condition = &"is_dead"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_cpmkp"]
xfade_time = 0.2
advance_mode = 2
advance_condition = &"is_idle"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_pqdy0"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_bomb_hold"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_twoj4"]
advance_mode = 2
advance_condition = &"is_idle"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_2thx0"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_moving_bomb"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_a6x0e"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_bomb_hold"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_r6d6b"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_jumping"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_4ovym"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_bomb_hold"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_m78b3"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_moving"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_ph06f"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_jumping"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_1wf68"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_moving"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_cfiwb"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_idle"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_au1er"]
advance_mode = 2
advance_condition = &"is_dead"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_jcjja"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_jumping"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_8bbmh"]
xfade_time = 0.1
advance_mode = 2
advance_condition = &"is_moving"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_oytuk"]
xfade_time = 0.2
advance_mode = 2
advance_condition = &"is_win"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_ri00f"]
xfade_time = 0.2
advance_mode = 2
advance_condition = &"is_lose"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_s5rxm"]
xfade_time = 0.2
advance_mode = 2
advance_condition = &"is_idle"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_8udw1"]
xfade_time = 0.2
advance_mode = 2
advance_condition = &"is_idle"

[sub_resource type="AnimationNodeStateMachine" id="AnimationNodeStateMachine_4tn7a"]
states/Lose/node = SubResource("AnimationNodeAnimation_s5rxm")
states/Lose/position = Vector2(711, 237)
states/Victory/node = SubResource("AnimationNodeAnimation_pxof6")
states/Victory/position = Vector2(726, 170)
states/die/node = SubResource("AnimationNodeAnimation_cpq4e")
states/die/position = Vector2(478.377, -3.29416)
states/idle/node = SubResource("AnimationNodeAnimation_scw33")
states/idle/position = Vector2(382, 100)
states/idle_bomb/node = SubResource("AnimationNodeAnimation_nrqdy")
states/idle_bomb/position = Vector2(382, 344)
states/jump/node = SubResource("AnimationNodeAnimation_q1t7d")
states/jump/position = Vector2(479, 216)
states/jump_bomb/node = SubResource("AnimationNodeAnimation_blnwj")
states/jump_bomb/position = Vector2(478.377, 459)
states/move/node = SubResource("AnimationNodeBlendTree_dlx7i")
states/move/position = Vector2(579, 100)
states/move_bomb/node = SubResource("AnimationNodeAnimation_m717m")
states/move_bomb/position = Vector2(579, 344)
transitions = ["Start", "idle", SubResource("AnimationNodeStateMachineTransition_4tn7a"), "idle", "jump", SubResource("AnimationNodeStateMachineTransition_4q8le"), "jump", "idle", SubResource("AnimationNodeStateMachineTransition_nrqdy"), "idle", "die", SubResource("AnimationNodeStateMachineTransition_blnwj"), "die", "idle", SubResource("AnimationNodeStateMachineTransition_cpmkp"), "idle", "idle_bomb", SubResource("AnimationNodeStateMachineTransition_pqdy0"), "idle_bomb", "idle", SubResource("AnimationNodeStateMachineTransition_twoj4"), "idle_bomb", "move_bomb", SubResource("AnimationNodeStateMachineTransition_2thx0"), "move_bomb", "idle_bomb", SubResource("AnimationNodeStateMachineTransition_a6x0e"), "idle_bomb", "jump_bomb", SubResource("AnimationNodeStateMachineTransition_r6d6b"), "jump_bomb", "idle_bomb", SubResource("AnimationNodeStateMachineTransition_4ovym"), "jump_bomb", "move_bomb", SubResource("AnimationNodeStateMachineTransition_m78b3"), "move_bomb", "jump_bomb", SubResource("AnimationNodeStateMachineTransition_ph06f"), "idle", "move", SubResource("AnimationNodeStateMachineTransition_1wf68"), "move", "idle", SubResource("AnimationNodeStateMachineTransition_cfiwb"), "move", "die", SubResource("AnimationNodeStateMachineTransition_au1er"), "move", "jump", SubResource("AnimationNodeStateMachineTransition_jcjja"), "jump", "move", SubResource("AnimationNodeStateMachineTransition_8bbmh"), "idle", "Victory", SubResource("AnimationNodeStateMachineTransition_oytuk"), "idle", "Lose", SubResource("AnimationNodeStateMachineTransition_ri00f"), "Victory", "idle", SubResource("AnimationNodeStateMachineTransition_s5rxm"), "Lose", "idle", SubResource("AnimationNodeStateMachineTransition_8udw1")]
graph_offset = Vector2(36, 67)

[sub_resource type="AnimationNodeBlendTree" id="AnimationNodeBlendTree_07qfg"]
graph_offset = Vector2(-227.808, 150.946)
nodes/StateMachine/node = SubResource("AnimationNodeStateMachine_4tn7a")
nodes/StateMachine/position = Vector2(40, 140)
nodes/output/position = Vector2(600, 160)
node_connections = [&"output", 0, &"StateMachine"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_07qfg"]
radius = 0.3
height = 1.16477

[sub_resource type="ShaderMaterial" id="ShaderMaterial_73v70"]
render_priority = 0
shader = ExtResource("38_pxof6")
shader_parameter/speed = Vector2(2, 3)
shader_parameter/aura_texture = ExtResource("39_0nmdm")

[sub_resource type="Curve" id="Curve_nkjm3"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.469767, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_pxof6"]
texture_mode = 1
curve = SubResource("Curve_nkjm3")

[sub_resource type="Curve" id="Curve_0nmdm"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Curve" id="Curve_oytuk"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Curve" id="Curve_ri00f"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveXYZTexture" id="CurveXYZTexture_s5rxm"]
curve_x = SubResource("Curve_0nmdm")
curve_y = SubResource("Curve_oytuk")
curve_z = SubResource("Curve_ri00f")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_8udw1"]
gravity = Vector3(0, 0, 0)
scale_min = 0.5
scale_max = 0.5
scale_curve = SubResource("CurveXYZTexture_s5rxm")
color = Color(0.450077, 1.2, 0.151592, 1)
alpha_curve = SubResource("CurveTexture_pxof6")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_hrx22"]
render_priority = 0
shader = ExtResource("38_pxof6")
shader_parameter/speed = Vector2(1, 3)
shader_parameter/aura_texture = ExtResource("41_ri00f")

[sub_resource type="Curve" id="Curve_7ykd1"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.469767, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_v4e51"]
texture_mode = 1
curve = SubResource("Curve_7ykd1")

[sub_resource type="Curve" id="Curve_8cdnn"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Curve" id="Curve_ofi5q"]
_data = [Vector2(0, 0.506079), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Curve" id="Curve_sjnhf"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveXYZTexture" id="CurveXYZTexture_djxxo"]
curve_x = SubResource("Curve_8cdnn")
curve_y = SubResource("Curve_ofi5q")
curve_z = SubResource("Curve_sjnhf")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_27u1n"]
gravity = Vector3(0, 0, 0)
scale_min = 1.2
scale_max = 1.2
scale_curve = SubResource("CurveXYZTexture_djxxo")
color = Color(2.5, 1.575, 0.284, 1)
alpha_curve = SubResource("CurveTexture_v4e51")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_8cdnn"]
render_priority = 0
shader = ExtResource("42_s5rxm")
shader_parameter/Main_Texture = ExtResource("43_8udw1")
shader_parameter/Proximity_Fade = 0.5

[sub_resource type="Curve" id="Curve_pxof6"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.21374, 1), 0.0, 0.0, 0, 0, Vector2(0.78626, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 4

[sub_resource type="CurveTexture" id="CurveTexture_0nmdm"]
texture_mode = 1
curve = SubResource("Curve_pxof6")

[sub_resource type="Curve" id="Curve_s5rxm"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_8udw1"]
curve = SubResource("Curve_s5rxm")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_hl27u"]
emission_shape = 6
emission_ring_axis = Vector3(0, 1, 0)
emission_ring_height = 1.0
emission_ring_radius = 0.1
emission_ring_inner_radius = 1.0
emission_ring_cone_angle = 0.0
direction = Vector3(0, 1, 0)
spread = 50.0
initial_velocity_min = 6.0
initial_velocity_max = 9.0
gravity = Vector3(0, 0, 0)
damping_min = 3.0
damping_max = 5.0
scale_min = 0.5
scale_max = 0.5
scale_curve = SubResource("CurveTexture_8udw1")
color = Color(0.450077, 1.2, 0.151592, 1)
alpha_curve = SubResource("CurveTexture_0nmdm")
turbulence_noise_strength = 0.1
turbulence_noise_speed_random = 0.1
turbulence_influence_min = 0.05

[sub_resource type="QuadMesh" id="QuadMesh_sh0yt"]

[sub_resource type="Animation" id="Animation_p5psq"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("AuraCylinder:emitting")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("AuraCylinder2:emitting")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("LittleParticles:emitting")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_5rwps"]
resource_name = "default"
length = 2.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("AuraCylinder:emitting")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("AuraCylinder2:emitting")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("LittleParticles:emitting")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_nm8rl"]
_data = {
&"RESET": SubResource("Animation_p5psq"),
&"default": SubResource("Animation_5rwps")
}

[node name="Model" type="CharacterBody3D"]
collision_layer = 2
script = ExtResource("1_g5bav")

[node name="Skeleton_01" type="Node3D" parent="."]
unique_name_in_owner = true
script = ExtResource("2_ch4jb")

[node name="Skeleton3D" type="Skeleton3D" parent="Skeleton_01"]
unique_name_in_owner = true
bones/0/name = "Root"
bones/0/parent = -1
bones/0/rest = Transform3D(1, -8.74228e-08, -8.74227e-08, 8.74228e-08, 1, -2.0067e-07, 8.74227e-08, 2.0067e-07, 1, 0, 0, 0)
bones/0/enabled = true
bones/0/position = Vector3(0, 0, 0)
bones/0/rotation = Quaternion(1.00335e-07, -4.37113e-08, 4.37114e-08, 1)
bones/0/scale = Vector3(1, 1, 1)
bones/1/name = "Hips"
bones/1/parent = 0
bones/1/rest = Transform3D(1, 1.42109e-14, 2.00413e-06, -3.97224e-07, 0.980161, 0.198203, -1.96437e-06, -0.198203, 0.980161, 1.92424e-13, 0.313905, -0.000313224)
bones/1/enabled = true
bones/1/position = Vector3(0.00275985, 0.295744, -0.00182062)
bones/1/rotation = Quaternion(-0.104626, 0.0374209, -0.0132528, 0.993719)
bones/1/scale = Vector3(1, 1, 1)
bones/2/name = "LeftUpLeg"
bones/2/parent = 1
bones/2/rest = Transform3D(-0.00782502, 4.28408e-08, 0.99997, -0.0315513, -0.999502, -0.000246855, 0.999472, -0.0315523, 0.00782113, 0.0695258, 0.0255534, -0.000220532)
bones/2/enabled = true
bones/2/position = Vector3(0.0695258, 0.0255541, -0.00022064)
bones/2/rotation = Quaternion(0.726652, 0.0828937, 0.681914, 0.00995283)
bones/2/scale = Vector3(1, 1, 1)
bones/3/name = "LeftLeg"
bones/3/parent = 2
bones/3/rest = Transform3D(0.903131, -0.428907, -0.0198431, 0.429072, 0.903257, 0.00478992, 0.015869, -0.01284, 0.999792, -5.96629e-08, 0.149529, 3.17086e-08)
bones/3/enabled = true
bones/3/position = Vector3(-6.16591e-10, 0.14953, 2.78805e-08)
bones/3/rotation = Quaternion(-0.0103712, -0.00995365, 0.40902, 0.912412)
bones/3/scale = Vector3(1, 1, 1)
bones/4/name = "LeftFoot"
bones/4/parent = 3
bones/4/rest = Transform3D(-0.00880372, 0.993541, -0.113137, 0.00947878, -0.113053, -0.993544, -0.999916, -0.00981928, -0.00842226, 1.97906e-09, 0.164674, -1.40572e-08)
bones/4/enabled = true
bones/4/position = Vector3(1.97906e-09, 0.164674, -1.40572e-08)
bones/4/rotation = Quaternion(-0.562965, -0.378588, 0.580451, -0.450354)
bones/4/scale = Vector3(1, 1, 1)
bones/5/name = "LeftToeBase"
bones/5/parent = 4
bones/5/rest = Transform3D(-1, 3.4799e-07, -7.77837e-06, 2.57521e-07, 0.999933, 0.0116277, 7.78189e-06, 0.0116277, -0.999932, 1.45044e-08, 0.0799548, -5.13991e-08)
bones/5/enabled = true
bones/5/position = Vector3(-3.96374e-10, 0.0799549, 1.052e-09)
bones/5/rotation = Quaternion(-0.0291193, 0.999281, 0.00866951, 0.0226902)
bones/5/scale = Vector3(1, 1, 1)
bones/6/name = "Spine"
bones/6/parent = 1
bones/6/rest = Transform3D(1, -3.53156e-07, -1.73435e-06, 4.20129e-08, 0.984352, -0.176214, 1.76944e-06, 0.176214, 0.984352, -3.75991e-08, 0.0911961, 0.0187607)
bones/6/enabled = true
bones/6/position = Vector3(-3.88284e-08, 0.0911963, 0.0187605)
bones/6/rotation = Quaternion(0.0977553, -0.000510158, -0.0119205, 0.995139)
bones/6/scale = Vector3(1, 1, 1)
bones/7/name = "Spine1"
bones/7/parent = 6
bones/7/rest = Transform3D(1, 1.34492e-08, -2.3804e-07, -4.26326e-14, 0.998408, 0.0564097, 2.38419e-07, -0.0564097, 0.998408, -4.44089e-16, 0.0859079, 1.47847e-08)
bones/7/enabled = true
bones/7/position = Vector3(-2.2701e-09, 0.0859085, -8.79298e-10)
bones/7/rotation = Quaternion(-0.0951247, 0.001376, -0.00174386, 0.995463)
bones/7/scale = Vector3(1, 1, 1)
bones/8/name = "Neck"
bones/8/parent = 7
bones/8/rest = Transform3D(1, 1.20792e-13, -4.76838e-07, -1.7059e-07, 0.933816, -0.357752, 4.45279e-07, 0.357752, 0.933816, -8.66529e-14, 0.0740407, 0.000782351)
bones/8/enabled = true
bones/8/position = Vector3(-3.20994e-13, 0.0740402, 0.00078268)
bones/8/rotation = Quaternion(0.152886, 0.00338565, 0.00172663, 0.988237)
bones/8/scale = Vector3(1, 0.999999, 1)
bones/9/name = "Head"
bones/9/parent = 8
bones/9/rest = Transform3D(1, -1.33988e-07, 2.19207e-07, 6.6994e-08, 0.95971, 0.280992, -2.48025e-07, -0.280992, 0.95971, 3.12639e-13, 0.0580662, 4.25614e-07)
bones/9/enabled = true
bones/9/position = Vector3(1.57181e-09, 0.0580664, 6.81652e-09)
bones/9/rotation = Quaternion(-0.201461, -0.0597973, -0.0648259, 0.975518)
bones/9/scale = Vector3(1, 1, 0.999999)
bones/10/name = "LeftShoulder"
bones/10/parent = 7
bones/10/rest = Transform3D(0.154985, 0.976141, 0.152078, -0.0939982, -0.138669, 0.985868, 0.983435, -0.16709, 0.0702638, 0.0109744, 0.0536174, 0.010723)
bones/10/enabled = true
bones/10/position = Vector3(0.0109743, 0.0536167, 0.0107229)
bones/10/rotation = Quaternion(-0.553035, -0.398773, -0.51331, 0.521195)
bones/10/scale = Vector3(1, 1, 1)
bones/11/name = "LeftArm"
bones/11/parent = 10
bones/11/rest = Transform3D(0.999173, -0.0126007, 0.0386464, 0.000179859, 0.952101, 0.305783, -0.0406484, -0.305523, 0.951317, -3.74857e-08, 0.0806824, -4.00807e-07)
bones/11/enabled = true
bones/11/position = Vector3(1.31304e-09, 0.0806818, -1.42711e-07)
bones/11/rotation = Quaternion(-0.377758, 0.168191, -0.0931779, 0.90572)
bones/11/scale = Vector3(1, 1, 1)
bones/12/name = "LeftForeArm"
bones/12/parent = 11
bones/12/rest = Transform3D(-0.04777, 0.307489, 0.950352, -0.161448, 0.93655, -0.311139, -0.985724, -0.168296, 0.0049045, 5.7742e-08, 0.140995, 2.05066e-07)
bones/12/enabled = true
bones/12/position = Vector3(-1.70863e-08, 0.140995, 1.22905e-07)
bones/12/rotation = Quaternion(0.134525, 0.698207, -0.246115, 0.658663)
bones/12/scale = Vector3(1, 1, 1)
bones/13/name = "LeftHand"
bones/13/parent = 12
bones/13/rest = Transform3D(0.999985, -0.00206376, -0.00503197, 0.00239686, 0.997742, 0.0671157, 0.0048821, -0.0671268, 0.997732, -3.27709e-08, 0.115858, 1.86265e-09)
bones/13/enabled = true
bones/13/position = Vector3(-3.27709e-08, 0.115858, 1.86265e-09)
bones/13/rotation = Quaternion(0.109579, 0.00814771, 0.124578, 0.986107)
bones/13/scale = Vector3(1, 1, 1)
bones/14/name = "LeftHandIndex1"
bones/14/parent = 13
bones/14/rest = Transform3D(-0.00117087, 0.252277, 0.967654, -0.0990004, 0.962872, -0.25115, -0.995087, -0.0960922, 0.0238481, -0.0103993, 0.0882659, 0.0290431)
bones/14/enabled = true
bones/14/position = Vector3(-0.0103997, 0.0882661, 0.0290431)
bones/14/rotation = Quaternion(0.0550174, 0.696455, -0.124644, 0.704548)
bones/14/scale = Vector3(1, 1, 1)
bones/15/name = "LeftHandIndex2"
bones/15/parent = 14
bones/15/rest = Transform3D(0.998271, -0.0565877, 0.0159309, 0.0586141, 0.978879, -0.19586, -0.00451116, 0.196455, 0.980502, 2.37487e-08, 0.0239113, -1.44375e-07)
bones/15/enabled = true
bones/15/position = Vector3(-3.93601e-09, 0.0239113, 1.90477e-08)
bones/15/rotation = Quaternion(0.0986069, 0.0051375, 0.0289545, 0.994692)
bones/15/scale = Vector3(1, 1, 1)
bones/16/name = "LeftHandMiddle1"
bones/16/parent = 13
bones/16/rest = Transform3D(0.00248802, 0.233364, 0.972386, -0.096867, 0.967873, -0.232033, -0.995294, -0.0936148, 0.0250134, -0.00954829, 0.0832559, 0.00452071)
bones/16/enabled = true
bones/16/position = Vector3(-0.00954878, 0.0832564, 0.00452069)
bones/16/rotation = Quaternion(0.0489999, 0.696485, -0.116895, 0.706288)
bones/16/scale = Vector3(1, 1, 1)
bones/17/name = "LeftHandMiddle2"
bones/17/parent = 16
bones/17/rest = Transform3D(0.999993, 0.00372776, -0.000839063, -0.00378411, 0.996609, -0.0821918, 0.000529827, 0.0821944, 0.996616, -2.90493e-09, 0.022215, -4.0627e-07)
bones/17/enabled = true
bones/17/position = Vector3(-2.99319e-10, 0.0222148, -4.29344e-08)
bones/17/rotation = Quaternion(0.0411268, -0.000342718, -0.00187913, 0.999152)
bones/17/scale = Vector3(1, 1, 1)
bones/18/name = "LeftHandPinky1"
bones/18/parent = 13
bones/18/rest = Transform3D(-0.0202649, 0.231054, 0.97273, -0.202747, 0.951765, -0.230298, -0.979022, -0.201885, 0.0275581, -0.00575942, 0.0736976, -0.037381)
bones/18/enabled = true
bones/18/position = Vector3(-0.00575963, 0.0736974, -0.0373809)
bones/18/rotation = Quaternion(0.0101495, 0.697222, -0.154966, 0.699832)
bones/18/scale = Vector3(1, 1, 1)
bones/19/name = "LeftHandPinky2"
bones/19/parent = 18
bones/19/rest = Transform3D(0.999265, -0.0365792, 0.0115088, 0.0372763, 0.99701, -0.0676886, -0.00899841, 0.0680678, 0.99764, -3.72529e-09, 0.0157822, 2.23517e-08)
bones/19/enabled = true
bones/19/position = Vector3(-3.72529e-09, 0.0157822, 2.23517e-08)
bones/19/rotation = Quaternion(0.0339655, 0.00513056, 0.0184783, 0.999239)
bones/19/scale = Vector3(1, 1, 1)
bones/20/name = "LeftHandProp"
bones/20/parent = 13
bones/20/rest = Transform3D(0.00316727, 0.0907187, 0.995871, -0.0710367, 0.993381, -0.0902659, -0.997469, -0.0704576, 0.00959067, 0.0238626, 0.0687287, -0.00391634)
bones/20/enabled = true
bones/20/position = Vector3(0.0238619, 0.0687287, -0.00391632)
bones/20/rotation = Quaternion(0.00699259, 0.703673, -0.0571019, 0.708191)
bones/20/scale = Vector3(1, 1, 1)
bones/21/name = "LeftHandRing1"
bones/21/parent = 13
bones/21/rest = Transform3D(-0.00952935, 0.369464, 0.929196, -0.139558, 0.919652, -0.367101, -0.990168, -0.133175, 0.0427979, -0.0105838, 0.0809476, -0.0176894)
bones/21/enabled = true
bones/21/position = Vector3(-0.0105837, 0.080948, -0.0176894)
bones/21/rotation = Quaternion(0.0836958, 0.686729, -0.182122, 0.698735)
bones/21/scale = Vector3(1, 1, 1)
bones/22/name = "LeftHandRing2"
bones/22/parent = 21
bones/22/rest = Transform3D(0.999937, -0.0109594, -0.00248465, 0.0110625, 0.998869, 0.0462359, 0.00197513, -0.0462605, 0.998927, -2.42144e-08, 0.0204472, 6.27057e-07)
bones/22/enabled = true
bones/22/position = Vector3(7.79837e-10, 0.0204468, 3.60115e-08)
bones/22/rotation = Quaternion(-0.0231299, -0.0011154, 0.00550745, 0.999717)
bones/22/scale = Vector3(1, 1, 1)
bones/23/name = "LeftHandThumb1"
bones/23/parent = 13
bones/23/rest = Transform3D(-0.829163, 0.249863, 0.500056, 0.412954, 0.876712, 0.246669, -0.376772, 0.411029, -0.830119, 0.013069, 0.0355156, 0.0356359)
bones/23/enabled = true
bones/23/position = Vector3(0.0130688, 0.0355156, 0.0356366)
bones/23/rotation = Quaternion(0.176242, 0.940212, 0.17488, 0.233147)
bones/23/scale = Vector3(1, 1, 1)
bones/24/name = "LeftHandThumb2"
bones/24/parent = 23
bones/24/rest = Transform3D(0.979557, 0.162232, -0.118945, -0.182024, 0.966538, -0.180753, 0.0856414, 0.198709, 0.97631, -7.59959e-07, 0.0425107, -2.21655e-07)
bones/24/enabled = true
bones/24/position = Vector3(1.97076e-08, 0.0425103, 4.29177e-08)
bones/24/rotation = Quaternion(0.0957992, -0.0516503, -0.0869116, 0.990253)
bones/24/scale = Vector3(1, 1, 1)
bones/25/name = "RightShoulder"
bones/25/parent = 7
bones/25/rest = Transform3D(0.154986, -0.976141, -0.152078, 0.0939979, -0.138669, 0.985868, -0.983435, -0.16709, 0.0702636, -0.0109744, 0.0536174, 0.010723)
bones/25/enabled = true
bones/25/position = Vector3(-0.0109743, 0.0536167, 0.0107229)
bones/25/rotation = Quaternion(-0.553035, 0.398774, 0.51331, 0.521196)
bones/25/scale = Vector3(1, 1, 1)
bones/26/name = "RightArm"
bones/26/parent = 25
bones/26/rest = Transform3D(0.999173, 0.0126008, -0.0386469, -0.000179834, 0.952101, 0.305783, 0.0406488, -0.305524, 0.951316, 4.05125e-08, 0.0806824, -3.88183e-07)
bones/26/enabled = true
bones/26/position = Vector3(-8.89361e-10, 0.0806818, -1.23354e-07)
bones/26/rotation = Quaternion(-0.441262, -0.108914, -0.0721948, 0.887814)
bones/26/scale = Vector3(1, 1, 1)
bones/27/name = "RightForeArm"
bones/27/parent = 26
bones/27/rest = Transform3D(-0.0477698, -0.307489, -0.950352, 0.161448, 0.93655, -0.311139, 0.985724, -0.168296, 0.00490487, -5.40167e-08, 0.140995, 2.58035e-07)
bones/27/enabled = true
bones/27/position = Vector3(1.65323e-08, 0.140995, 1.28891e-07)
bones/27/rotation = Quaternion(0.145883, -0.697129, 0.256407, 0.65344)
bones/27/scale = Vector3(1, 1, 1)
bones/28/name = "RightHand"
bones/28/parent = 27
bones/28/rest = Transform3D(0.999985, 0.00206413, 0.00503201, -0.00239723, 0.997742, 0.0671157, -0.00488211, -0.0671268, 0.997732, 1.64582e-08, 0.115858, -9.31323e-10)
bones/28/enabled = true
bones/28/position = Vector3(1.64582e-08, 0.115858, -9.31323e-10)
bones/28/rotation = Quaternion(0.0130071, -0.0893174, -0.0134719, 0.995827)
bones/28/scale = Vector3(1, 1, 1)
bones/29/name = "RightHandIndex1"
bones/29/parent = 28
bones/29/rest = Transform3D(-0.00117075, -0.252277, -0.967654, 0.0990006, 0.962872, -0.25115, 0.995087, -0.0960923, 0.0238482, 0.0103993, 0.0882659, 0.0290431)
bones/29/enabled = true
bones/29/position = Vector3(0.0103996, 0.088266, 0.0290431)
bones/29/rotation = Quaternion(0.0550176, -0.696455, 0.124644, 0.704548)
bones/29/scale = Vector3(1, 1, 1)
bones/30/name = "RightHandIndex2"
bones/30/parent = 29
bones/30/rest = Transform3D(0.998271, 0.0565878, -0.0159314, -0.0586143, 0.978879, -0.19586, 0.00451154, 0.196455, 0.980502, -2.52621e-08, 0.0239113, -7.85385e-08)
bones/30/enabled = true
bones/30/position = Vector3(-1.69592e-08, 0.0239113, -3.78994e-09)
bones/30/rotation = Quaternion(0.0986067, -0.00513789, -0.0289542, 0.994692)
bones/30/scale = Vector3(1, 1, 1)
bones/31/name = "RightHandMiddle1"
bones/31/parent = 28
bones/31/rest = Transform3D(0.00248832, -0.233364, -0.972386, 0.0968671, 0.967873, -0.232033, 0.995294, -0.0936148, 0.0250137, 0.00954835, 0.0832559, 0.00452071)
bones/31/enabled = true
bones/31/position = Vector3(0.00954877, 0.0832563, 0.00452067)
bones/31/rotation = Quaternion(0.049, -0.696485, 0.116895, 0.706288)
bones/31/scale = Vector3(1, 1, 1)
bones/32/name = "RightHandMiddle2"
bones/32/parent = 31
bones/32/rest = Transform3D(0.999993, -0.00372779, 0.000838432, 0.00378409, 0.996609, -0.0821918, -0.000529196, 0.0821944, 0.996616, 3.08501e-09, 0.0222149, -3.76696e-07)
bones/32/enabled = true
bones/32/position = Vector3(1.8521e-09, 0.0222148, -3.20088e-08)
bones/32/rotation = Quaternion(0.0411266, 0.000342277, 0.0018795, 0.999152)
bones/32/scale = Vector3(1, 1, 1)
bones/33/name = "RightHandPinky1"
bones/33/parent = 28
bones/33/rest = Transform3D(-0.0202647, -0.231054, -0.97273, 0.202746, 0.951765, -0.230298, 0.979022, -0.201884, 0.0275581, 0.00575948, 0.0736975, -0.0373809)
bones/33/enabled = true
bones/33/position = Vector3(0.00575958, 0.0736974, -0.0373809)
bones/33/rotation = Quaternion(0.0101499, -0.697222, 0.154966, 0.699832)
bones/33/scale = Vector3(1, 1, 1)
bones/34/name = "RightHandPinky2"
bones/34/parent = 33
bones/34/rest = Transform3D(0.999264, 0.0365789, -0.011509, -0.037276, 0.99701, -0.0676887, 0.00899863, 0.0680679, 0.99764, -3.72529e-09, 0.0157822, -3.50992e-08)
bones/34/enabled = true
bones/34/position = Vector3(-3.72529e-09, 0.0157822, -3.50992e-08)
bones/34/rotation = Quaternion(0.0339653, -0.00513077, -0.0184779, 0.999239)
bones/34/scale = Vector3(1, 1, 1)
bones/35/name = "RightHandProp"
bones/35/parent = 28
bones/35/rest = Transform3D(0.00316727, -0.0907186, -0.995871, 0.0710368, 0.993381, -0.0902659, 0.997469, -0.0704576, 0.00959067, -0.0238625, 0.0687287, -0.00391634)
bones/35/enabled = true
bones/35/position = Vector3(-0.0238619, 0.0687286, -0.00391634)
bones/35/rotation = Quaternion(0.00699253, -0.703673, 0.0571016, 0.708191)
bones/35/scale = Vector3(1, 1, 1)
bones/36/name = "RightHandRing1"
bones/36/parent = 28
bones/36/rest = Transform3D(-0.00952935, -0.369464, -0.929196, 0.139558, 0.919653, -0.367101, 0.990168, -0.133175, 0.0427978, 0.0105838, 0.0809475, -0.0176894)
bones/36/enabled = true
bones/36/position = Vector3(0.0105836, 0.0809479, -0.0176894)
bones/36/rotation = Quaternion(0.0836959, -0.686729, 0.182122, 0.698735)
bones/36/scale = Vector3(1, 1, 1)
bones/37/name = "RightHandRing2"
bones/37/parent = 36
bones/37/rest = Transform3D(0.999937, 0.0109592, 0.0024846, -0.0110624, 0.998869, 0.0462358, -0.00197508, -0.0462604, 0.998927, 2.51457e-08, 0.0204472, 7.01009e-07)
bones/37/enabled = true
bones/37/position = Vector3(-1.60953e-09, 0.0204467, 6.38013e-08)
bones/37/rotation = Quaternion(-0.0231301, 0.00111526, -0.00550706, 0.999717)
bones/37/scale = Vector3(1, 1, 1)
bones/38/name = "RightHandThumb1"
bones/38/parent = 28
bones/38/rest = Transform3D(-0.829163, -0.249863, -0.500057, -0.412954, 0.876711, 0.246669, 0.376772, 0.411029, -0.830119, -0.013069, 0.0355155, 0.0356359)
bones/38/enabled = true
bones/38/position = Vector3(-0.0130688, 0.0355155, 0.0356365)
bones/38/rotation = Quaternion(-0.176242, 0.940212, 0.17488, -0.233147)
bones/38/scale = Vector3(1, 1, 1)
bones/39/name = "RightHandThumb2"
bones/39/parent = 38
bones/39/rest = Transform3D(0.979557, -0.162232, 0.118945, 0.182025, 0.966538, -0.180753, -0.0856413, 0.198709, 0.97631, 8.00937e-07, 0.0425107, -2.14204e-07)
bones/39/enabled = true
bones/39/position = Vector3(-1.4314e-08, 0.0425103, 4.04567e-08)
bones/39/rotation = Quaternion(0.095799, 0.0516501, 0.086912, 0.990253)
bones/39/scale = Vector3(1, 1, 1)
bones/40/name = "RightUpLeg"
bones/40/parent = 1
bones/40/rest = Transform3D(-0.00745988, 2.01138e-07, -0.999972, 0.0315514, -0.999502, -0.000235577, -0.999474, -0.0315523, 0.00745616, -0.0695258, 0.0255534, -0.000220805)
bones/40/enabled = true
bones/40/position = Vector3(-0.0695258, 0.0255542, -0.000220906)
bones/40/rotation = Quaternion(-0.755427, 0.0594782, 0.652218, 0.0200818)
bones/40/scale = Vector3(1, 1, 1)
bones/41/name = "RightLeg"
bones/41/parent = 40
bones/41/rest = Transform3D(0.903136, 0.428911, 0.019482, -0.429072, 0.903257, 0.00478973, -0.0155429, -0.012685, 0.999799, 7.03731e-08, 0.149529, 4.38013e-08)
bones/41/enabled = true
bones/41/position = Vector3(-2.90202e-09, 0.149529, 8.81325e-09)
bones/41/rotation = Quaternion(-0.00771247, 0.00873721, -0.361376, 0.932347)
bones/41/scale = Vector3(1, 1, 1)
bones/42/name = "RightFoot"
bones/42/parent = 41
bones/42/rest = Transform3D(-0.0088042, -0.99354, 0.113137, -0.0094763, -0.113053, -0.993544, 0.999916, -0.00981948, -0.00841975, -2.5495e-08, 0.164674, 3.05008e-08)
bones/42/enabled = true
bones/42/position = Vector3(2.49833e-09, 0.164674, -2.2473e-10)
bones/42/rotation = Quaternion(0.54385, -0.382202, 0.566015, 0.487622)
bones/42/scale = Vector3(1, 1, 1)
bones/43/name = "RightToeBase"
bones/43/parent = 42
bones/43/rest = Transform3D(-1, -9.49826e-08, 1.06409e-05, 2.87513e-08, 0.999932, 0.0116276, -1.06412e-05, 0.0116276, -0.999932, -2.3974e-08, 0.0799549, -7.55262e-08)
bones/43/enabled = true
bones/43/position = Vector3(-5.35747e-09, 0.0799549, 3.87814e-10)
bones/43/rotation = Quaternion(-0.0073894, 0.999509, 0.0297332, 0.00650522)
bones/43/scale = Vector3(1, 1, 1)

[node name="shirts" type="MeshInstance3D" parent="Skeleton_01/Skeleton3D"]
mesh = ExtResource("5_nkjm3")
skin = SubResource("Skin_2k135")

[node name="pants" type="MeshInstance3D" parent="Skeleton_01/Skeleton3D"]
mesh = ExtResource("6_pxof6")
skin = SubResource("Skin_gsnyu")

[node name="shoes" type="MeshInstance3D" parent="Skeleton_01/Skeleton3D"]
skin = SubResource("Skin_kfipw")

[node name="hair" type="MeshInstance3D" parent="Skeleton_01/Skeleton3D"]
unique_name_in_owner = true
mesh = ExtResource("4_g5bav")
skin = SubResource("Skin_gsnyu")

[node name="hat" type="MeshInstance3D" parent="Skeleton_01/Skeleton3D"]
skin = SubResource("Skin_gsnyu")

[node name="gloves" type="MeshInstance3D" parent="Skeleton_01/Skeleton3D"]
skin = SubResource("Skin_gsnyu")

[node name="glasses" type="MeshInstance3D" parent="Skeleton_01/Skeleton3D"]
skin = SubResource("Skin_gsnyu")

[node name="emotion" type="MeshInstance3D" parent="Skeleton_01/Skeleton3D"]
mesh = SubResource("ArrayMesh_fyusp")
skin = SubResource("Skin_2k135")

[node name="mustache" type="MeshInstance3D" parent="Skeleton_01/Skeleton3D"]
skin = SubResource("Skin_gsnyu")

[node name="body" type="MeshInstance3D" parent="Skeleton_01/Skeleton3D"]
mesh = SubResource("ArrayMesh_uj2ed")
skin = SubResource("Skin_2k135")

[node name="ear" type="MeshInstance3D" parent="Skeleton_01/Skeleton3D"]
mesh = SubResource("ArrayMesh_hnoft")
skin = SubResource("Skin_2k135")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
unique_name_in_owner = true
libraries = {
&"": SubResource("AnimationLibrary_0c5ky")
}

[node name="AnimationTree" type="AnimationTree" parent="."]
tree_root = SubResource("AnimationNodeBlendTree_07qfg")
anim_player = NodePath("../AnimationPlayer")
parameters/StateMachine/conditions/is_bomb_hold = false
parameters/StateMachine/conditions/is_dead = false
parameters/StateMachine/conditions/is_idle = false
parameters/StateMachine/conditions/is_jumping = false
parameters/StateMachine/conditions/is_lose = false
parameters/StateMachine/conditions/is_moving = false
parameters/StateMachine/conditions/is_moving_bomb = false
parameters/StateMachine/conditions/is_win = false
parameters/StateMachine/move/TimeScale/scale = 1.2

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.581059, 0)
shape = SubResource("CapsuleShape3D_07qfg")

[node name="VFX_PowerUp" type="Node3D" parent="."]
unique_name_in_owner = true
script = ExtResource("37_nkjm3")

[node name="AuraCylinder" type="GPUParticles3D" parent="VFX_PowerUp"]
material_override = SubResource("ShaderMaterial_73v70")
cast_shadow = 0
emitting = false
amount = 1
one_shot = true
local_coords = true
process_material = SubResource("ParticleProcessMaterial_8udw1")
draw_pass_1 = ExtResource("40_oytuk")

[node name="AuraCylinder2" type="GPUParticles3D" parent="VFX_PowerUp"]
visible = false
material_override = SubResource("ShaderMaterial_hrx22")
cast_shadow = 0
emitting = false
amount = 1
one_shot = true
local_coords = true
process_material = SubResource("ParticleProcessMaterial_27u1n")
draw_pass_1 = ExtResource("40_oytuk")

[node name="LittleParticles" type="GPUParticles3D" parent="VFX_PowerUp"]
material_override = SubResource("ShaderMaterial_8cdnn")
emitting = false
amount = 20
one_shot = true
explosiveness = 0.62
randomness = 1.0
local_coords = true
process_material = SubResource("ParticleProcessMaterial_hl27u")
draw_pass_1 = SubResource("QuadMesh_sh0yt")

[node name="AnimationPlayer" type="AnimationPlayer" parent="VFX_PowerUp"]
libraries = {
&"": SubResource("AnimationLibrary_nm8rl")
}
