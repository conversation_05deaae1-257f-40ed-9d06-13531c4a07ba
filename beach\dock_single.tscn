[gd_scene load_steps=10 format=4 uid="uid://cwvpao8htuhk6"]

[ext_resource type="PackedScene" uid="uid://b680t6puup1ub" path="res://beach/Dock2.glb" id="1_bapf2"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_bapf2"]
resource_name = "Wood"
albedo_color = Color(0.533319, 0.416, 0.257181, 1)
metallic_specular = 0.15
roughness = 0.3
metadata/extras = {
"fromFBX": {
"isTruePBR": false,
"shadingModel": "Phong"
}
}

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_447wi"]
resource_name = "Wood_Light"
albedo_color = Color(0.646657, 0.530323, 0.344881, 1)
metallic = 0.4
roughness = 0.272166
metadata/extras = {
"fromFBX": {
"isTruePBR": false,
"shadingModel": "Phong"
}
}

[sub_resource type="ArrayMesh" id="ArrayMesh_so8o6"]
_surfaces = [{
"aabb": AABB(-0.0070587, -0.00644598, -2.42274e-05, 0.00676506, 0.006513, 0.00574075),
"format": 34896613377,
"index_count": 804,
"index_data": PackedByteArray("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"),
"lods": [7.64018e-05, PackedByteArray("AAABAAIAAgADAAAAAgABAAQABQADAAIABgAEAAEABQAGAAMAAQAHAAYABwADAAYACAAJAAoACwAKAAkACgAMAAgACgALAA0ADAAOAAgACwAPAA0ADwAIAA4ADwAOAA0AEAARABIAEAATABEAFAAQABIAEwAQABUAFgAUABIAFgATABUAFgASABcAEwAWABcAGAAZABoAGwAZABgAGgAcABgAGwAYAB0AGgAeABwAHgAbAB0AHwAcAB4AHQAfAB4AIAAhACIAIgAjACAAIgAhACQAJQAjACIAJgAkACEAJQAmACMAIQAnACYAJwAjACYAKAApACoAKwAqACkAKgAsACgAKgArAC0ALAAuACgAKwAvAC0ALwAoAC4ALwAuAC0AMAAxADIAMAAzADEANAAwADIAMwAwADUANgA0ADIANgAzADUANgAyADcAMwA2ADcAOAA5ADoAOgA5ADsAOgA7ADwAOgA8ADgAOwA5AD0APQA5ADgAOwA+ADwAOwA9AD4APAA+ADgAPwA+AD0APgA/ADgAOAA/AD0AQABBAEIAQABCAEMAQwBCAEEARABBAEAAQQBFAEMARgBBAEQARgBFAEEARwBGAEQARQBGAEcARABAAEcARwBDAEUAQABDAEcASABJAEoASwBKAEkATABJAEgASQBNAEsATgBJAEwATgBNAEkATABPAE4ATgBPAE0ATABIAE8ATwBLAE0ATwBIAEoASgBLAE8AUABRAFIAUgBRAFMAUgBUAFAAVQBSAFMAVgBQAFQAVQBTAFYAVABXAFYAVwBVAFYAWABZAFoAWgBbAFgAWQBYAFwAXQBbAFoAXgBZAFwAWgBfAF0AXwBeAFwAXABdAF8AYABhAGIAYABiAGMAYQBgAGQAYwBlAGAAZABmAGEAYwBmAGUAZABnAGYAZgBnAGUAaABpAGoAaABrAGkAagBsAGgAaABtAGsAagBuAGwAbQBvAGsAagBvAG4AbQBuAG8AcABxAHIAcwBwAHIAcABzAHQAdABzAHIAdQBxAHAAdAB1AHAAdQB2AHEAcgB3AHQAdAB3AHUAdwByAHEAdQB3AHYAcQB2AHcAeAB5AHoAewB5AHgAegB8AHgAeAB9AHsAfAB6AH4AfgB7AH0AfgB/AHwAfQB/AH4AgACBAIIAggCDAIAAgQCAAIQAhQCDAIIAhgCBAIQAggCHAIUAhwCGAIQAhACFAIcAiACJAIoAiwCJAIgAjACIAIoAiACNAIsAjACKAI4AjQCPAIsAjwCMAI4AjwCOAIsAkACRAJIAkACSAJMAkQCQAJQAkwCVAJAAlACWAJEAlQCTAJYAlACXAJYAlgCXAJUAmACZAJoAmwCZAJgAnACYAJoAmACdAJsAnACaAJ4AnQCfAJsAnwCcAJ4AnwCeAJsAoAChAKIAogChAKMAoACjAKEAogCkAKAAowCgAKUAowCmAKIApACiAKYApgCjAKUApwCgAKQApwCkAKYApwClAKAApgClAKcA")],
"name": "Wood",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 208,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.00678255, -0.00644598, 0.0052823, 0.00620985, 0.006513, 0.000434227),
"format": 34896613377,
"index_count": 84,
"index_data": PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADAAOAA8AEAARABIAEgATABAAFAAVABYAFgAXABQAGAAZABoAGgAbABgAHAAdAB4AHAAeAB8AIAAhACIAIAAiACMAJAAlACYAJAAnACUAKAApACoAKgArACgALAAtAC4ALAAvAC0AMAAxADIAMAAzADEANAA1ADYANAA3ADUA"),
"name": "Wood_Light",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 56,
"vertex_data": PackedByteArray("5d0k+poQAAA6//H5bRMAAK7+r/1H9AAAWd3i/XTxAADEt9v50QgAABrZCvqRCQAAgdhC/uH2AAArtxP+IfYAABiSoPveGAAAa7NH/CYfAACAsv////8AAC2RWP+3+QAAfG+v/Uf0AADwbvH5bRMAAEaQJPqaEAAA0pDi/XTxAAC3TCT6mhAAAA1u8fltEwAAgW2v/Uf0AAAsTOL9dPEAAJcm2/nRCAAA7EcK+pEJAABTR0L+4fYAAP4lE/4h9gAA6wCg+94YAAA+Ikf8Jh8AAFMh/////wAAAABY/7f5AABK/yMExdoAAP//mwUhAwAADtvUBQAAAABa2lwEpNcAANrbNQDP8gAAfdy/AdIKAAAAt4kB+wkAAF22AAD38QAA07aKBj/lAABpllAHuwYAALiV0QVJ3gAAhbcJCLENAAAck9QFAAAAACxumwUhAwAA4W4jBMXaAADRk1wEpNcAAB1uIwTF2gAA4UnUBQAAAAAtSVwEpNcAANFumwUhAwAArUo1AM/yAADTJYkB+wkAADAlAAD38QAAUEu/AdIKAACmJYoGP+UAADwFUAe7BgAAiwTRBUneAABYJgkIsQ0AAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_4mtni"]
resource_name = "Root Scene_Houses_FirstAge_3_Level1"
_surfaces = [{
"aabb": AABB(-0.0070587, -0.00644598, -2.42274e-05, 0.00676506, 0.006513, 0.00574075),
"format": 34896613383,
"index_count": 804,
"index_data": PackedByteArray("AAABAHgBAAB4AXkBeQF4AQMAeQEDAAIADwAOAHoBewF6AQ4AegF7AQ0ADAANAHsBBAAFAHwBfAF9AQQAfQF8AQYABgAHAH0BfgEJAAsAfwF+AQsACAB+AX8BCgAIAH8BHgAcAIABgAGBAR4AgQGAAR8AgQEfAB0AGwAaAIIBgwGCARoAggGDARkAGAAZAIMBEAARAIQBhAGFARAAhQGEARIAEgATAIUBhgEVABYAhwGGARYAFACGAYcBFwAUAIcBLACIAYkBLAAvAIgBiQGIAS0AiQEtAC4AKQAqAIoBiwGKASoAigGLASsAKAArAIsBJgAkAIwBjAGNASYAjQGMAScAJwAlAI0BjgEhACIAjwGOASIAIACOAY8BIwAgAI8BPwA+AJABkQGQAT4AkAGRAT0APAA9AJEBOAA5AJIBOACSAZMBkwGSAToAkwE6ADsANAA1AJQBlAGVATQAlQGUATYANgA3AJUBMACWAZcBlwEzADAAMgCXAZYBlgExADIAQACYAZkBQABBAJgBmQGYAUMAmQFDAEIATwBOAJoBmwGaAU4AmgGbAU0ATABNAJsBRABFAJwBnAGdAUQAnQGcAUYARgBHAJ0BngFJAEsAnwGeAUsASACeAZ8BSgBIAJ8BXgBcAKABoAGhAV4AoQGgAV8AoQFfAF0AWwBaAKIBowGiAVoAogGjAVkAWABZAKMBUABRAKQBpAGlAVAApQGkAVIAUgBTAKUBpgFVAFYApwGmAVYAVACmAacBVwBUAKcBbACoAakBbABvAKgBqQGoAW0AqQFtAG4AaQBqAKoBqwGqAWoAqgGrAWsAaABrAKsBZgBkAKwBrAGtAWYArQGsAWcAZwBlAK0BrgFhAGIArwGuAWIAYACuAa8BYwBgAK8BcABxAHIAcgBzAHAAdAB1AHYAdwB1AHQAeAB5AHoAewB5AHgA5QCwAbEBsQHkAOUA6wDoALIBsgGzAesAtAHtAO4A7gC1AbQB8wC2AbcB8wDwALYBfAB9AH4AfgB/AHwAgACBAIIAgwCAAIIAhACFAIYAhwCEAIYAiACJAIoAigCLAIgAjACNAI4AjgCPAIwAkACRAJIAkgCTAJAAlACVAJYAlgCXAJQAmACZAJoAmwCYAJoAnACdAJ4AnACfAJ0AoAChAKIAoQCgAKMApAClAKYApwCkAKYAqACpAKoAqgCrAKgArACtAK4ArgCvAKwAsACxALIAsACzALEAtAC1ALYAtAC3ALUAuAC5ALoAugC7ALgAvAC9AL4AvgC/ALwAwADBAMIAwgDDAMAAxADFAMYAxwDEAMYAyADJAMoAyADKAMsAQAG4AbkBQAFBAbgBRAG6AbsBuwFHAUQBSwG8Ab0BSwFIAbwBvgFOAU8BTwG/Ab4BzADNAM4AzgDPAMwA0ADRANIA0gDTANAA1ADVANYA1gDXANQA2ADZANoA2gDbANgA3ADdAN4A3wDcAN4A4ADhAOIA4wDgAOIAwAHBAeYAwAHmAOcAwgHpAOoA6gDDAcIB7ADEAcUB7ADvAMQBxgHxAPIA8gDHAcYB9AD1APYA9gD3APQA+AD5APoA+gD7APgA/AD9AP4A/gD/APwAAAEBAQIBAwEAAQIBBAEFAQYBBwEEAQYBCAEJAQoBCwEIAQoBDAENAQ4BDwEMAQ4BEAERARIBEgETARABFAEVARYBFwEWARUBGAEZARoBGAEbARkBHAEdAR4BHQEcAR8BIAEhASIBIgEjASABJAElASYBJAEnASUBKAEpASoBKAErASkBLAEtAS4BLwEtASwBMAExATIBMwEwATIBNAE1ATYBNgE3ATQBOAE5AToBOwE5ATgBPAE9AT4BPgE/ATwByAHJAUIBQgFDAcgBygFFAUYBRgHLAcoBzAFJAUoBSgHNAcwBTAFNAc4BTAHOAc8BUAFRAVIBUwFQAVIBVAFVAVYBVwFUAVYBWAFZAVoBWwFYAVoBXAFdAV4BXgFfAVwBYAFhAWIBYgFjAWAB0AHRAWYBZgFkAdAB0gF1AXYBdgHTAdIBaAFpAdQB1AHVAWgB1gFtAdcBbQHWAWwBcAFxAXIBcAFzAXEB2AHZAWcBZQHYAWcBdAHaAdsBdAF3AdoB3AFqAWsBawHdAdwB3gFuAW8BbwHfAd4B"),
"lods": [7.64018e-05, PackedByteArray("AAABAAIAAgABAAMABAAFAAYABgAHAAQACAAJAAoACgAJAAsADAANAA4ADwAOAA0AEAARABIAEgATABAAFAAVABYAFwAUABYAGAAZABoAGwAaABkAHAAdAB4AHQAcAB8AIAAhACIAIwAgACIAJAAlACYAJQAkACcAKAApACoAKQAoACsALAAtAC4ALAAvAC0AMAAxADIAMgAzADAANAA1ADYANgA3ADQAOAA5ADoAOAA6ADsAPAA9AD4APwA+AD0AQABBAEIAQgBBAEMARABFAEYARgBHAEQASABJAEoASgBJAEsATABNAE4ATwBOAE0AUABRAFIAUgBTAFAAVABVAFYAVwBUAFYAWABZAFoAWwBaAFkAXABdAF4AXQBcAF8AYABhAGIAYwBgAGIAZABlAGYAZQBkAGcAaABpAGoAaQBoAGsAbABtAG4AbABvAG0AcABxAHIAcgBzAHAAdAB1AHYAdwB1AHQAeAB5AHoAewB5AHgAfAB9AH4AfgB/AHwAgACBAIIAgwCAAIIAhACFAIYAhwCEAIYAiACJAIoAigCLAIgAjACNAI4AjgCPAIwAkACRAJIAkgCTAJAAlACVAJYAlgCXAJQAmACZAJoAmwCYAJoAnACdAJ4AnACfAJ0AoAChAKIAoQCgAKMApAClAKYApwCkAKYAqACpAKoAqgCrAKgArACtAK4ArgCvAKwAsACxALIAsACzALEAtAC1ALYAtAC3ALUAuAC5ALoAugC7ALgAvAC9AL4AvgC/ALwAwADBAMIAwgDDAMAAxADFAMYAxwDEAMYAyADJAMoAyADKAMsAzADNAM4AzgDPAMwA0ADRANIA0gDTANAA1ADVANYA1gDXANQA2ADZANoA2gDbANgA3ADdAN4A3wDcAN4A4ADhAOIA4wDgAOIA5ADlAOYA5ADmAOcA6ADpAOoA6gDrAOgA7ADtAO4A7ADvAO0A8ADxAPIA8gDzAPAA9AD1APYA9gD3APQA+AD5APoA+gD7APgA/AD9AP4A/gD/APwAAAEBAQIBAwEAAQIBBAEFAQYBBwEEAQYBCAEJAQoBCwEIAQoBDAENAQ4BDwEMAQ4BEAERARIBEgETARABFAEVARYBFwEWARUBGAEZARoBGAEbARkBHAEdAR4BHQEcAR8BIAEhASIBIgEjASABJAElASYBJAEnASUBKAEpASoBKAErASkBLAEtAS4BLwEtASwBMAExATIBMwEwATIBNAE1ATYBNgE3ATQBOAE5AToBOwE5ATgBPAE9AT4BPgE/ATwBQAFBAUIBQgFDAUABRAFFAUYBRgFHAUQBSAFJAUoBSgFLAUgBTAFNAU4BTAFOAU8BUAFRAVIBUwFQAVIBVAFVAVYBVwFUAVYBWAFZAVoBWwFYAVoBXAFdAV4BXgFfAVwBYAFhAWIBYgFjAWABZAFlAWYBZQFkAWcBaAFpAWoBagFrAWgBbAFtAW4BbwFsAW4BcAFxAXIBcAFzAXEBdAF1AXYBdAF3AXUB")],
"material": SubResource("StandardMaterial3D_bapf2"),
"name": "Wood",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 480,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.00678255, -0.00644598, 0.0052823, 0.00620985, 0.006513, 0.000434227),
"format": 34896613383,
"index_count": 84,
"index_data": PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADAAOAA8AEAARABIAEgATABAAFAAVABYAFgAXABQAGAAZABoAGgAbABgAHAAdAB4AHAAeAB8AIAAhACIAIAAiACMAJAAlACYAJAAnACUAKAApACoAKgArACgALAAtAC4ALAAvAC0AMAAxADIAMAAzADEANAA1ADYANAA3ADUA"),
"material": SubResource("StandardMaterial3D_447wi"),
"name": "Wood_Light",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 56,
"vertex_data": PackedByteArray("5d0k+poQfPk6//H5bRN8+a7+r/1H9Hz5Wd3i/XTxfPnEt9v50Qh3+RrZCvqRCXf5gdhC/uH2d/krtxP+IfZ3+RiSoPveGCz6a7NH/CYfLPqAsv////8s+i2RWP+3+Sz6fG+v/Uf06PnwbvH5bRPo+UaQJPqaEOj50pDi/XTx6Pm3TCT6mhB8+Q1u8fltE3z5gW2v/Uf0fPksTOL9dPF8+Zcm2/nRCHf57EcK+pEJd/lTR0L+4fZ3+f4lE/4h9nf56wCg+94YLPo+Ikf8Jh8s+lMh/////yz6AABY/7f5LPpK/yMExdoJ/f//mwUhAwn9DtvUBQAACf1a2lwEpNcJ/drbNQDP8nD9fdy/AdIKcP0At4kB+wlw/V22AAD38XD907aKBj/l0/1pllAHuwbT/biV0QVJ3tP9hbcJCLEN0/0ck9QFAABZ/SxumwUhA1n94W4jBMXaWf3Rk1wEpNdZ/R1uIwTF2gn94UnUBQAACf0tSVwEpNcJ/dFumwUhAwn9rUo1AM/ycP3TJYkB+wlw/TAlAAD38XD9UEu/AdIKcP2mJYoGP+XT/TwFUAe7BtP9iwTRBUne0/1YJgkIsQ3T/b68QQi+vEEIvrxBCL68QQjevEQJ3rxECd68RAnevEQJPb30CD299Ag9vfQIPb30CA69tQgOvbUIDr21CA69tQi+vEEIvrxBCL68QQi+vEEI3rxECd68RAnevEQJ3rxECT299Ag9vfQIPb30CD299AibQdD8m0HQ/JtB0PybQdD8WEGM/FhBjPxYQYz8WEGM/AxB0fsMQdH7DEHR+wxB0ftlQXL8ZUFy/GVBcvxlQXL8m0HQ/JtB0PybQdD8m0HQ/FhBjPxYQYz8WEGM/FhBjPwMQdH7DEHR+wxB0fsMQdH7")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_so8o6")

[sub_resource type="BoxShape3D" id="BoxShape3D_4mtni"]
size = Vector3(0.0455322, 0.5, 0.550964)

[sub_resource type="BoxShape3D" id="BoxShape3D_pbbmg"]
size = Vector3(0.0490723, 0.5, 0.549255)

[sub_resource type="BoxShape3D" id="BoxShape3D_oaldi"]
size = Vector3(0.633057, 0.0509766, 0.645935)

[sub_resource type="BoxShape3D" id="BoxShape3D_bapf2"]
size = Vector3(0.546692, 0.0395203, 0.0446777)

[node name="Root Scene" instance=ExtResource("1_bapf2")]

[node name="Houses_FirstAge_3_Level1" parent="RootNode" index="0"]
mesh = SubResource("ArrayMesh_4mtni")

[node name="CollisionShape3D" type="CollisionShape3D" parent="." index="1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0732928, 0.25, 0.326068)
shape = SubResource("BoxShape3D_4mtni")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="." index="2"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.664078, 0.25, 0.325214)
shape = SubResource("BoxShape3D_pbbmg")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="." index="3"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.369849, 0.544934, 0.313401)
shape = SubResource("BoxShape3D_oaldi")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="." index="4"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.368736, 0.164474, 0.0777588)
shape = SubResource("BoxShape3D_bapf2")
