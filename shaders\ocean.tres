[gd_resource type="VisualShader" load_steps=24 format=3 uid="uid://c5khfe7ybgkey"]

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_bkxho"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2
operator = 2

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_4v2gq"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(1.5, 1.25, 1.25, 1.25)]
op_type = 2
operator = 5

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_5k80t"]
constant = 0.9

[sub_resource type="Gradient" id="Gradient_oiqfe"]
offsets = PackedFloat32Array(0.186131, 1)
colors = PackedColorArray(0.246094, 0.246094, 0.246094, 1, 1, 1, 1, 1)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_1ev52"]
noise_type = 2
seed = 6
frequency = 0.12
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_tkl2m"]
seamless = true
color_ramp = SubResource("Gradient_oiqfe")
noise = SubResource("FastNoiseLite_1ev52")

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_eb8cv"]
texture = SubResource("NoiseTexture2D_tkl2m")

[sub_resource type="VisualShaderNodeColorParameter" id="VisualShaderNodeColorParameter_ti41g"]
output_port_for_preview = 0
parameter_name = "ColorParameter"
default_value_enabled = true
default_value = Color(0.0744019, 0.532943, 0.999944, 1)

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_71psl"]
texture = SubResource("NoiseTexture2D_tkl2m")

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_kfnqd"]
output_port_for_preview = 0
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_y32d3"]
default_input_values = [1, Vector2(0.01, 0.01), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_wfiqt"]
input_name = "time"

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_3wm4o"]
default_input_values = [1, Vector2(-0.01, -0.01), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_da4ba"]
input_name = "time"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_50lga"]
input_name = "time"

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_xrg0d"]
default_input_values = [1, Vector2(0.025, 0.025), 2, Vector2(0, 0)]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_utg4i"]
noise_type = 2
seed = 1
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_47dmq"]
seamless = true
noise = SubResource("FastNoiseLite_utg4i")

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_4q42h"]
texture = SubResource("NoiseTexture2D_47dmq")

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_pmtf6"]
input_name = "vertex"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_gnj2w"]
input_name = "normal"

[sub_resource type="VisualShaderNodeMultiplyAdd" id="VisualShaderNodeMultiplyAdd_l4rrl"]
default_input_values = [0, Vector4(0, 0, 0, 0), 1, Vector4(1, 1, 1, 1), 2, Vector4(0, 0, 0, 0)]
op_type = 3

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_4et34"]
default_input_values = [0, Vector3(0, 0, 0), 1, Vector3(5, 0.5, 0.5)]
operator = 2

[resource]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_back, diffuse_lambert, specular_schlick_ggx;

uniform sampler2D tex_vtx_4;
uniform vec4 ColorParameter : source_color = vec4(0.074402, 0.532943, 0.999944, 1.000000);
uniform sampler2D tex_frg_3;
uniform sampler2D tex_frg_15;



void vertex() {
// Input:2
	float n_out2p0 = TIME;


// UVFunc:3
	vec2 n_in3p1 = vec2(0.02500, 0.02500);
	vec2 n_out3p0 = vec2(n_out2p0) * n_in3p1 + UV;


// Texture2D:4
	vec4 n_out4p0 = texture(tex_vtx_4, n_out3p0);


// Input:6
	vec3 n_out6p0 = NORMAL;


// VectorOp:8
	vec3 n_in8p1 = vec3(5.00000, 0.50000, 0.50000);
	vec3 n_out8p0 = n_out6p0 * n_in8p1;


// Input:5
	vec3 n_out5p0 = VERTEX;


// MultiplyAdd:7
	vec4 n_out7p0 = (n_out4p0 * vec4(n_out8p0, 0.0)) + vec4(n_out5p0, 0.0);


// Output:0
	VERTEX = vec3(n_out7p0.xyz);


}

void fragment() {
// ColorParameter:2
	vec4 n_out2p0 = ColorParameter;


// Input:6
	float n_out6p0 = TIME;


// UVFunc:5
	vec2 n_in5p1 = vec2(0.01000, 0.01000);
	vec2 n_out5p0 = vec2(n_out6p0) * n_in5p1 + UV;


// Texture2D:3
	vec4 n_out3p0 = texture(tex_frg_3, n_out5p0);


// Input:9
	float n_out9p0 = TIME;


// UVFunc:8
	vec2 n_in8p1 = vec2(-0.01000, -0.01000);
	vec2 n_out8p0 = vec2(n_out9p0) * n_in8p1 + UV;


// Texture2D:15
	vec4 n_out15p0 = texture(tex_frg_15, n_out8p0);


// VectorOp:10
	vec4 n_out10p0 = n_out3p0 * n_out15p0;


// VectorOp:11
	vec4 n_in11p1 = vec4(1.50000, 1.25000, 1.25000, 1.25000);
	vec4 n_out11p0 = pow(n_out10p0, n_in11p1);


// VectorOp:4
	vec4 n_out4p0 = n_out2p0 + n_out11p0;


// FloatConstant:13
	float n_out13p0 = 0.900000;


// Output:0
	ALBEDO = vec3(n_out4p0.xyz);
	ROUGHNESS = n_out13p0;


}
"
nodes/vertex/0/position = Vector2(640, 120)
nodes/vertex/2/node = SubResource("VisualShaderNodeInput_50lga")
nodes/vertex/2/position = Vector2(-552.787, -31.8079)
nodes/vertex/3/node = SubResource("VisualShaderNodeUVFunc_xrg0d")
nodes/vertex/3/position = Vector2(-132.787, -31.8079)
nodes/vertex/4/node = SubResource("VisualShaderNodeTexture_4q42h")
nodes/vertex/4/position = Vector2(147.213, -11.8079)
nodes/vertex/5/node = SubResource("VisualShaderNodeInput_pmtf6")
nodes/vertex/5/position = Vector2(-552.787, 488.192)
nodes/vertex/6/node = SubResource("VisualShaderNodeInput_gnj2w")
nodes/vertex/6/position = Vector2(-532.787, 228.192)
nodes/vertex/7/node = SubResource("VisualShaderNodeMultiplyAdd_l4rrl")
nodes/vertex/7/position = Vector2(387.213, 408.192)
nodes/vertex/8/node = SubResource("VisualShaderNodeVectorOp_4et34")
nodes/vertex/8/position = Vector2(-172.787, 348.192)
nodes/vertex/connections = PackedInt32Array(2, 0, 3, 2, 3, 0, 4, 0, 4, 0, 7, 0, 5, 0, 7, 2, 6, 0, 8, 0, 8, 0, 7, 1, 7, 0, 0, 0)
nodes/fragment/2/node = SubResource("VisualShaderNodeColorParameter_ti41g")
nodes/fragment/2/position = Vector2(-720, 40)
nodes/fragment/3/node = SubResource("VisualShaderNodeTexture_71psl")
nodes/fragment/3/position = Vector2(-1560, -20)
nodes/fragment/4/node = SubResource("VisualShaderNodeVectorOp_kfnqd")
nodes/fragment/4/position = Vector2(-160, 180)
nodes/fragment/5/node = SubResource("VisualShaderNodeUVFunc_y32d3")
nodes/fragment/5/position = Vector2(-1960, -20)
nodes/fragment/6/node = SubResource("VisualShaderNodeInput_wfiqt")
nodes/fragment/6/position = Vector2(-2440, 60)
nodes/fragment/8/node = SubResource("VisualShaderNodeUVFunc_3wm4o")
nodes/fragment/8/position = Vector2(-1960, 380)
nodes/fragment/9/node = SubResource("VisualShaderNodeInput_da4ba")
nodes/fragment/9/position = Vector2(-2440, 460)
nodes/fragment/10/node = SubResource("VisualShaderNodeVectorOp_bkxho")
nodes/fragment/10/position = Vector2(-980, 280)
nodes/fragment/11/node = SubResource("VisualShaderNodeVectorOp_4v2gq")
nodes/fragment/11/position = Vector2(-700, 340)
nodes/fragment/13/node = SubResource("VisualShaderNodeFloatConstant_5k80t")
nodes/fragment/13/position = Vector2(-160, 660)
nodes/fragment/15/node = SubResource("VisualShaderNodeTexture_eb8cv")
nodes/fragment/15/position = Vector2(-1600, 340)
nodes/fragment/connections = PackedInt32Array(5, 0, 3, 0, 6, 0, 5, 2, 9, 0, 8, 2, 3, 0, 10, 0, 10, 0, 11, 0, 2, 0, 4, 0, 11, 0, 4, 1, 4, 0, 0, 0, 13, 0, 0, 3, 8, 0, 15, 0, 15, 0, 10, 1)
