[remap]

importer="wavefront_obj"
importer_version=1
type="Mesh"
uid="uid://e371smbj4und"
path="res://.godot/imported/cylinder_long.obj-db020cd3f963c38cb166b9aaf85ddb94.mesh"

[deps]

files=["res://.godot/imported/cylinder_long.obj-db020cd3f963c38cb166b9aaf85ddb94.mesh"]

source_file="res://cylinder_long.obj"
dest_files=["res://.godot/imported/cylinder_long.obj-db020cd3f963c38cb166b9aaf85ddb94.mesh", "res://.godot/imported/cylinder_long.obj-db020cd3f963c38cb166b9aaf85ddb94.mesh"]

[params]

generate_tangents=true
generate_lods=true
generate_shadow_mesh=true
generate_lightmap_uv2=false
generate_lightmap_uv2_texel_size=0.2
scale_mesh=Vector3(1, 1, 1)
offset_mesh=Vector3(0, 0, 0)
force_disable_mesh_compression=false
