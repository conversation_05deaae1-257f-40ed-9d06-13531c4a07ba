[gd_scene load_steps=6 format=4 uid="uid://3agu7gb3kjif"]

[ext_resource type="Material" uid="uid://g5x7g5rmse4o" path="res://scenes/bomb_tag/level/cactus/materials/cactus_body.material" id="1_iicbv"]
[ext_resource type="Material" uid="uid://3oxpnywrvsyc" path="res://scenes/bomb_tag/level/cactus/materials/cactus_flowers.material" id="2_mnurc"]

[sub_resource type="ArrayMesh" id="ArrayMesh_uaae7"]
_surfaces = [{
"aabb": AABB(-0.204884, -0.190451, -0.102441, 0.522865, 2.29516, 0.380037),
"format": 34896613377,
"index_count": 936,
"index_data": PackedByteArray("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"),
"lods": [0.0197438, PackedByteArray("DwAMAA4AAAAPAA4ADwANAAwADQAKAAwAAAABAA8AAgABAAAADQALAAoACwAIAAoACwAJAAgACQAGAAgACQAHAAYAAgADAAEABQADAAIABQACAAQABgAFAAQABgAHAAUAAgAZACEAGQAaACEABAACACEAAgAAABkABAAhACAAIQAaACAABgAEACAAAAAbABkAGgAZABsAAAAOABsABgAgAB8AIAAaAB8ACAAGAB8ADgAcABsAHAAaABsADgAMABwACAAfAB4AHwAaAB4ACgAIAB4ADAAdABwADAAKAB0ACgAeAB0AHQAaABwAHgAaAB0AUQBbAFoAWwBpAFoAWwBiAGkAYwBiAFsAaQBhAFoAYQBRAFoAaQBoAGEAYwBbAF0AWwBLAF0AaABgAGEAYQBZAFEAaABnAGAAYABZAGEAZwBfAGAAZwBmAF8AYABPAFkAXwBPAGAAZgBlAF4AZgBeAF8AXgBOAF8AXwBOAE8AXgBNAE4ATgBNAFgAWAAtAE4AWAAsAC0AWQBPAFMATwBOAFMATgAtAC4ATgAuAFMAUwAuAC8AUwAvAFkAWQAvADAAWQAwAFEAMABKAFEAUQBKAFsAWwBKAEsAMAApAEoASgApACoASgAqAFIASwBKAFIAUgAqACsAUgArACwAUgAsAFgATQBSAFgATQBLAFIAXQBLAE0AXQBNAF4AZQBdAF4AZQBkAF0AZABjAF0AKgA6AEIAOgA7AEIAKwAqAEIAKgApADoAKwBCAEEAQgA7AEEALAArAEEAKQA8ADoAOwA6ADwAKQAwADwALABBAEAAQQA7AEAALQAsAEAAMAA9ADwAPQA7ADwAMAAvAD0ALQBAAD8AQAA7AD8ALgAtAD8ALwA+AD0ALwAuAD4ALgA/AD4APgA7AD0APwA7AD4AkgCcAJsAnACqAJsAogCSAJsAqgCiAJsAnACjAKoAqgCpAKIApACjAJwApACcAJ4AnACMAJ4ApQCkAJ4AnACLAIwAkgCLAJwAjACLAJMAiwBrAJMAiwBqAGsAcQBqAIsAcQCLAJIAmgBxAJIApgClAJ4ApgCeAJ8AngCOAJ8AngCMAI4AjgCMAJMAjgCTAJkAkwBtAJkAkwBsAG0AkwBrAGwAmgBwAHEAogCaAJIAlABwAJoAlABvAHAAoQCaAKIAqQChAKIAqQCoAKEAqACnAKAAqACgAKEAjwBvAJQAjwBuAG8AkACPAJQAoACQAKEAmgCQAJQAoQCQAJoAoACPAJAAnwCPAKAAmQBuAI8AmQBtAG4AjwCOAJkAnwCOAI8ApwCfAKAApwCmAJ8AawB7AIMAewB8AIMAbABrAIMAawBqAHsAbACDAIIAgwB8AIIAbQBsAIIAagB9AHsAfAB7AH0AagBxAH0AbQCCAIEAggB8AIEAbgBtAIEAcQB+AH0AfgB8AH0AcQBwAH4AbgCBAIAAgQB8AIAAbwBuAIAAcAB/AH4AcABvAH8AbwCAAH8AfwB8AH4AgAB8AH8A"), 0.113362, PackedByteArray("DwANAAoADQALAAoACgABAA8ABAABAAoACwAJAAoACQAGAAoACQAHAAYABAAZACEAGQAaACEABAAhACAAIQAaACAABgAEACAAGgAZABsABgAgAB8AIAAaAB8ACgAGAB8ACgAfAB4AHwAaAB4ACgAeAB0AHgAaAB0ACgAdABwAHQAaABwAHAAaABsACgAcABsABAAKABkABgAFAAQABgAHAAUABQADAAQABAADAAEACgAbABkAKwA6AEIAOgA7AEIAKwBCAEEAQgA7AEEALAArAEEALABBAEAAQQA7AEAAOwA6ADwAKwA8ADoAQAA7AD8ALwBAAD8APwA7AD4ALwA/AD4ALwAsAEAALwA+AD0APgA7AD0APQA7ADwALwA9ADwAKwAvADwAYwBiAGkAZQBkAGMAaQArAF4ALwArAGkAXgAvAGkAZwBeAGgAZwBmAF4AZgBlAF4AZQBjAF4AXgArACwAXgAsAC8AXgBpAGgAaQBeAGMAbAB7AIMAewB8AIMAbACDAIIAgwB8AIIAbQBsAIIAbQCCAIEAggB8AIEAfAB7AH0AbAB9AHsAgQB8AIAAcACBAIAAgAB8AH8AcACAAH8AcAB+AH0AcAB/AH4AfgB8AH0AfwB8AH4AcABtAIEAbABwAH0ApACjAKoAqgBsAJ8ApgClAKQAqACfAKkAqACnAJ8ApwCmAJ8AnwBwAKoAcABsAKoApgCkAJ8AnwBsAG0AnwBtAHAAnwCqAKkAqgCfAKQA")],
"name": "Plant_2",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 171,
"vertex_data": PackedByteArray("iU804wAAAACJTwAAAAAAACgyNONsKAAAKDIAAGwoAAAoMjTjlmEAACgyAACWYQAAiU804wKKAACJTwAAAooAABZ5NOMCigAAFnkAAAKKAAB3ljTjlmEAAHeWAACWYQAAd5Y042woAAB3lgAAbCgAABZ5NOMAAAAAFnkAAAAAAACXdznn+AQAANqSOed7KgAA2pI554dfAACXdznnCYUAAAhROecJhQAAxTU554dfAADFNTnneyoAAAhROef4BAAAS1Wh6iATAACsW+jsUCgAAE9kte0BRQAA8mzo7FAoAAAqeejsHzkAACp56OzjUAAA8mzo7LFhAACsW+jssWEAAHVP6OzjUAAAdU/o7B85AAAPQKHqWDAAAA9AoeqqWQAAS1Wh6uF2AABTc6Hq4XYAAJCIoeqqWQAAkIih6lgwAABTc6HqIBMAAMvvM/pjagAA/v8z+hhUAAD+/zP6kTQAAMvvM/pHHgAA4dgz+kceAACtyDP6kTQAAK3IM/oYVAAA4dgz+mNqAAC02Wv8pWcAAKvKa/z1UgAAq8pr/LQ1AAC02Wv8BSEAAPfua/wFIQAAAP5r/LQ1AAAA/mv89VIAAPfua/ylZwAAnuxM/tZfAAAZ6Y7/J1QAAFbk//9VRAAAkt+O/ydUAADW2I7/4koAANbYjv/HPQAAkt+O/4I0AAAZ6Y7/gjQAANbvjv/HPQAA1u+O/+JKAABU+Ez+uU8AAFT4TP7wOAAAnuxM/tMoAAAO3Ez+0ygAAFjQTP7wOAAAWNBM/rlPAAAO3Ez+1l8AACPlRr1jagAAq/I/uxhUAACr8j+7kTQAACPlRr1HHgAA/9ElwEceAAB3xC3CkTQAAHfELcIYVAAA/9ElwGNqAAD+/7jHkTQAAK3IuMeRNAAA//+4xxhUAADh2LjHRx4AAOHYuMdjagAAy++4x2NqAADL77jHRx4AAK3IuMcYVAAAXazeuGNqAABtraezY2oAAC2u968YVAAALa73r5E0AABtraezRx4AAF2s3rhHHgAAnauOvJE0AACdq468GFQAAMht+7JjagAAiG5LrxhUAACIbkuvkTQAAMht+7JHHgAAuGwyuEceAAD4a+K7kTQAAPhr4rsYVAAAuGwyuGNqAAAAAKHGfc8AAGsEocZs7gAAcRehxv//AADtLaHG6vkAALM6oca93wAARzahxs7AAABBI6HGPK8AAMUMocZQtQAA9w3ZyMm3AADUItnIJLIAAHs02chzwgAAlTjZyCjfAAC7LNnIcfcAAN4X2cgW/QAANwbZyMfsAAAdAtnIE9AAACIIusq90QAAJRH7yzzUAABZHWzMndcAAHUW+8tayQAAzh/7y9PGAAC3J/vLIs4AAI0p+8v+2gAAPST7y+DlAADkGvvLZ+gAAPwS+8sZ4QAAVAu6yhnoAAAUGbrKzfQAAFQpuspo8AAAkDK6yn3dAABeL7rKIccAAJ4husptugAAXhG6ytK+AADwBbSJUcMAANkLrIcz3wAA3x6sh8bwAADdM7SJvu0AAIk+k4zh1wAAoDiajv+7AACaJZqObKoAAJsQk4x0rQAAcRcmlP7/AABHNiaUzsAAAGsEJpRs7gAAszomlL3fAADFDCaUULUAAAAAJpR9zwAA7S0mlOr5AABBIyaUPK8AAJYlTIV4ggAA/iQVgK+DAAAHMmV895AAAA1FZXyKogAA61IVgByuAACDU0yF5awAAHpG/IicnwAAdDP8iAqOAAB4SGl//DoAAIFVuXtFSAAAh2i5e9hZAABmdml/aWUAAP12oIQzZAAA9WlQiOpWAADvVlCIV0UAABBJoITGOQAA")
}, {
"aabb": AABB(-0.242313, -4.76837e-07, -0.168799, 0.596793, 2.13123, 0.484281),
"format": 34896613377,
"index_count": 1440,
"index_data": PackedByteArray("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"),
"name": "Plant_1",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 576,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_xeviw"]
resource_name = "CanyonPlantsRocksTiles_Cactus_Tall_3"
_surfaces = [{
"aabb": AABB(-0.204884, -0.190451, -0.102441, 0.522865, 2.29516, 0.380037),
"attribute_data": PackedByteArray("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"),
"format": 34896613399,
"index_count": 936,
"index_data": PackedByteArray("GQAUABcAGQAWABQAIAAiADMAIAAhACIACgAIAAYACgANAAgAHwAzADQAHwAgADMAEAAJAA4AEAAMAAkAHgA0ADUAHgAfADQAHQA1ADYAHQAeADUAEwAOABEAEwAQAA4AHAA2ADcAHAAdADYAFgARABQAFgATABEAGwA3ADgAGwAcADcAAAAZABcAAAACABkAGgA4ADkAGgAbADgAAQAaACEAAQAYABoAGAAbABoAGAAVABsAFQAcABsAFQASABwAEgAdABwAEgAPAB0ADwAeAB0ADwALAB4ACwAfAB4ACwAHAB8ABwAgAB8ABwAEACAABAAhACAABAABACEALQAqACwAIgAyADMAIgAjADIAIQA5ACIAIQAaADkAIwAmADIAMgAkADEAMQAlADAAMAAnAC8ALwApAC4ALgArAC0AIwA5ACwAIwAiADkAOAAsADkAOAAtACwANwAtADgANwAuAC0ANgAuADcANgAvAC4ANQAvADYANQAwAC8ANAAwADUANAAxADAAMwAxADQAMwAyADEAKAAjACwACAADAAYACAAFAAMAAwACAAAAAwAFAAIAcwB+AH0AcwBrAH4AUQBTAGQAUQBSAFMAbQB2AHQAbQBsAHYAUABkAGUAUABRAGQAbwB0AHsAbwBtAHQATwBlAGYATwBQAGUAcAB6AHcAcABuAHoATgBmAGcATgBPAGYAcQB3AHUAcQBwAHcATQBnAGgATQBOAGcAcgB1AHwAcgBxAHUATABoAGkATABNAGgAcwB8AHgAcwByAHwASwBpAGoASwBMAGkAOwBLAFIAOwBKAEsASgBMAEsASgBIAEwASABNAEwASABGAE0ARgBOAE0ARgBEAE4ARABPAE4ARABCAE8AQgBQAE8AQgA/AFAAPwBRAFAAPwA9AFEAPQBSAFEAPQA7AFIAXgBbAF0AUwBjAGQAUwBUAGMAUgBqAFMAUgBLAGoAVABXAGMAYwBVAGIAYgBWAGEAYQBYAGAAYABaAF8AXwBcAF4AVABqAF0AVABTAGoAaQBdAGoAaQBeAF0AaABeAGkAaABfAF4AZwBfAGgAZwBgAF8AZgBgAGcAZgBhAGAAZQBhAGYAZQBiAGEAZABiAGUAZABjAGIAWQBUAF0AbAB5AHYAbABrAHkAhwB+AH8AhwCGAH4AjgCFAH0AjgCNAIUAjQCEAIUAjQCMAIQAjACDAIQAjACLAIMAiwCBAIMAiwCJAIEAigCAAIIAigCIAIAAiAB/AIAAiACHAH8ASQB5AHgASQA6AHkAeQBzAHgAeQBrAHMAdABBAHsAdAA+AEEAdwBFAHUAdwBDAEUAdQBHAHwAdQBFAEcAfABJAHgAfABHAEkAdgA+AHQAdgA8AD4AegBDAHcAegBAAEMAeQA8AHYAeQA6ADwAgQBwAIMAgQBuAHAAhQBzAH0AhQByAHMAgABvAIIAgABtAG8AhAByAIUAhABxAHIAfwBtAIAAfwBsAG0AgwBxAIQAgwBwAHEAfgBsAH8AfgBrAGwAfgCOAH0AfgCGAI4AyADTANIAyADAANMApgCoALkApgCnAKgAwgDLAMkAwgDBAMsApQC5ALoApQCmALkAxADJANAAxADCAMkApAC6ALsApAClALoAxQDPAMwAxQDDAM8AowC7ALwAowCkALsAxgDMAMoAxgDFAMwAogC8AL0AogCjALwAxwDKANEAxwDGAMoAoQC9AL4AoQCiAL0AyADRAM0AyADHANEAoAC+AL8AoAChAL4AkACgAKcAkACfAKAAnwChAKAAnwCdAKEAnQCiAKEAnQCbAKIAmwCjAKIAmwCZAKMAmQCkAKMAmQCXAKQAlwClAKQAlwCUAKUAlACmAKUAlACSAKYAkgCnAKYAkgCQAKcAswCwALIAqAC4ALkAqACpALgApwC/AKgApwCgAL8AqQCsALgAuACqALcAtwCrALYAtgCtALUAtQCvALQAtACxALMAqQC/ALIAqQCoAL8AvgCyAL8AvgCzALIAvQCzAL4AvQC0ALMAvAC0AL0AvAC1ALQAuwC1ALwAuwC2ALUAugC2ALsAugC3ALYAuQC3ALoAuQC4ALcArgCpALIAwQDOAMsAwQDAAM4A3ADTANQA3ADbANMA4wDaANIA4wDiANoA4gDZANoA4gDhANkA4QDYANkA4QDgANgA4ADWANgA4ADeANYA3wDVANcA3wDdANUA3QDUANUA3QDcANQAngDOAM0AngCPAM4AzgDIAM0AzgDAAMgAyQCWANAAyQCTAJYAzACaAMoAzACYAJoAygCcANEAygCaAJwA0QCeAM0A0QCcAJ4AywCTAMkAywCRAJMAzwCYAMwAzwCVAJgAzgCRAMsAzgCPAJEA1gDFANgA1gDDAMUA2gDIANIA2gDHAMgA1QDEANcA1QDCAMQA2QDHANoA2QDGAMcA1ADCANUA1ADBAMIA2ADGANkA2ADFAMYA0wDBANQA0wDAAMEA0wDjANIA0wDbAOMA"),
"lods": [0.0197438, PackedByteArray("YQFdARcASwFhARcAYQFfAV0BXwFaAV0BSwECAGEBTgECAEsBXwFcAVkBXAFWAVkBXAFYAVYBWAFSAVYBWAFVAVIBTQEFAAIAUQEFAE0BUQFNAQYAUwFRAQYAUwENAFEBTwFiAWkBYgEmAGkBUAFPAWkBTwFMAWIBUAFpAWgBaQEkAGgBVAFQAWgBTAFjAWIBKABiAWMBTAFgAWMBVAFoAWcBaAElAGcBVwFUAWcBYAFkAWMBZAEqAGMBYAFeAWQBVwFnAWYBZwEnAGYBWwFXAWYBXgFlAWQBXgFbAWUBWwFmAWUBZQErAGQBZgEpAGUBiQGQAX0AkAGjAX0AkAGGAKMBmwGGAJABowGZAX0AmQGJAX0AowGiAZkBmwGRAZIBkQGDAZIBogGYAZkBmQGOAYkBogGhAZgBmAGOAZoBoQGWAZgBoQGgAZYBmAGIAY4BlgGIAZgBoAGeAZQBoAGUAZcBlAGGAZYBlgGGAYgBlAFuAIYBhgFuAI0BjQFxAYYBjQFAAHEBjgGIAYwBiAGHAYwBhwFxAXMBhwFzAYwBjAFzAXUBjAF1AY4BjgF1AXcBjwF3AYkBdwGBAYkBiQGBAZABkAGBAYQBdwFqAYEBgQFqAWwBggFsAYsBgwGCAYsBigFsAT4AigE+AG8BigFvAXsAhQGKAXsAhQGDAYoBkwGDAYUBkwGFAZUBnwGTAZUBnwGdAZMBnQGcAZMBbQF5AYABeQFXAIABbgFtAYABbQFrAXkBbgGAAX8BgAFVAH8BcAFuAX8BawF6AXkBWQB5AXoBawF4AXoBcAF/AX4BfwFWAH4BcgFwAX4BeAF7AXoBewFbAHoBeAF2AXsBcgF+AX0BfgFYAH0BdAFyAX0BdgF8AXsBdgF0AXwBdAF9AXwBfAFcAHsBfQFaAHwBwwHLAcoBywHiAcoB1wHDAcoB4QHXAcoBywHYAeEB4QHgAdcB2QHYAc0B2QHMAc4BzAG+Ac4B3QDaAc4BywG6Ab4BwwG6AcsBvgG6AcUBvAGlAcUBugGPAKYBsAGPALoBsAG7AcMByQGwAcMB3AHdAM8B3AHPAdEBzwG/AdEBzgG9Ab8BvwG9AcQBvwHEAdAAxAGqAdAAxAGoAaoBxAGlAagByAGcALAB1wHIAcMBxgGcAMgBxgGaAJwA1QHIAdcB4AHVAdcB4AHfAdUB3wHdAdMB3wHSAdUBwQGaAMYBwQGsAZoAwgHBAcYB0gHCAdUByAHCAcYB1gHCAcgB0gHAAcIB0AHAAdIBxwGsAcABxwGVAKwBwAHDAMcB0AHDAMAB3gHQAdQB3gHbAdABpwGyAbkBsgGsALkBqQGnAbkBpwGkAbIBqQG5AbgBuQGqALgBqwGpAbgBpAGzAbIBrgCyAbMBpAGxAbMBqwG4AbcBuAGrALcBrQGrAbcBsQG0AbMBtAGwALMBsQGvAbQBrQG3AbYBtwGtALYBrgGtAbYBrwG1AbQBrwGuAbUBrgG2AbUBtQGxALQBtgGvALUB"), 0.113362, PackedByteArray("+QD4APIA+AATAPIA9ADkAPkA6ADkAPQAEwDxAPMA8QDtAPMA8QDwAO0A6gD6ADIA+gAmADIA6gAyAP4AMgAkAP4A7wDqAP4AKAD6APwA7wD+AP0A/gAlAP0A9gDvAP0A9gD9AC8A/QAnAC8A9gAvAC4ALwApAC4A9gAuAC0ALgArAC0ALQAqAPwA9wAtAPwA6wD1APsA7gDsAOcA7gANAOwA7ADmAOcA6QDmAOUAEgAsACMAAAFUAGMAVABXAGMAAAFjAAsBYwBVAAsBAwEAAQsBAwELAWEACwFWAGEAWQBUAF0APwBdAFQAYQBYAGAASABhAGAAYABaAF8ABgFgAF8ABgEEAQoBBgFfAF4AXwBcAF4AXgBbAF0ABwFeAF0AAQEIAQkBFgEVASIBHAEZARYBIgE+ABMBRwA+ACIBDgFHAI4AHgEMASABHwEdAQ0BHQEaAQ0BGwEXAREBEgH/AEEADwECAQUBEAEjASEBJAEUARgBKAGpALgAqQCsALgAKAG4ADIBuACqADIBKwEoATIBKwEyAbYAMgGrALYArgCpALIAlACyAKkAtgCtALUAnQC2ALUAtQCvALQALgG1ALQALgGzALIALgG0ALMAswCwALIAtACxALMALgEsATEBKQEvATABPQE8AUgBSAEnAToBQgFAAT0BRAEzAUYBRQFDATQBQwHeADUBNAGcAOMAnAAlAeMAQQE+ATgBOQEmAZYANgEqAS0BNwFJAUcBSgE7AT8B")],
"material": ExtResource("1_iicbv"),
"name": "Plant_2",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 483,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.242313, -4.76837e-07, -0.168799, 0.596793, 2.13123, 0.484281),
"attribute_data": PackedByteArray("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"),
"format": 34896613399,
"index_count": 1440,
"index_data": PackedByteArray("FAAAAAEAFAAQAAAAAwAFAAcAAwACAAUABgALAA8ABgAEAAsAJgEhASUBJgEiASEBhgCBAIUAhgCCAIEAFgARABUAFgASABEAIgAbABgAIgAgABsAJAAeAB8AJAAmAB4AHQAhACMAHQAaACEAGQAnACUAGQAcACcAPAAoACkAPAA4ACgAKwAtAC8AKwAqAC0ALgAzADcALgAsADMADgAJAA0ADgAKAAkADAATABcADAAIABMAPgA5AD0APgA6ADkASwBEAEEASwBJAEQATQBGAEcATQBPAEYARQBIAEoARQBCAEgAQABOAEwAQABDAE4AZABQAFEAZABgAFAAUwBVAFcAUwBSAFUAVgBbAF8AVgBUAFsANgAxADUANgAyADEANAA7AD8ANAAwADsAZgBhAGUAZgBiAGEAcwBsAGkAcwBxAGwAdQBuAG8AdQB3AG4AbQBwAHIAbQBqAHAAaAB2AHQAaABrAHYAjAB4AHkAjACIAHgAewB9AH8AewB6AH0AfgCDAIcAfgB8AIMAXgBZAF0AXgBaAFkAXABjAGcAXABYAGMAjgCJAI0AjgCKAIkAmwCUAJEAmwCZAJQAnQCWAJcAnQCfAJYAlQCYAJoAlQCSAJgAkACeAJwAkACTAJ4AtACgAKEAtACwAKAAowClAKcAowCiAKUApgCrAK8ApgCkAKsAhACLAI8AhACAAIsAtgCxALUAtgCyALEAwwC8ALkAwwDBALwAxQC+AL8AxQDHAL4AvQDAAMIAvQC6AMAAuADGAMQAuAC7AMYA3ADIAMkA3ADYAMgAywDNAM8AywDKAM0AzgDTANcAzgDMANMArgCpAK0ArgCqAKkArACzALcArACoALMA3gDZAN0A3gDaANkA6gDjAOAA6gDoAOMA7ADmAOcA7ADuAOYA5QDpAOsA5QDiAOkA4QDvAO0A4QDkAO8ABAHwAPEABAEAAfAA8wD1APcA8wDyAPUA9gD7AP8A9gD0APsA1gDRANUA1gDSANEA1ADbAN8A1ADQANsABgEBAQUBBgECAQEBEgELAQgBEgEQAQsBFAEOAQ8BFAEWAQ4BDQERARMBDQEKAREBCQEXARUBCQEMARcBLAEYARkBLAEoARgBGwEdAR8BGwEaAR0BHgEjAScBHgEcASMB/gD5AP0A/gD6APkA/AADAQcB/AD4AAMBLgEpAS0BLgEqASkBOgEzATABOgE4ATMBPAE2ATcBPAE+ATYBNQE5ATsBNQEyATkBMQE/AT0BMQE0AT8BJAErAS8BJAEgASsBTAFAAUEBTAFIAUABRwFnAncCRwFEAWcCRQFNAlMCRQFCAU0CTgFJAU0BTgFKAUkBWwFUAVEBWwFZAVQBXQFWAVcBXQFfAVYBVQFYAVoBVQFSAVgBUAFeAVwBUAFTAV4BbAFgAWEBbAFoAWABZwFtAn0CZwFkAW0CZQFVAlsCZQFiAVUCbgFpAW0BbgFqAWkBegFzAXABegF4AXMBfAF2AXcBfAF+AXYBdQF5AXsBdQFyAXkBcQF/AX0BcQF0AX8BjAGAAYEBjAGIAYABhwFzAmMChwGEAXMChQFcAkAChQGCAVwCjgGJAY0BjgGKAYkBmgGTAZABmgGYAZMBnAGWAZcBnAGeAZYBlQGZAZsBlQGSAZkBkQGfAZ0BkQGUAZ8BrAGgAaEBrAGoAaABpwF4AmgCpwGkAXgCpgFDAkUCpgGjAUMCrgGpAa0BrgGqAakBugGzAbABugG4AbMBvAG2AbcBvAG+AbYBtQG5AbsBtQGyAbkBsQG/Ab0BsQG0Ab8BzAHAAcEBzAHIAcABxwF+Am4CxwHEAX4CxgFGAkgCxgHDAUYCzgHJAc0BzgHKAckB2gHTAdAB2gHYAdMB3AHWAdcB3AHeAdYB1QHZAdsB1QHSAdkB0QHfAd0B0QHUAd8B7AHgAeEB7AHoAeAB5wFkAnQC5wHkAWQC5gFKAk4C5gHjAUoC7gHpAe0B7gHqAekB+wH0AfEB+wH5AfQB/QH2AfcB/QH/AfYB9QH4AfoB9QHyAfgB8AH+AfwB8AHzAf4BDAIAAgECDAIIAgACBwJqAnoCBwIEAmoCBgJRAlcCBgIDAlECDgIJAg0CDgIKAgkCGwIUAhECGwIZAhQCHQIWAhcCHQIfAhYCFQIYAhoCFQISAhgCEAIeAhwCEAITAh4CLAIgAiECLAIoAiACJwJxAmECJwIkAnECJQJYAl4CJQIiAlgCLgIpAi0CLgIqAikCOwI0AjECOwI5AjQCPQI2AjcCPQI/AjYCNQI4AjoCNQIyAjgCMAI+AjwCMAIzAj4CWQIvAl8CWQIrAi8CUAIPAlYCUAILAg8CSwLvAU8CSwLrAe8BRwLPAUkCRwLLAc8BQgKvAUQCQgKrAa8BXQKPAUECXQKLAY8BVAJvAVoCVAJrAW8BTAJPAVICTAJLAU8BeQKlAWkCeQKiAaUBcgKGAWICcgKDAYYBbAJmAXwCbAJjAWYBZgJGAXYCZgJDAUYBcAImAmACcAIjAiYCawIFAnsCawICAgUCZQLlAXUCZQLiAeUBfwLFAW8CfwLCAcUBjAKAAoECjAKIAoAChwKmA7YDhwKEAqYDhgKMA5IDhgKDAowDjgKJAo0CjgKKAokCmwKUApECmwKZApQCnQKWApcCnQKfApYClQKYApoClQKSApgCkAKeApwCkAKTAp4CrAKgAqECrAKoAqACpwKsA7wDpwKkAqwDpgKVA5sDpgKjApUDrgKpAq0CrgKqAqkCuwK0ArECuwK5ArQCvQK2ArcCvQK/ArYCtQK4AroCtQKyArgCsAK+ArwCsAKzAr4CzALAAsECzALIAsACxwKyA6IDxwLEArIDxgKdA4EDxgLDAp0DzgLJAs0CzgLKAskC2wLUAtEC2wLZAtQC3QLWAtcC3QLfAtYC1QLYAtoC1QLSAtgC0ALeAtwC0ALTAt4C7QLgAuEC7QLpAuAC5wK5A6kD5wLkArkD5QKCA4QD5QLiAoID7gLoAuwC7gLqAugC+wL0AvEC+wL5AvQC/QL2AvcC/QL/AvYC9QL4AvoC9QLyAvgC8AL+AvwC8ALzAv4CDAMAAwEDDAMIAwADBwO/A68DBwMEA78DBQOHA4kDBQMCA4cDDgMJAw0DDgMKAwkDGgMTAxADGgMYAxMDHAMWAxcDHAMeAxYDFQMZAxsDFQMSAxkDEQMfAx0DEQMUAx8DLAMgAyEDLAMoAyADJwOlA7UDJwMkA6UDJQOKA44DJQMiA4oDLgMpAy0DLgMqAykDOgMzAzADOgM4AzMDPAM2AzcDPAM+AzYDNQM5AzsDNQMyAzkDMQM/Az0DMQM0Az8DTANAA0EDTANIA0ADRwOrA7sDRwNEA6sDRQOQA5YDRQNCA5ADTgNJA00DTgNKA0kDWgNTA1ADWgNYA1MDXANWA1cDXANeA1YDVQNZA1sDVQNSA1kDUQNfA10DUQNUA18DbANgA2EDbANoA2ADZwOwA6ADZwNkA7ADZgOYA54DZgNjA5gDbgNpA20DbgNqA2kDegNzA3ADegN4A3MDfAN2A3cDfAN+A3YDdQN5A3sDdQNyA3kDcQN/A30DcQN0A38DmQNvA58DmQNrA28DkQNPA5cDkQNLA08DiwMvA48DiwMrAy8DhgMPA4gDhgMLAw8DgwPvAoUDgwPrAu8CnAPPAoADnAPLAs8ClAOvApoDlAOrAq8CjQOPApMDjQOLAo8CuAPmAqgDuAPjAuYCswPFAqMDswPCAsUCrQOlAr0DrQOiAqUCpwOFArcDpwOCAoUCsQNlA6EDsQNiA2UDqgNGA7oDqgNDA0YDpAMmA7QDpAMjAyYDvgMGA64DvgMDAwYD"),
"material": ExtResource("2_mnurc"),
"name": "Plant_1",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 960,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_uaae7")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_ani52"]
data = PackedVector3Array(0.0424324, -0.190451, -0.102441, 0.102441, 1.84657, -0.0424324, 0.0424324, 1.84657, -0.102441, 0.0424324, -0.190451, -0.102441, 0.102441, -0.190451, -0.0424324, 0.102441, 1.84657, -0.0424324, -0.0424324, 1.84657, -0.102441, 0.03067, 1.91317, -0.0740441, -0.03067, 1.91317, -0.0740441, -0.0424324, 1.84657, -0.102441, 0.0424324, 1.84657, -0.102441, 0.03067, 1.91317, -0.0740441, -0.0424324, 1.84657, 0.102441, -0.102441, -0.190451, 0.0424324, -0.102441, 1.84657, 0.0424324, -0.0424324, 1.84657, 0.102441, -0.0424324, -0.190451, 0.102441, -0.102441, -0.190451, 0.0424324, -0.102441, 1.84657, -0.0424324, -0.03067, 1.91317, -0.0740441, -0.0740441, 1.91317, -0.03067, -0.102441, 1.84657, -0.0424324, -0.0424324, 1.84657, -0.102441, -0.03067, 1.91317, -0.0740441, 0.0424324, -0.190451, 0.102441, -0.0424324, 1.84657, 0.102441, 0.0424324, 1.84657, 0.102441, 0.0424324, -0.190451, 0.102441, -0.0424324, -0.190451, 0.102441, -0.0424324, 1.84657, 0.102441, -0.102441, 1.84657, 0.0424324, -0.0740441, 1.91317, -0.03067, -0.0740441, 1.91317, 0.03067, -0.102441, 1.84657, 0.0424324, -0.102441, 1.84657, -0.0424324, -0.0740441, 1.91317, -0.03067, -0.0424324, 1.84657, 0.102441, -0.0740441, 1.91317, 0.03067, -0.03067, 1.91317, 0.0740441, -0.0424324, 1.84657, 0.102441, -0.102441, 1.84657, 0.0424324, -0.0740441, 1.91317, 0.03067, 0.102441, -0.190451, 0.0424324, 0.0424324, 1.84657, 0.102441, 0.102441, 1.84657, 0.0424324, 0.102441, -0.190451, 0.0424324, 0.0424324, -0.190451, 0.102441, 0.0424324, 1.84657, 0.102441, 0.0424324, 1.84657, 0.102441, -0.03067, 1.91317, 0.0740441, 0.03067, 1.91317, 0.0740441, 0.0424324, 1.84657, 0.102441, -0.0424324, 1.84657, 0.102441, -0.03067, 1.91317, 0.0740441, 0.102441, -0.190451, -0.0424324, 0.102441, 1.84657, 0.0424324, 0.102441, 1.84657, -0.0424324, 0.102441, -0.190451, -0.0424324, 0.102441, -0.190451, 0.0424324, 0.102441, 1.84657, 0.0424324, 0.102441, 1.84657, 0.0424324, 0.03067, 1.91317, 0.0740441, 0.0740441, 1.91317, 0.03067, 0.102441, 1.84657, 0.0424324, 0.0424324, 1.84657, 0.102441, 0.03067, 1.91317, 0.0740441, -0.0424324, 1.84657, -0.102441, 0.0424324, -0.190451, -0.102441, 0.0424324, 1.84657, -0.102441, -0.0424324, 1.84657, -0.102441, -0.0424324, -0.190451, -0.102441, 0.0424324, -0.190451, -0.102441, 0.102441, 1.84657, -0.0424324, 0.0740441, 1.91317, 0.03067, 0.0740441, 1.91317, -0.03067, 0.102441, 1.84657, -0.0424324, 0.102441, 1.84657, 0.0424324, 0.0740441, 1.91317, 0.03067, 0, 1.94075, 0, -0.03067, 1.91317, -0.0740441, 0.03067, 1.91317, -0.0740441, 0.0740441, 1.91317, 0.03067, 0, 1.94075, 0, 0.0740441, 1.91317, -0.03067, 0.238087, 2.05277, 0.0554963, 0.220653, 2.0895, 0.0159163, 0.244573, 2.0895, 0.0398362, 0.238087, 2.05277, 0.0554963, 0.204993, 2.05277, 0.022403, 0.220653, 2.0895, 0.0159163, -0.0740441, 1.91317, -0.03067, -0.03067, 1.91317, -0.0740441, 0, 1.94075, 0, -0.0740441, 1.91317, -0.03067, 0, 1.94075, 0, -0.0740441, 1.91317, 0.03067, -0.0740441, 1.91317, 0.03067, 0, 1.94075, 0, -0.03067, 1.91317, 0.0740441, -0.03067, 1.91317, 0.0740441, 0, 1.94075, 0, 0.03067, 1.91317, 0.0740441, 0.03067, 1.91317, 0.0740441, 0, 1.94075, 0, 0.0740441, 1.91317, 0.03067, 0.0740441, 1.91317, -0.03067, 0, 1.94075, 0, 0.03067, 1.91317, -0.0740441, -0.102441, -0.190451, 0.0424324, -0.102441, 1.84657, -0.0424324, -0.102441, 1.84657, 0.0424324, -0.102441, -0.190451, 0.0424324, -0.102441, -0.190451, -0.0424324, -0.102441, 1.84657, -0.0424324, -0.102441, 1.84657, -0.0424324, -0.0424324, -0.190451, -0.102441, -0.0424324, 1.84657, -0.102441, -0.102441, 1.84657, -0.0424324, -0.102441, -0.190451, -0.0424324, -0.0424324, -0.190451, -0.102441, 0.224033, 1.53226, 0.0554963, 0.149338, 1.42027, 0.0554964, 0.147169, 1.46702, 0.0554963, 0.224033, 1.53226, 0.0554963, 0.263124, 1.50652, 0.0554963, 0.149338, 1.42027, 0.0554964, 0.284888, 2.05277, 0.0554964, 0.244573, 2.0895, 0.0398362, 0.278401, 2.0895, 0.0398362, 0.284888, 2.05277, 0.0554964, 0.238087, 2.05277, 0.0554963, 0.244573, 2.0895, 0.0398362, 0.290765, 1.48832, -0.0243981, 0.317981, 1.60018, 0.022403, 0.317981, 1.60018, -0.0243981, 0.290765, 1.48832, -0.0243981, 0.290765, 1.48832, 0.022403, 0.317981, 1.60018, 0.022403, 0.317981, 2.05277, 0.022403, 0.278401, 2.0895, 0.0398362, 0.302321, 2.0895, 0.0159163, 0.317981, 2.05277, 0.022403, 0.284888, 2.05277, 0.0554964, 0.278401, 2.0895, 0.0398362, 0.263124, 1.50652, -0.0574914, 0.317981, 1.60018, -0.0243981, 0.284888, 1.60018, -0.0574914, 0.263124, 1.50652, -0.0574914, 0.290765, 1.48832, -0.0243981, 0.317981, 1.60018, -0.0243981, 0.317981, 2.05277, -0.0243981, 0.302321, 2.0895, 0.0159163, 0.302321, 2.0895, -0.0179114, 0.317981, 2.05277, -0.0243981, 0.317981, 2.05277, 0.022403, 0.302321, 2.0895, 0.0159163, 0.224033, 1.53226, -0.0574914, 0.284888, 1.60018, -0.0574914, 0.238087, 1.60018, -0.0574914, 0.224033, 1.53226, -0.0574914, 0.263124, 1.50652, -0.0574914, 0.284888, 1.60018, -0.0574914, 0.284888, 2.05277, -0.0574914, 0.302321, 2.0895, -0.0179114, 0.278401, 2.0895, -0.0418313, 0.284888, 2.05277, -0.0574914, 0.317981, 2.05277, -0.0243981, 0.302321, 2.0895, -0.0179114, 0.196392, 1.55045, -0.0243981, 0.238087, 1.60018, -0.0574914, 0.204993, 1.60018, -0.0243981, 0.196392, 1.55045, -0.0243981, 0.224033, 1.53226, -0.0574914, 0.238087, 1.60018, -0.0574914, 0.238087, 2.05277, -0.0574914, 0.278401, 2.0895, -0.0418313, 0.244573, 2.0895, -0.0418313, 0.238087, 2.05277, -0.0574914, 0.284888, 2.05277, -0.0574914, 0.278401, 2.0895, -0.0418313, 0.196392, 1.55045, 0.022403, 0.204993, 1.60018, -0.0243981, 0.204993, 1.60018, 0.022403, 0.196392, 1.55045, 0.022403, 0.196392, 1.55045, -0.0243981, 0.204993, 1.60018, -0.0243981, 0.204993, 2.05277, -0.0243981, 0.244573, 2.0895, -0.0418313, 0.220653, 2.0895, -0.0179114, 0.204993, 2.05277, -0.0243981, 0.238087, 2.05277, -0.0574914, 0.244573, 2.0895, -0.0418313, 0.224033, 1.53226, 0.0554963, 0.204993, 1.60018, 0.022403, 0.238087, 1.60018, 0.0554963, 0.224033, 1.53226, 0.0554963, 0.196392, 1.55045, 0.022403, 0.204993, 1.60018, 0.022403, 0.204993, 2.05277, 0.022403, 0.220653, 2.0895, -0.0179114, 0.220653, 2.0895, 0.0159163, 0.204993, 2.05277, 0.022403, 0.204993, 2.05277, -0.0243981, 0.220653, 2.0895, -0.0179114, 0.261487, 2.10471, -0.000997527, 0.278401, 2.0895, 0.0398362, 0.244573, 2.0895, 0.0398362, 0.220653, 2.0895, -0.0179114, 0.261487, 2.10471, -0.000997527, 0.220653, 2.0895, 0.0159163, -0.178796, 1.5904, 0.16673, -0.136218, 1.62713, 0.17432, -0.16941, 1.62713, 0.180845, -0.178796, 1.5904, 0.16673, -0.132873, 1.5904, 0.157703, -0.136218, 1.62713, 0.17432, 0.302321, 2.0895, 0.0159163, 0.278401, 2.0895, 0.0398362, 0.261487, 2.10471, -0.000997527, 0.302321, 2.0895, 0.0159163, 0.261487, 2.10471, -0.000997527, 0.302321, 2.0895, -0.0179114, 0.302321, 2.0895, -0.0179114, 0.261487, 2.10471, -0.000997527, 0.278401, 2.0895, -0.0418313, 0.278401, 2.0895, -0.0418313, 0.261487, 2.10471, -0.000997527, 0.244573, 2.0895, -0.0418313, 0.244573, 2.0895, -0.0418313, 0.261487, 2.10471, -0.000997527, 0.220653, 2.0895, -0.0179114, 0.220653, 2.0895, 0.0159163, 0.261487, 2.10471, -0.000997527, 0.244573, 2.0895, 0.0398362, 0.290765, 1.48832, 0.022403, 0.284888, 1.60018, 0.0554963, 0.317981, 1.60018, 0.022403, 0.290765, 1.48832, 0.022403, 0.263124, 1.50652, 0.0554963, 0.284888, 1.60018, 0.0554963, 0.0208798, 1.38118, 0.0224031, 0.149338, 1.42027, 0.0554964, 0.150871, 1.38721, 0.0224031, 0.0208798, 1.38118, 0.0224031, 0.0193463, 1.41424, 0.0554964, 0.149338, 1.42027, 0.0554964, 0.0171774, 1.46099, 0.0554964, 0.145635, 1.50007, 0.022403, 0.147169, 1.46702, 0.0554963, 0.0171774, 1.46099, 0.0554964, 0.0156438, 1.49404, 0.0224031, 0.145635, 1.50007, 0.022403, 0.0156438, 1.49404, 0.0224031, 0.145635, 1.50007, -0.0243981, 0.145635, 1.50007, 0.022403, 0.0156438, 1.49404, 0.0224031, 0.0156438, 1.49404, -0.0243981, 0.145635, 1.50007, -0.0243981, 0.0156438, 1.49404, -0.0243981, 0.147169, 1.46702, -0.0574914, 0.145635, 1.50007, -0.0243981, 0.0156438, 1.49404, -0.0243981, 0.0171774, 1.46099, -0.0574914, 0.147169, 1.46702, -0.0574914, 0.0171774, 1.46099, -0.0574914, 0.149338, 1.42027, -0.0574914, 0.147169, 1.46702, -0.0574914, 0.0171774, 1.46099, -0.0574914, 0.0193463, 1.41424, -0.0574914, 0.149338, 1.42027, -0.0574914, 0.0193463, 1.41424, -0.0574914, 0.150871, 1.38721, -0.0243981, 0.149338, 1.42027, -0.0574914, 0.0193463, 1.41424, -0.0574914, 0.0208798, 1.38118, -0.024398, 0.150871, 1.38721, -0.0243981, 0.0208798, 1.38118, -0.024398, 0.150871, 1.38721, 0.0224031, 0.150871, 1.38721, -0.0243981, 0.0208798, 1.38118, -0.024398, 0.0208798, 1.38118, 0.0224031, 0.150871, 1.38721, 0.0224031, 0.238087, 2.05277, 0.0554963, 0.284888, 1.60018, 0.0554963, 0.238087, 1.60018, 0.0554963, 0.238087, 2.05277, 0.0554963, 0.284888, 2.05277, 0.0554964, 0.284888, 1.60018, 0.0554963, 0.284888, 1.60018, 0.0554963, 0.224033, 1.53226, 0.0554963, 0.238087, 1.60018, 0.0554963, 0.284888, 1.60018, 0.0554963, 0.263124, 1.50652, 0.0554963, 0.224033, 1.53226, 0.0554963, 0.317981, 1.60018, -0.0243981, 0.284888, 2.05277, -0.0574914, 0.284888, 1.60018, -0.0574914, 0.317981, 1.60018, -0.0243981, 0.317981, 2.05277, -0.0243981, 0.284888, 2.05277, -0.0574914, 0.238087, 1.60018, -0.0574914, 0.204993, 2.05277, -0.0243981, 0.204993, 1.60018, -0.0243981, 0.238087, 1.60018, -0.0574914, 0.238087, 2.05277, -0.0574914, 0.204993, 2.05277, -0.0243981, 0.204993, 1.60018, -0.0243981, 0.204993, 2.05277, 0.022403, 0.204993, 1.60018, 0.022403, 0.204993, 1.60018, -0.0243981, 0.204993, 2.05277, -0.0243981, 0.204993, 2.05277, 0.022403, 0.204993, 1.60018, 0.022403, 0.238087, 2.05277, 0.0554963, 0.238087, 1.60018, 0.0554963, 0.204993, 1.60018, 0.022403, 0.204993, 2.05277, 0.022403, 0.238087, 2.05277, 0.0554963, 0.317981, 1.60018, 0.022403, 0.317981, 2.05277, -0.0243981, 0.317981, 1.60018, -0.0243981, 0.317981, 1.60018, 0.022403, 0.317981, 2.05277, 0.022403, 0.317981, 2.05277, -0.0243981, 0.284888, 1.60018, -0.0574914, 0.238087, 2.05277, -0.0574914, 0.238087, 1.60018, -0.0574914, 0.284888, 1.60018, -0.0574914, 0.284888, 2.05277, -0.0574914, 0.238087, 2.05277, -0.0574914, 0.284888, 1.60018, 0.0554963, 0.317981, 2.05277, 0.022403, 0.317981, 1.60018, 0.022403, 0.284888, 1.60018, 0.0554963, 0.284888, 2.05277, 0.0554964, 0.317981, 2.05277, 0.022403, 0.149338, 1.42027, -0.0574914, 0.224033, 1.53226, -0.0574914, 0.147169, 1.46702, -0.0574914, 0.149338, 1.42027, -0.0574914, 0.263124, 1.50652, -0.0574914, 0.224033, 1.53226, -0.0574914, 0.145635, 1.50007, 0.022403, 0.224033, 1.53226, 0.0554963, 0.147169, 1.46702, 0.0554963, 0.145635, 1.50007, 0.022403, 0.196392, 1.55045, 0.022403, 0.224033, 1.53226, 0.0554963, 0.150871, 1.38721, -0.0243981, 0.263124, 1.50652, -0.0574914, 0.149338, 1.42027, -0.0574914, 0.150871, 1.38721, -0.0243981, 0.290765, 1.48832, -0.0243981, 0.263124, 1.50652, -0.0574914, 0.145635, 1.50007, -0.0243981, 0.196392, 1.55045, 0.022403, 0.145635, 1.50007, 0.022403, 0.145635, 1.50007, -0.0243981, 0.196392, 1.55045, -0.0243981, 0.196392, 1.55045, 0.022403, 0.150871, 1.38721, 0.0224031, 0.290765, 1.48832, -0.0243981, 0.150871, 1.38721, -0.0243981, 0.150871, 1.38721, 0.0224031, 0.290765, 1.48832, 0.022403, 0.290765, 1.48832, -0.0243981, 0.147169, 1.46702, -0.0574914, 0.196392, 1.55045, -0.0243981, 0.145635, 1.50007, -0.0243981, 0.147169, 1.46702, -0.0574914, 0.224033, 1.53226, -0.0574914, 0.196392, 1.55045, -0.0243981, 0.149338, 1.42027, 0.0554964, 0.290765, 1.48832, 0.022403, 0.150871, 1.38721, 0.0224031, 0.149338, 1.42027, 0.0554964, 0.263124, 1.50652, 0.0554963, 0.290765, 1.48832, 0.022403, 0.149338, 1.42027, 0.0554964, 0.0171774, 1.46099, 0.0554964, 0.147169, 1.46702, 0.0554963, 0.149338, 1.42027, 0.0554964, 0.0193463, 1.41424, 0.0554964, 0.0171774, 1.46099, 0.0554964, -0.170962, 1.06989, 0.155063, -0.129324, 0.957901, 0.0930493, -0.128115, 1.00465, 0.0912487, -0.170962, 1.06989, 0.155063, -0.192752, 1.04416, 0.187517, -0.129324, 0.957901, 0.0930493, -0.204884, 1.5904, 0.205586, -0.16941, 1.62713, 0.180845, -0.188267, 1.62713, 0.20893, -0.204884, 1.5904, 0.205586, -0.178796, 1.5904, 0.16673, -0.16941, 1.62713, 0.180845, -0.14183, 1.02596, 0.255001, -0.195857, 1.13781, 0.251508, -0.157002, 1.13781, 0.277596, -0.14183, 1.02596, 0.255001, -0.180685, 1.02596, 0.228912, -0.195857, 1.13781, 0.251508, -0.195857, 1.5904, 0.251508, -0.188267, 1.62713, 0.20893, -0.181742, 1.62713, 0.242122, -0.195857, 1.5904, 0.251508, -0.204884, 1.5904, 0.205586, -0.188267, 1.62713, 0.20893, -0.0989474, 1.04416, 0.2505, -0.157002, 1.13781, 0.277596, -0.11108, 1.13781, 0.268569, -0.0989474, 1.04416, 0.2505, -0.14183, 1.02596, 0.255001, -0.157002, 1.13781, 0.277596, -0.157002, 1.5904, 0.277596, -0.181742, 1.62713, 0.242122, -0.153658, 1.62713, 0.260979, -0.157002, 1.5904, 0.277596, -0.195857, 1.5904, 0.251508, -0.181742, 1.62713, 0.242122, -0.077157, 1.06989, 0.218046, -0.11108, 1.13781, 0.268569, -0.0849909, 1.13781, 0.229714, -0.077157, 1.06989, 0.218046, -0.0989474, 1.04416, 0.2505, -0.11108, 1.13781, 0.268569, -0.11108, 1.5904, 0.268569, -0.153658, 1.62713, 0.260979, -0.120465, 1.62713, 0.254454, -0.11108, 1.5904, 0.268569, -0.157002, 1.5904, 0.277596, -0.153658, 1.62713, 0.260979, -0.0892237, 1.08809, 0.176651, -0.0849909, 1.13781, 0.229714, -0.0940182, 1.13781, 0.183792, -0.0892237, 1.08809, 0.176651, -0.077157, 1.06989, 0.218046, -0.0849909, 1.13781, 0.229714, -0.0849909, 1.5904, 0.229714, -0.120465, 1.62713, 0.254454, -0.101608, 1.62713, 0.22637, -0.0849909, 1.5904, 0.229714, -0.11108, 1.5904, 0.268569, -0.120465, 1.62713, 0.254454, -0.128079, 1.08809, 0.150562, -0.0940182, 1.13781, 0.183792, -0.132873, 1.13781, 0.157703, -0.128079, 1.08809, 0.150562, -0.0892237, 1.08809, 0.176651, -0.0940182, 1.13781, 0.183792, -0.0940183, 1.5904, 0.183792, -0.101608, 1.62713, 0.22637, -0.108133, 1.62713, 0.193177, -0.0940183, 1.5904, 0.183792, -0.0849909, 1.5904, 0.229714, -0.101608, 1.62713, 0.22637, -0.170962, 1.06989, 0.155063, -0.132873, 1.13781, 0.157703, -0.178796, 1.13781, 0.16673, -0.170962, 1.06989, 0.155063, -0.128079, 1.08809, 0.150562, -0.132873, 1.13781, 0.157703, -0.132873, 1.5904, 0.157703, -0.108133, 1.62713, 0.193177, -0.136218, 1.62713, 0.17432, -0.132873, 1.5904, 0.157703, -0.0940183, 1.5904, 0.183792, -0.108133, 1.62713, 0.193177, -0.144938, 1.64234, 0.21765, -0.188267, 1.62713, 0.20893, -0.16941, 1.62713, 0.180845, -0.108133, 1.62713, 0.193177, -0.144938, 1.64234, 0.21765, -0.136218, 1.62713, 0.17432, -0.181742, 1.62713, 0.242122, -0.188267, 1.62713, 0.20893, -0.144938, 1.64234, 0.21765, -0.181742, 1.62713, 0.242122, -0.144938, 1.64234, 0.21765, -0.153658, 1.62713, 0.260979, -0.153658, 1.62713, 0.260979, -0.144938, 1.64234, 0.21765, -0.120465, 1.62713, 0.254454, -0.120465, 1.62713, 0.254454, -0.144938, 1.64234, 0.21765, -0.101608, 1.62713, 0.22637, -0.101608, 1.62713, 0.22637, -0.144938, 1.64234, 0.21765, -0.108133, 1.62713, 0.193177, -0.136218, 1.62713, 0.17432, -0.144938, 1.64234, 0.21765, -0.16941, 1.62713, 0.180845, -0.180685, 1.02596, 0.228912, -0.204884, 1.13781, 0.205586, -0.195857, 1.13781, 0.251508, -0.180685, 1.02596, 0.228912, -0.192752, 1.04416, 0.187517, -0.204884, 1.13781, 0.205586, -0.030242, 0.918813, 0.00484838, -0.129324, 0.957901, 0.0930493, -0.102704, 0.924843, 0.11277, -0.030242, 0.918813, 0.00484838, -0.0568619, 0.95187, -0.0148722, -0.129324, 0.957901, 0.0930493, -0.0556529, 0.998621, -0.0166728, -0.0997852, 1.03771, 0.108423, -0.128115, 1.00465, 0.0912487, -0.0556529, 0.998621, -0.0166728, -0.0273233, 1.03168, 0.000501333, -0.0997852, 1.03771, 0.108423, -0.0273233, 1.03168, 0.000501333, -0.06093, 1.03771, 0.134512, -0.0997852, 1.03771, 0.108423, -0.0273233, 1.03168, 0.000501333, 0.011532, 1.03168, 0.02659, -0.06093, 1.03771, 0.134512, 0.011532, 1.03168, 0.02659, -0.0343101, 1.00465, 0.154232, -0.06093, 1.03771, 0.134512, 0.011532, 1.03168, 0.02659, 0.0381518, 0.998621, 0.0463106, -0.0343101, 1.00465, 0.154232, 0.0381518, 0.998621, 0.0463106, -0.0355191, 0.957901, 0.156033, -0.0343101, 1.00465, 0.154232, 0.0381518, 0.998621, 0.0463106, 0.0369428, 0.95187, 0.0481112, -0.0355191, 0.957901, 0.156033, 0.0369428, 0.95187, 0.0481112, -0.0638487, 0.924843, 0.138859, -0.0355191, 0.957901, 0.156033, 0.0369428, 0.95187, 0.0481112, 0.00861319, 0.918813, 0.030937, -0.0638487, 0.924843, 0.138859, 0.00861319, 0.918813, 0.030937, -0.102704, 0.924843, 0.11277, -0.0638487, 0.924843, 0.138859, 0.00861319, 0.918813, 0.030937, -0.030242, 0.918813, 0.00484838, -0.102704, 0.924843, 0.11277, -0.178796, 1.5904, 0.16673, -0.204884, 1.13781, 0.205586, -0.178796, 1.13781, 0.16673, -0.178796, 1.5904, 0.16673, -0.204884, 1.5904, 0.205586, -0.204884, 1.13781, 0.205586, -0.204884, 1.13781, 0.205586, -0.170962, 1.06989, 0.155063, -0.178796, 1.13781, 0.16673, -0.204884, 1.13781, 0.205586, -0.192752, 1.04416, 0.187517, -0.170962, 1.06989, 0.155063, -0.157002, 1.13781, 0.277596, -0.11108, 1.5904, 0.268569, -0.11108, 1.13781, 0.268569, -0.157002, 1.13781, 0.277596, -0.157002, 1.5904, 0.277596, -0.11108, 1.5904, 0.268569, -0.0849909, 1.13781, 0.229714, -0.0940183, 1.5904, 0.183792, -0.0940182, 1.13781, 0.183792, -0.0849909, 1.13781, 0.229714, -0.0849909, 1.5904, 0.229714, -0.0940183, 1.5904, 0.183792, -0.0940182, 1.13781, 0.183792, -0.132873, 1.5904, 0.157703, -0.132873, 1.13781, 0.157703, -0.0940182, 1.13781, 0.183792, -0.0940183, 1.5904, 0.183792, -0.132873, 1.5904, 0.157703, -0.132873, 1.13781, 0.157703, -0.178796, 1.5904, 0.16673, -0.178796, 1.13781, 0.16673, -0.132873, 1.13781, 0.157703, -0.132873, 1.5904, 0.157703, -0.178796, 1.5904, 0.16673, -0.195857, 1.13781, 0.251508, -0.157002, 1.5904, 0.277596, -0.157002, 1.13781, 0.277596, -0.195857, 1.13781, 0.251508, -0.195857, 1.5904, 0.251508, -0.157002, 1.5904, 0.277596, -0.11108, 1.13781, 0.268569, -0.0849909, 1.5904, 0.229714, -0.0849909, 1.13781, 0.229714, -0.11108, 1.13781, 0.268569, -0.11108, 1.5904, 0.268569, -0.0849909, 1.5904, 0.229714, -0.204884, 1.13781, 0.205586, -0.195857, 1.5904, 0.251508, -0.195857, 1.13781, 0.251508, -0.204884, 1.13781, 0.205586, -0.204884, 1.5904, 0.205586, -0.195857, 1.5904, 0.251508, -0.0355191, 0.957901, 0.156033, -0.077157, 1.06989, 0.218046, -0.0343101, 1.00465, 0.154232, -0.0355191, 0.957901, 0.156033, -0.0989474, 1.04416, 0.2505, -0.077157, 1.06989, 0.218046, -0.0997852, 1.03771, 0.108423, -0.170962, 1.06989, 0.155063, -0.128115, 1.00465, 0.0912487, -0.0997852, 1.03771, 0.108423, -0.128079, 1.08809, 0.150562, -0.170962, 1.06989, 0.155063, -0.0638487, 0.924843, 0.138859, -0.0989474, 1.04416, 0.2505, -0.0355191, 0.957901, 0.156033, -0.0638487, 0.924843, 0.138859, -0.14183, 1.02596, 0.255001, -0.0989474, 1.04416, 0.2505, -0.06093, 1.03771, 0.134512, -0.128079, 1.08809, 0.150562, -0.0997852, 1.03771, 0.108423, -0.06093, 1.03771, 0.134512, -0.0892237, 1.08809, 0.176651, -0.128079, 1.08809, 0.150562, -0.102704, 0.924843, 0.11277, -0.14183, 1.02596, 0.255001, -0.0638487, 0.924843, 0.138859, -0.102704, 0.924843, 0.11277, -0.180685, 1.02596, 0.228912, -0.14183, 1.02596, 0.255001, -0.0343101, 1.00465, 0.154232, -0.0892237, 1.08809, 0.176651, -0.06093, 1.03771, 0.134512, -0.0343101, 1.00465, 0.154232, -0.077157, 1.06989, 0.218046, -0.0892237, 1.08809, 0.176651, -0.129324, 0.957901, 0.0930493, -0.180685, 1.02596, 0.228912, -0.102704, 0.924843, 0.11277, -0.129324, 0.957901, 0.0930493, -0.192752, 1.04416, 0.187517, -0.180685, 1.02596, 0.228912, -0.129324, 0.957901, 0.0930493, -0.0556529, 0.998621, -0.0166728, -0.128115, 1.00465, 0.0912487, -0.129324, 0.957901, 0.0930493, -0.0568619, 0.95187, -0.0148722, -0.0556529, 0.998621, -0.0166728, 0.0424324, 1.84657, -0.102441, 0.0740441, 1.91317, -0.03067, 0.03067, 1.91317, -0.0740441, 0.0424324, 1.84657, -0.102441, 0.102441, 1.84657, -0.0424324, 0.0740441, 1.91317, -0.03067)

[node name="Cactus_Tall_3" type="MeshInstance3D"]
mesh = SubResource("ArrayMesh_xeviw")
skeleton = NodePath("")

[node name="Cactus_Tall_3" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="Cactus_Tall_3"]
shape = SubResource("ConcavePolygonShape3D_ani52")
