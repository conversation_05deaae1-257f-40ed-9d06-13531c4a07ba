extends Node3D
# Movement targets
var target_position: Vector3
var target_camera_rotation_x: float
var target_platform_rotation_y: float

# Store the initial state
var initial_camera_position: Vector3
var initial_camera_rotation: Vector3
var initial_platform_rotation: Vector3

# Movement properties
var is_moving: bool = false
var speed: float = 4.0
var rotation_speed: float = 4.0
@onready var platform: StaticBody3D = %platform
@onready var camera_3d: Camera3D = %Camera3D
@onready var victory_ui: Control = %Victory
@onready var search_ui: Control = %Search

@onready var win_text: Label = $UI/Victory/Panel/win_text
@onready var lose_text: Label = $UI/Victory/Panel/lose_text
@onready var coin_win: Label = $UI/Victory/Panel/coin_win
@onready var coin_lose: Label = $UI/Victory/Panel/coin_lose
@onready var cup_win: Label = $UI/Victory/Panel/cup_win
@onready var cup_lose: Label = $UI/Victory/Panel/cup_lose
@onready var game_over_card: TextureRect = $UI/Victory/Panel/GameOverCard

var victory_card = preload("res://assets/new/victory_card.png")
var defeat_card = preload("res://assets/new/defeat_card.png")



func _ready() -> void:
	if Global.global_player_id == Global.winner_id:
		game_over_card.texture = victory_card
		#title
		win_text.show()
		lose_text.hide()
		#cup and coin
		coin_win.show()
		cup_win.show()
		victory()
	else:
		game_over_card.texture = defeat_card
		win_text.hide()
		lose_text.show()
		coin_lose.show()
		cup_lose.show()
		lose()


func _process(delta: float) -> void:
	if is_moving:
		# Smoothly move camera to the target position
		camera_3d.position = camera_3d.position.lerp(target_position, speed * delta)
		
		# Smoothly rotate camera on its X-axis
		var current_cam_rot_x = camera_3d.rotation_degrees.x
		camera_3d.rotation_degrees.x = lerp(current_cam_rot_x, target_camera_rotation_x, rotation_speed * delta)

		# Smoothly rotate platform on its Y-axis
		var current_platform_rot_y = platform.rotation_degrees.y
		platform.rotation_degrees.y = lerp(current_platform_rot_y, target_platform_rotation_y, rotation_speed * delta)
		
		# Check if movement is complete
		if camera_3d.position.distance_to(target_position) < 0.01 and \
		   abs(camera_3d.rotation_degrees.x - target_camera_rotation_x) < 0.1 and \
		   abs(platform.rotation_degrees.y - target_platform_rotation_y) < 0.1:
			
			# Snap to final positions to ensure accuracy
			camera_3d.position = target_position
			camera_3d.rotation_degrees.x = target_camera_rotation_x
			platform.rotation_degrees.y = target_platform_rotation_y
			is_moving = false
	

func victory() -> void:
	%Model.play_victory_anim()
	
func lose() -> void:
	%Model.play_lose_anim()
	


func _on_play_again_pressed() -> void:
	%Model.go_idle()
	victory_ui.hide()
	search_ui.show()
	target_position = Vector3(-15, 10.9, 18) + Vector3(%platform.position.x,0,0)
	target_camera_rotation_x = -11.8
	target_platform_rotation_y = 0 # Or any other rotation you want for this view
	is_moving = true
	Tcpserver.send_gnsearch(1)
