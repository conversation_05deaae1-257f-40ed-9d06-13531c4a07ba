[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://cd5h3i40j2wsd"
path.s3tc="res://.godot/imported/cactus_body_normal.webp-dd5b9cb5e5ec3bfbf225313eea3b2cb4.s3tc.ctex"
path.etc2="res://.godot/imported/cactus_body_normal.webp-dd5b9cb5e5ec3bfbf225313eea3b2cb4.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}

[deps]

source_file="res://scenes/bomb_tag/level/cactus/materials/cactus_body_normal.webp"
dest_files=["res://.godot/imported/cactus_body_normal.webp-dd5b9cb5e5ec3bfbf225313eea3b2cb4.s3tc.ctex", "res://.godot/imported/cactus_body_normal.webp-dd5b9cb5e5ec3bfbf225313eea3b2cb4.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=1
roughness/src_normal="res://level/cactus/textures/plants_2/Plants_2_Normal.webp"
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
