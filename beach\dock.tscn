[gd_scene load_steps=9 format=4 uid="uid://xqpck7mfgwsw"]

[ext_resource type="PackedScene" uid="uid://cg8ket2rvivv3" path="res://beach/Dock.glb" id="1_cjfj6"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cjfj6"]
resource_name = "Wood"
albedo_color = Color(0.533319, 0.416, 0.257181, 1)
metallic_specular = 0.15
roughness = 0.3
metadata/extras = {
"fromFBX": {
"isTruePBR": false,
"shadingModel": "Phong"
}
}

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_dsh3e"]
resource_name = "Wood_Light"
albedo_color = Color(0.646657, 0.530323, 0.344881, 1)
metallic = 0.4
roughness = 0.272166
metadata/extras = {
"fromFBX": {
"isTruePBR": false,
"shadingModel": "Phong"
}
}

[sub_resource type="ArrayMesh" id="ArrayMesh_jw8m7"]
_surfaces = [{
"aabb": AABB(-0.00261176, -0.00609398, -0.00353902, 0.00524322, 0.012188, 0.00465754),
"format": 34896613377,
"index_count": 1056,
"index_data": PackedByteArray("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"),
"lods": [0.*********, PackedByteArray("AAABAAIAAwACAAEAAgAEAAAAAQAFAAMABgAAAAQABwADAAUABQAGAAcABAAHAAYACAAJAAoACwAKAAkACgAMAAgACQANAAsADgAIAAwADwALAA0ADQAOAA8ADAAPAA4AEAARABIAEwASABEAEgAUABAAEQAVABMAFgAQABQAFwATABUAFQAWABcAFAAXABYAGAAZABoAGwAaABkAGgAcABgAGQAdABsAHgAYABwAHwAbAB0AHQAeAB8AHAAfAB4AIAAhACIAIwAgACIAJAAiACEAIwAiACUAJgAkACEAJgAjACUAJgAhACcAJgAnACMAKAApACoAKAArACkAKAAqACwAKAAtACsAKgAuACwALQAvACsALwAsAC4ALwAuACsAMAAxADIAMAAzADEAMAAyADQAMAA1ADMAMgA2ADQANQA3ADMANwA0ADYANwA2ADMAOAA5ADoAOQA7ADoAOAA8ADkAPQA6ADsAOAA+ADwAPQA7AD4AOAA/AD4APwA9AD4AQABBAEIAQABDAEEAQABCAEQAQABFAEMAQgBGAEQARQBHAEMARwBEAEYARwBGAEMASABJAEoASABLAEkATABIAEoASwBNAEkATABKAE4ASwBPAE0ATwBMAE4ATwBOAE0AUABRAFIAUABTAFEAVABQAFIAUwBVAFEAVABSAFYAUwBXAFUAVwBUAFYAVwBWAFUAWABZAFoAWABbAFkAXABYAFoAWwBdAFkAXABaAF4AWwBfAF0AXwBcAF4AXwBeAF0AYABhAGIAYABjAGEAZABgAGIAYwBlAGEAZABiAGYAYwBnAGUAZwBkAGYAZwBmAGUAaABpAGoAagBrAGgAbABqAGkAawBtAGgAbABpAG4AbwBoAG0AbwBtAGwAbgBvAGwAcABxAHIAcgBzAHAAdAByAHEAcwB1AHAAdABxAHYAdwBwAHUAdwB1AHQAdgB3AHQAeAB5AHoAeQB7AHoAfAB4AHoAeQB9AHsAeAB8AH4AfQB+AHwAfAB/AH0AfwB7AH0AgACBAIIAggCDAIAAhACCAIEAgwCFAIAAhACBAIYAhwCAAIUAhwCFAIQAhgCHAIQA")],
"name": "Wood",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 244,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.00261176, -0.00609398, 0.*********, 0.00524322, 0.012188, 0.*********),
"format": 34896613377,
"index_count": 108,
"index_data": PackedByteArray("AAABAAIAAwABAAAABAAFAAYABwAFAAQACAAJAAoACwAJAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAZABoAGgAbABgAHAAdAB4AHgAfABwAIAAhACIAIgAjACAAJAAlACYAJAAmACcAKAApACoAKAAqACsALAAtAC4ALAAuAC8AMAAxADIAMgAzADAANAA1ADYANAA2ADcAOAA5ADoAOAA6ADsAPAA9AD4APAA+AD8AQABBAEIAQABDAEEARABFAEYARABGAEcA"),
"name": "Wood_Light",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 72,
"vertex_data": PackedByteArray("AABbsf//AAB3FE6m//8AAAAATqb//wAAdxRbsf//AAAAAP7///8AAHcU8vT//wAAAADy9P//AAB3FP7///8AAIfrW7H//wAA//9Opv//AACH606m//8AAP//W7H//wAAh+vy9P//AACH6/////8AAP///v///wAA///y9P//AAAwwa/+AAAAAO/sr/7gAQAAGeyv/gKDAABawK/+IoEAAGCUr/4AAAAAH8Cv/uABAABJv6/+AoMAAIqTr/4igQAArGmv/gAGAAASlq/+gQYAAFGVr/5tkQAA62iv/uyQAAATQ6/+BQQAAElqr/4vCAAAd2mv/kSJAABAQq/+GoUAAKERr/4AAAAAYD2v/uABAACKPK/+AoMAAMsQr/4igQAAdxSwWf//AAB3FKNO//8AAAAAo07//wAAAACwWf//AAB3FAwL//8AAHcUAAD//wAAAAAAAP//AAAAAAwL//8AAP//sFn//wAA//+jTv//AACH66NO//8AAIfrsFn//wAA//8AAP//AACH6wAA//8AAIfrDAv//wAA//8MC///AAAZ7E8BAoMAAO/sTwHgAQAAMMFPAQAAAABawE8BIoEAAEm/TwECgwAAH8BPAeABAABglE8BAAAAAIqTTwEigQAAUZVPAW2RAAASlk8BgQYAAKxpTwEABgAA62hPAeyQAAB3aU8BRIkAABNDTwEFBAAAQEJPARqFAABJak8BLwgAAIo8TwECgwAAYD1PAeABAAChEU8BAAAAAMsQTwEigQAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_rd6gc"]
resource_name = "Root Scene_Dock_FirstAge"
_surfaces = [{
"aabb": AABB(-0.00261176, -0.00609398, -0.00353902, 0.00524322, 0.012188, 0.00465754),
"format": ***********,
"index_count": 1056,
"index_data": PackedByteArray("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"),
"lods": [0.*********, PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAZABoAGgAbABgAHAAdAB4AHgAfABwAIAAhACIAIgAjACAAJAAlACYAJgAnACQAKAApACoAKgArACgALAAtAC4ALgAvACwAMAAxADIAMgAzADAANAA1ADYANgA3ADQAOAA5ADoAOgA7ADgAPAA9AD4APgA/ADwAQABBAEIAQwBCAEEARABFAEYARABGAEcASABJAEoASABKAEsATABNAE4ATQBPAE4AUABRAFIAUABTAFEAVABVAFYAVwBWAFUAWABZAFoAWABaAFsAXABdAF4AXQBfAF4AYABhAGIAYABjAGEAZABlAGYAZwBmAGUAaABpAGoAaABrAGkAbABtAG4AbQBvAG4AcABxAHIAcABzAHEAdAB1AHYAdwB2AHUAeAB5AHoAeAB6AHsAfAB9AH4AfQB/AH4AgACBAIIAgACDAIEAhACFAIYAhwCGAIUAiACJAIoAiwCIAIoAjACNAI4AjwCMAI4AkACRAJIAkwCQAJIAlACVAJYAlwCUAJYAmACZAJoAmwCYAJoAnACdAJ4AnwCcAJ4AoAChAKIAowCgAKIApAClAKYApwCkAKYAqACpAKoAqwCoAKoArACtAK4ArwCsAK4AsACxALIAswCwALIAtAC1ALYAtwC0ALYAuAC5ALoAuwC4ALoAvAC9AL4AvwC8AL4AwADBAMIAwwDAAMIAxADFAMYAxwDEAMYAyADJAMoAygDLAMgAzADNAM4AzwDMAM4A0ADRANIA0wDSANEA1ADVANYA1ADWANcA2ADZANoA2gDbANgA3ADdAN4A3wDcAN4A4ADhAOIA4wDiAOEA5ADlAOYA5ADmAOcA6ADpAOoA6QDrAOoA7ADtAO4A7gDvAOwA8ADxAPIA8wDyAPEA9AD1APYA9QD0APcA+AD5APoA+AD7APkA/AD9AP4A/AD/AP0AAAEBAQIBAgEDAQABBAEFAQYBBwEEAQYBCAEJAQoBCwEKAQkBDAENAQ4BDAEPAQ0B")],
"material": SubResource("StandardMaterial3D_cjfj6"),
"name": "Wood",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 488,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.00261176, -0.00609398, 0.*********, 0.00524322, 0.012188, 0.*********),
"format": ***********,
"index_count": 108,
"index_data": PackedByteArray("AAABAAIAAwABAAAABAAFAAYABwAFAAQACAAJAAoACwAJAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFgAXABQAGAAZABoAGgAbABgAHAAdAB4AHgAfABwAIAAhACIAIgAjACAAJAAlACYAJAAmACcAKAApACoAKAAqACsALAAtAC4ALAAuAC8AMAAxADIAMgAzADAANAA1ADYANAA2ADcAOAA5ADoAOAA6ADsAPAA9AD4APAA+AD8AQABBAEIAQABDAEEARABFAEYARABGAEcA"),
"material": SubResource("StandardMaterial3D_dsh3e"),
"name": "Wood_Light",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 72,
"vertex_data": PackedByteArray("AABbsf///793FE6m////vwAATqb///+/dxRbsf///78AAP7/////v3cU8vT///+/AADy9P///793FP7/////v4frW7H///+///9Opv///7+H606m////v///W7H///+/h+vy9P///7+H6///////v////v////+////y9P///78wwa/+AAD//+/sr/7gAf//Geyv/gKD//9awK/+IoH//2CUr/4AAP//H8Cv/uAB//9Jv6/+AoP//4qTr/4igf//rGmv/gAG//8Slq/+gQb//1GVr/5tkf//62iv/uyQ//8TQ6/+BQT//0lqr/4vCP//d2mv/kSJ//9AQq/+GoX//6ERr/4AAP//YD2v/uAB//+KPK/+AoP//8sQr/4igf//dxSwWf///793FKNO////vwAAo07///+/AACwWf///793FAwL////v3cUAAD///+/AAAAAP///78AAAwL////v///sFn///+///+jTv///7+H66NO////v4frsFn///+///8AAP///7+H6wAA////v4frDAv///+///8MC////78Z7E8BAoP//+/sTwHgAf//MMFPAQAA//9awE8BIoH//0m/TwECg///H8BPAeAB//9glE8BAAD//4qTTwEigf//UZVPAW2R//8Slk8BgQb//6xpTwEABv//62hPAeyQ//93aU8BRIn//xNDTwEFBP//QEJPARqF//9Jak8BLwj//4o8TwECg///YD1PAeAB//+hEU8BAAD//8sQTwEigf///3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//7//f/+//3//v/9//7//f/+//3//v/9//7//f/+//3//v/9//7//f/+//3//v/9//7//f/+//3//v/9//7//f/+//3//v/9//7//f/+//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//3//f/9//z//f/8//3//P/9//z//f/8//3//P/9//z//f/8//3//P/9//z//f/8//3//P/9//z//f/8//3//P/9//z//f/8//3//P/9//z//f/8/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_jw8m7")

[sub_resource type="BoxShape3D" id="BoxShape3D_cjfj6"]
size = Vector3(0.46875, 0.0446777, 1.20483)

[sub_resource type="BoxShape3D" id="BoxShape3D_rd6gc"]
size = Vector3(0.0478516, 0.477295, 0.0629883)

[sub_resource type="BoxShape3D" id="BoxShape3D_3gitc"]
size = Vector3(0.0192871, 0.0432129, 0.326172)

[node name="Dock" instance=ExtResource("1_cjfj6")]
transform = Transform3D(4, 0, 0, 0, 4, 0, 0, 0, 4, 0, 0, 0)

[node name="Dock_FirstAge" parent="RootNode" index="0"]
mesh = SubResource("ArrayMesh_rd6gc")

[node name="CollisionShape3D" type="CollisionShape3D" parent="." index="1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.0592835, -0.00109863)
shape = SubResource("BoxShape3D_cjfj6")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="." index="2"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.242694, -0.122925, -0.583165)
shape = SubResource("BoxShape3D_rd6gc")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="." index="3"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.242694, -0.122925, -0.208165)
shape = SubResource("BoxShape3D_rd6gc")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="." index="4"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.242694, -0.122925, 0.209516)
shape = SubResource("BoxShape3D_rd6gc")

[node name="CollisionShape3D5" type="CollisionShape3D" parent="." index="5"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.242694, -0.122925, 0.584516)
shape = SubResource("BoxShape3D_rd6gc")

[node name="CollisionShape3D6" type="CollisionShape3D" parent="." index="6"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.239625, -0.122925, 0.584516)
shape = SubResource("BoxShape3D_rd6gc")

[node name="CollisionShape3D7" type="CollisionShape3D" parent="." index="7"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.239625, -0.122925, 0.209516)
shape = SubResource("BoxShape3D_rd6gc")

[node name="CollisionShape3D8" type="CollisionShape3D" parent="." index="8"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.239625, -0.122925, -0.215484)
shape = SubResource("BoxShape3D_rd6gc")

[node name="CollisionShape3D9" type="CollisionShape3D" parent="." index="9"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.239625, -0.122925, -0.590484)
shape = SubResource("BoxShape3D_rd6gc")

[node name="CollisionShape3D10" type="CollisionShape3D" parent="." index="10"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.240559, -0.0530926, -0.396483)
shape = SubResource("BoxShape3D_3gitc")

[node name="CollisionShape3D11" type="CollisionShape3D" parent="." index="11"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.240559, -0.0530926, -0.00121081)
shape = SubResource("BoxShape3D_3gitc")

[node name="CollisionShape3D12" type="CollisionShape3D" parent="." index="12"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.240559, -0.0530926, 0.407104)
shape = SubResource("BoxShape3D_3gitc")

[node name="CollisionShape3D13" type="CollisionShape3D" parent="." index="13"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.237503, -0.0530926, -0.396483)
shape = SubResource("BoxShape3D_3gitc")

[node name="CollisionShape3D14" type="CollisionShape3D" parent="." index="14"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.237503, -0.0530926, -0.00121081)
shape = SubResource("BoxShape3D_3gitc")

[node name="CollisionShape3D15" type="CollisionShape3D" parent="." index="15"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.237503, -0.0530926, 0.407104)
shape = SubResource("BoxShape3D_3gitc")
