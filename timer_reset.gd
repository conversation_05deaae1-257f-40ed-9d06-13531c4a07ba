extends Area3D

@export var rotation_speed: float = 70.0
@export var power_up_sfx: AudioStream

func _on_body_entered(body: Node3D) -> void:
	if body is Player:
		var sfx_player = AudioStreamPlayer3D.new()
		sfx_player.stream = power_up_sfx
		sfx_player.finished.connect(sfx_player.queue_free)
		get_tree().root.add_child(sfx_player)
		sfx_player.global_position = global_position
		sfx_player.play()
		body.vfx_power_up.play_anim()
		get_node("/root/Game").increase_bomb_timer.rpc()
		queue_free()

func _process(delta: float) -> void:
	rotation_degrees.y += rotation_speed * delta
