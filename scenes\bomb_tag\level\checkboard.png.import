[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://brbtohct73mlt"
path="res://.godot/imported/checkboard.png-577c0b38e31a66086e989b38559c5292.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://scenes/bomb_tag/level/checkboard.png"
dest_files=["res://.godot/imported/checkboard.png-577c0b38e31a66086e989b38559c5292.ctex"]

[params]

compress/mode=3
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
