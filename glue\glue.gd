extends Area3D


# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	pass # Replace with function body.


# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass


func _on_body_entered(body: Node3D) -> void:
	if body is Player:
		#body.vfx_power_up.play_anim()
		body.speed = 4
		#body.start_speed_timer.rpc()
		#queue_free()


func _on_body_exited(body: Node3D) -> void:
	if body is Player:
		#body.vfx_power_up.play_anim()
		body.speed = 10
		#body.start_speed_timer.rpc()
		#queue_free()
