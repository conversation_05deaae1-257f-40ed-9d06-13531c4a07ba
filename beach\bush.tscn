[gd_scene load_steps=3 format=3 uid="uid://ck6saj0aumi6d"]

[ext_resource type="PackedScene" uid="uid://bypclqujj86by" path="res://beach/Bush.glb" id="1_jr34d"]

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_8rhmd"]
data = PackedVector3Array(0.0012, 0.0048, -0.0021, 0, 0.0054, 0, -0.0012, 0.0048, -0.0021, -0.0012, 0.0048, -0.0021, 0, 0.0046, -0.0028, 0.0012, 0.0048, -0.0021, 0, 0.0033, -0.0039, 0.0012, 0.0048, -0.0021, 0, 0.0046, -0.0028, 0, 0.0033, -0.0039, 0, 0.0046, -0.0028, -0.0012, 0.0048, -0.0021, 0.0018, 0.0037, -0.0031, 0.0012, 0.0048, -0.0021, 0, 0.0033, -0.0039, -0.0012, 0.0048, -0.0021, -0.0018, 0.0037, -0.0031, 0, 0.0033, -0.0039, 0.0012, 0.0048, -0.0021, 0.0018, 0.0037, -0.0031, 0.0024, 0.0046, -0.0014, 0.0012, 0.0048, -0.0021, 0.0024, 0.0046, -0.0014, 0.0024, 0.0048, 0, 0.0024, 0.0048, 0, 0, 0.0054, 0, 0.0012, 0.0048, -0.0021, 0.0036, 0.0037, 0, 0.0024, 0.0048, 0, 0.0024, 0.0046, -0.0014, 0.0012, 0.0048, 0.0021, 0, 0.0054, 0, 0.0024, 0.0048, 0, 0.0036, 0.0037, 0, 0.0024, 0.0046, 0.0014, 0.0024, 0.0048, 0, 0.0024, 0.0048, 0, 0.0024, 0.0046, 0.0014, 0.0012, 0.0048, 0.0021, -0.0012, 0.0048, -0.0021, 0, 0.0054, 0, -0.0024, 0.0048, 0, 0.0034, 0.0033, -0.002, 0.0024, 0.0046, -0.0014, 0.0018, 0.0037, -0.0031, 0.0024, 0.0046, -0.0014, 0.0034, 0.0033, -0.002, 0.0036, 0.0037, 0, 0.0033, 0.0026, -0.0034, 0.0034, 0.0033, -0.002, 0.0018, 0.0037, -0.0031, -0.0024, 0.0048, 0, 0, 0.0054, 0, -0.0012, 0.0048, 0.0021, -0.0012, 0.0048, 0.0021, 0, 0.0054, 0, 0.0012, 0.0048, 0.0021, -0.0024, 0.0048, 0, -0.0024, 0.0046, -0.0014, -0.0012, 0.0048, -0.0021, -0.0018, 0.0037, -0.0031, -0.0012, 0.0048, -0.0021, -0.0024, 0.0046, -0.0014, -0.0024, 0.0048, 0, -0.0036, 0.0037, 0, -0.0024, 0.0046, -0.0014, 0.0018, 0.0037, -0.0031, 0.0024, 0.0029, -0.0041, 0.0033, 0.0026, -0.0034, -0.0024, 0.0046, 0.0014, -0.0036, 0.0037, 0, -0.0024, 0.0048, 0, -0.0012, 0.0048, 0.0021, -0.0024, 0.0046, 0.0014, -0.0024, 0.0048, 0, 0.0013, 0.0026, -0.0046, 0.0024, 0.0029, -0.0041, 0.0018, 0.0037, -0.0031, 0.0013, 0.0026, -0.0046, 0.0018, 0.0037, -0.0031, 0, 0.0033, -0.0039, 0.0025, 0.0016, -0.0043, 0.0033, 0.0026, -0.0034, 0.0024, 0.0029, -0.0041, 0.0025, 0.0016, -0.0043, 0.0024, 0.0029, -0.0041, 0.0013, 0.0026, -0.0046, -0.0024, 0.0046, 0.0014, -0.0012, 0.0048, 0.0021, -0.0018, 0.0037, 0.0031, 0.0036, 0.0015, -0.0033, 0.0033, 0.0026, -0.0034, 0.0025, 0.0016, -0.0043, 0.0031, 0, -0.0031, 0.0036, 0.0015, -0.0033, 0.0025, 0.0016, -0.0043, 0.0037, 0.0015, -0.0022, 0.0033, 0.0026, -0.0034, 0.0036, 0.0015, -0.0033, 0.0037, 0.0015, -0.0022, 0.0036, 0.0015, -0.0033, 0.0031, 0, -0.0031, 0.0031, 0, -0.0031, 0.0026, 0, -0.0015, 0.0037, 0.0015, -0.0022, 0.0025, 0.0016, -0.0043, 0.0022, 0, -0.0038, 0.0031, 0, -0.0031, 0.0034, 0.0033, -0.002, 0.0033, 0.0026, -0.0034, 0.0037, 0.0015, -0.0022, 0.0022, 0, -0.0038, 0.0025, 0.0016, -0.0043, 0.001, 0.0015, -0.0048, 0.0013, 0.0026, -0.0046, 0.001, 0.0015, -0.0048, 0.0025, 0.0016, -0.0043, 0.001, 0.0015, -0.0048, 0.0012, 0, -0.0042, 0.0022, 0, -0.0038, 0, 0, -0.003, 0.0012, 0, -0.0042, 0.001, 0.0015, -0.0048, 0.001, 0.0015, -0.0048, 0, 0.0015, -0.0043, 0, 0, -0.003, 0, 0.0015, -0.0043, 0.001, 0.0015, -0.0048, 0.0013, 0.0026, -0.0046, 0, 0.0015, -0.0043, 0.0013, 0.0026, -0.0046, 0, 0.0033, -0.0039, 0.0046, 0.0026, -0.0012, 0.0034, 0.0033, -0.002, 0.0037, 0.0015, -0.0022, 0, 0.0033, -0.0039, -0.0013, 0.0026, -0.0046, 0, 0.0015, -0.0043, 0.0034, 0.0033, -0.002, 0.0046, 0.0026, -0.0012, 0.0036, 0.0037, 0, -0.0013, 0.0026, -0.0046, 0, 0.0033, -0.0039, -0.0018, 0.0037, -0.0031, 0.0037, 0.0015, -0.0022, 0.0046, 0.0015, -0.0015, 0.0046, 0.0026, -0.0012, 0.0037, 0.0015, -0.0022, 0.0042, 0, -0.0011, 0.0046, 0.0015, -0.0015, 0.0026, 0, -0.0015, 0.0042, 0, -0.0011, 0.0037, 0.0015, -0.0022, 0.005, 0.0016, 0, 0.0046, 0.0015, -0.0015, 0.0042, 0, -0.0011, 0.0046, 0.0026, -0.0012, 0.0046, 0.0015, -0.0015, 0.005, 0.0016, 0, 0.005, 0.0016, 0, 0.0042, 0, -0.0011, 0.0044, 0, 0, 0.0047, 0.0029, 0, 0.0036, 0.0037, 0, 0.0046, 0.0026, -0.0012, 0.005, 0.0016, 0, 0.0047, 0.0029, 0, 0.0046, 0.0026, -0.0012, 0.005, 0.0016, 0, 0.0044, 0, 0, 0.0042, 0, 0.0011, 0.0046, 0.0026, 0.0012, 0.0036, 0.0037, 0, 0.0047, 0.0029, 0, 0.005, 0.0016, 0, 0.0046, 0.0026, 0.0012, 0.0047, 0.0029, 0, 0.0046, 0.0026, 0.0012, 0.005, 0.0016, 0, 0.0046, 0.0015, 0.0015, 0.0042, 0, 0.0011, 0.0046, 0.0015, 0.0015, 0.005, 0.0016, 0, 0.0037, 0.0015, 0.0022, 0.0046, 0.0015, 0.0015, 0.0042, 0, 0.0011, 0.0046, 0.0026, 0.0012, 0.0046, 0.0015, 0.0015, 0.0037, 0.0015, 0.0022, 0.0042, 0, 0.0011, 0.0026, 0, 0.0015, 0.0037, 0.0015, 0.0022, 0.0034, 0.0033, 0.002, 0.0036, 0.0037, 0, 0.0046, 0.0026, 0.0012, 0.0046, 0.0026, 0.0012, 0.0037, 0.0015, 0.0022, 0.0034, 0.0033, 0.002, 0.0034, 0.0033, 0.002, 0.0024, 0.0046, 0.0014, 0.0036, 0.0037, 0, 0.0024, 0.0046, 0.0014, 0.0034, 0.0033, 0.002, 0.0018, 0.0037, 0.0031, 0.0024, 0.0046, 0.0014, 0.0018, 0.0037, 0.0031, 0.0012, 0.0048, 0.0021, 0.0033, 0.0026, 0.0034, 0.0034, 0.0033, 0.002, 0.0037, 0.0015, 0.0022, 0.0033, 0.0026, 0.0034, 0.0018, 0.0037, 0.0031, 0.0034, 0.0033, 0.002, 0.0037, 0.0015, 0.0022, 0.0036, 0.0015, 0.0033, 0.0033, 0.0026, 0.0034, 0.0031, 0, 0.0031, 0.0036, 0.0015, 0.0033, 0.0037, 0.0015, 0.0022, 0.0031, 0, 0.0031, 0.0037, 0.0015, 0.0022, 0.0026, 0, 0.0015, 0.0025, 0.0016, 0.0043, 0.0036, 0.0015, 0.0033, 0.0031, 0, 0.0031, 0.0033, 0.0026, 0.0034, 0.0036, 0.0015, 0.0033, 0.0025, 0.0016, 0.0043, 0.0031, 0, 0.0031, 0.0022, 0, 0.0038, 0.0025, 0.0016, 0.0043, 0.0033, 0.0026, 0.0034, 0.0024, 0.0029, 0.0041, 0.0018, 0.0037, 0.0031, 0.0025, 0.0016, 0.0043, 0.0024, 0.0029, 0.0041, 0.0033, 0.0026, 0.0034, 0.0018, 0.0037, 0.0031, 0.0024, 0.0029, 0.0041, 0.0013, 0.0026, 0.0046, 0.0013, 0.0026, 0.0046, 0.0024, 0.0029, 0.0041, 0.0025, 0.0016, 0.0043, 0.001, 0.0015, 0.0048, 0.0025, 0.0016, 0.0043, 0.0022, 0, 0.0038, 0.0025, 0.0016, 0.0043, 0.001, 0.0015, 0.0048, 0.0013, 0.0026, 0.0046, 0.0022, 0, 0.0038, 0.0012, 0, 0.0042, 0.001, 0.0015, 0.0048, 0.001, 0.0015, 0.0048, 0.0012, 0, 0.0042, 0, 0, 0.003, 0.001, 0.0015, 0.0048, 0, 0, 0.003, 0, 0.0015, 0.0043, 0.0013, 0.0026, 0.0046, 0.001, 0.0015, 0.0048, 0, 0.0015, 0.0043, 0.0018, 0.0037, 0.0031, 0.0013, 0.0026, 0.0046, 0, 0.0033, 0.0039, 0.0013, 0.0026, 0.0046, 0, 0.0015, 0.0043, 0, 0.0033, 0.0039, 0.0012, 0.0048, 0.0021, 0.0018, 0.0037, 0.0031, 0, 0.0033, 0.0039, 0, 0.0033, 0.0039, 0, 0.0046, 0.0028, 0.0012, 0.0048, 0.0021, 0.0012, 0.0048, 0.0021, 0, 0.0046, 0.0028, -0.0012, 0.0048, 0.0021, -0.0012, 0.0048, 0.0021, 0, 0.0046, 0.0028, 0, 0.0033, 0.0039, 0, 0.0033, 0.0039, -0.0018, 0.0037, 0.0031, -0.0012, 0.0048, 0.0021, 0, 0.0033, 0.0039, 0, 0.0015, 0.0043, -0.0013, 0.0026, 0.0046, 0, 0.0033, 0.0039, -0.0013, 0.0026, 0.0046, -0.0018, 0.0037, 0.0031, -0.0013, 0.0026, 0.0046, 0, 0.0015, 0.0043, -0.001, 0.0015, 0.0048, -0.001, 0.0015, 0.0048, 0, 0.0015, 0.0043, 0, 0, 0.003, 0, 0, 0.003, -0.0012, 0, 0.0042, -0.001, 0.0015, 0.0048, -0.001, 0.0015, 0.0048, -0.0012, 0, 0.0042, -0.0022, 0, 0.0038, -0.0022, 0, 0.0038, -0.0025, 0.0016, 0.0043, -0.001, 0.0015, 0.0048, -0.0013, 0.0026, 0.0046, -0.001, 0.0015, 0.0048, -0.0025, 0.0016, 0.0043, -0.0025, 0.0016, 0.0043, -0.0022, 0, 0.0038, -0.0031, 0, 0.0031, -0.0024, 0.0029, 0.0041, -0.0018, 0.0037, 0.0031, -0.0013, 0.0026, 0.0046, -0.0025, 0.0016, 0.0043, -0.0024, 0.0029, 0.0041, -0.0013, 0.0026, 0.0046, -0.0033, 0.0026, 0.0034, -0.0018, 0.0037, 0.0031, -0.0024, 0.0029, 0.0041, -0.0033, 0.0026, 0.0034, -0.0024, 0.0029, 0.0041, -0.0025, 0.0016, 0.0043, -0.0031, 0, 0.0031, -0.0036, 0.0015, 0.0033, -0.0025, 0.0016, 0.0043, -0.0025, 0.0016, 0.0043, -0.0036, 0.0015, 0.0033, -0.0033, 0.0026, 0.0034, -0.0037, 0.0015, 0.0022, -0.0036, 0.0015, 0.0033, -0.0031, 0, 0.0031, -0.0033, 0.0026, 0.0034, -0.0036, 0.0015, 0.0033, -0.0037, 0.0015, 0.0022, -0.0031, 0, 0.0031, -0.0026, 0, 0.0015, -0.0037, 0.0015, 0.0022, -0.0034, 0.0033, 0.002, -0.0018, 0.0037, 0.0031, -0.0033, 0.0026, 0.0034, -0.0037, 0.0015, 0.0022, -0.0034, 0.0033, 0.002, -0.0033, 0.0026, 0.0034, -0.0018, 0.0037, 0.0031, -0.0034, 0.0033, 0.002, -0.0024, 0.0046, 0.0014, -0.0036, 0.0037, 0, -0.0024, 0.0046, 0.0014, -0.0034, 0.0033, 0.002, -0.0046, 0.0026, 0.0012, -0.0034, 0.0033, 0.002, -0.0037, 0.0015, 0.0022, -0.0046, 0.0026, 0.0012, -0.0036, 0.0037, 0, -0.0034, 0.0033, 0.002, -0.0037, 0.0015, 0.0022, -0.0046, 0.0015, 0.0015, -0.0046, 0.0026, 0.0012, -0.0046, 0.0015, 0.0015, -0.0037, 0.0015, 0.0022, -0.0042, 0, 0.0011, -0.0037, 0.0015, 0.0022, -0.0026, 0, 0.0015, -0.0042, 0, 0.0011, -0.0046, 0.0015, 0.0015, -0.0042, 0, 0.0011, -0.005, 0.0016, 0, -0.0046, 0.0026, 0.0012, -0.0046, 0.0015, 0.0015, -0.005, 0.0016, 0, -0.0042, 0, 0.0011, -0.0044, 0, 0, -0.005, 0.0016, 0, -0.0046, 0.0026, 0.0012, -0.0047, 0.0029, 0, -0.0036, 0.0037, 0, -0.005, 0.0016, 0, -0.0047, 0.0029, 0, -0.0046, 0.0026, 0.0012, -0.005, 0.0016, 0, -0.0044, 0, 0, -0.0042, 0, -0.0011, -0.0036, 0.0037, 0, -0.0047, 0.0029, 0, -0.0046, 0.0026, -0.0012, -0.0047, 0.0029, 0, -0.005, 0.0016, 0, -0.0046, 0.0026, -0.0012, -0.005, 0.0016, 0, -0.0046, 0.0015, -0.0015, -0.0046, 0.0026, -0.0012, -0.0042, 0, -0.0011, -0.0046, 0.0015, -0.0015, -0.005, 0.0016, 0, -0.0042, 0, -0.0011, -0.0037, 0.0015, -0.0022, -0.0046, 0.0015, -0.0015, -0.0046, 0.0026, -0.0012, -0.0046, 0.0015, -0.0015, -0.0037, 0.0015, -0.0022, -0.0037, 0.0015, -0.0022, -0.0042, 0, -0.0011, -0.0026, 0, -0.0015, -0.0046, 0.0026, -0.0012, -0.0034, 0.0033, -0.002, -0.0036, 0.0037, 0, -0.0034, 0.0033, -0.002, -0.0046, 0.0026, -0.0012, -0.0037, 0.0015, -0.0022, -0.0036, 0.0037, 0, -0.0034, 0.0033, -0.002, -0.0024, 0.0046, -0.0014, -0.0018, 0.0037, -0.0031, -0.0024, 0.0046, -0.0014, -0.0034, 0.0033, -0.002, -0.0037, 0.0015, -0.0022, -0.0033, 0.0026, -0.0034, -0.0034, 0.0033, -0.002, -0.0034, 0.0033, -0.002, -0.0033, 0.0026, -0.0034, -0.0018, 0.0037, -0.0031, -0.0024, 0.0029, -0.0041, -0.0018, 0.0037, -0.0031, -0.0033, 0.0026, -0.0034, -0.0018, 0.0037, -0.0031, -0.0024, 0.0029, -0.0041, -0.0013, 0.0026, -0.0046, -0.0037, 0.0015, -0.0022, -0.0036, 0.0015, -0.0033, -0.0033, 0.0026, -0.0034, -0.0025, 0.0016, -0.0043, -0.0024, 0.0029, -0.0041, -0.0033, 0.0026, -0.0034, -0.0025, 0.0016, -0.0043, -0.0013, 0.0026, -0.0046, -0.0024, 0.0029, -0.0041, -0.0033, 0.0026, -0.0034, -0.0036, 0.0015, -0.0033, -0.0025, 0.0016, -0.0043, -0.0037, 0.0015, -0.0022, -0.0031, 0, -0.0031, -0.0036, 0.0015, -0.0033, -0.0031, 0, -0.0031, -0.0025, 0.0016, -0.0043, -0.0036, 0.0015, -0.0033, -0.0026, 0, -0.0015, -0.0031, 0, -0.0031, -0.0037, 0.0015, -0.0022, -0.0025, 0.0016, -0.0043, -0.0031, 0, -0.0031, -0.0022, 0, -0.0038, -0.001, 0.0015, -0.0048, -0.0013, 0.0026, -0.0046, -0.0025, 0.0016, -0.0043, -0.0022, 0, -0.0038, -0.001, 0.0015, -0.0048, -0.0025, 0.0016, -0.0043, -0.0013, 0.0026, -0.0046, -0.001, 0.0015, -0.0048, 0, 0.0015, -0.0043, -0.0012, 0, -0.0042, -0.001, 0.0015, -0.0048, -0.0022, 0, -0.0038, 0, 0, -0.003, 0, 0.0015, -0.0043, -0.001, 0.0015, -0.0048, -0.001, 0.0015, -0.0048, -0.0012, 0, -0.0042, 0, 0, -0.003, -0.0044, 0, 0, 0, 0, 0, -0.0026, 0, -0.0015, -0.0026, 0, -0.0015, -0.0042, 0, -0.0011, -0.0044, 0, 0, -0.0026, 0, 0.0015, 0, 0, 0, -0.0044, 0, 0, -0.0044, 0, 0, -0.0042, 0, 0.0011, -0.0026, 0, 0.0015, -0.0022, 0, -0.0038, -0.0031, 0, -0.0031, -0.0026, 0, -0.0015, -0.0022, 0, -0.0038, -0.0026, 0, -0.0015, 0, 0, 0, -0.0022, 0, 0.0038, 0, 0, 0, -0.0026, 0, 0.0015, -0.0026, 0, 0.0015, -0.0031, 0, 0.0031, -0.0022, 0, 0.0038, -0.0022, 0, -0.0038, 0, 0, 0, 0, 0, -0.003, 0, 0, -0.003, -0.0012, 0, -0.0042, -0.0022, 0, -0.0038, 0, 0, 0.003, 0, 0, 0, -0.0022, 0, 0.0038, -0.0022, 0, 0.0038, -0.0012, 0, 0.0042, 0, 0, 0.003, 0.0022, 0, -0.0038, 0.0012, 0, -0.0042, 0, 0, -0.003, 0.0022, 0, -0.0038, 0, 0, -0.003, 0, 0, 0, 0.0022, 0, 0.0038, 0, 0, 0, 0, 0, 0.003, 0, 0, 0.003, 0.0012, 0, 0.0042, 0.0022, 0, 0.0038, 0.0022, 0, -0.0038, 0, 0, 0, 0.0026, 0, -0.0015, 0.0026, 0, -0.0015, 0.0031, 0, -0.0031, 0.0022, 0, -0.0038, 0.0026, 0, 0.0015, 0, 0, 0, 0.0022, 0, 0.0038, 0.0022, 0, 0.0038, 0.0031, 0, 0.0031, 0.0026, 0, 0.0015, 0.0044, 0, 0, 0.0042, 0, -0.0011, 0.0026, 0, -0.0015, 0.0026, 0, 0.0015, 0.0042, 0, 0.0011, 0.0044, 0, 0, 0.0026, 0, -0.0015, 0, 0, 0, 0.0044, 0, 0, 0.0044, 0, 0, 0, 0, 0, 0.0026, 0, 0.0015)

[node name="Bush" instance=ExtResource("1_jr34d")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="." index="0"]
transform = Transform3D(100, 0, 0, 0, 100, -1.62921e-05, 0, 1.62921e-05, 100, 0, 0, 0)
shape = SubResource("ConcavePolygonShape3D_8rhmd")
