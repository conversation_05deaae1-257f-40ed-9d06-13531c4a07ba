<svg width="194" height="83" viewBox="0 0 194 83" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dd_260_90)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.5489 5.71653C31.1213 2.69084 161.477 2.69084 179.701 5.71653C185.543 6.75163 189.292 10.2551 190.426 15.5898C194 32.3903 193.913 49.1909 190.426 66.071C189.379 71.4058 185.543 75.3869 179.701 75.8647C161.477 77.3775 31.1213 77.3775 12.5489 75.8647C6.61971 75.3869 2.43438 71.4058 1.73683 66.071C-0.355838 49.1909 -0.79181 32.3903 1.73683 15.5898C2.52158 10.2551 6.70691 6.67201 12.5489 5.71653Z" fill="white"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.4753 5.61016C33.2738 2.7263 158.198 2.7263 175.662 5.61016C181.261 6.59674 184.854 9.93595 185.94 15.0206C189.366 31.0336 189.283 47.0466 185.94 63.1355C184.938 68.2202 181.261 72.0148 175.662 72.4701C158.198 73.912 33.2738 73.912 15.4753 72.4701C9.79311 72.0148 5.78217 68.2202 5.11368 63.1355C3.10821 47.0466 2.6904 31.0336 5.11368 15.0206C5.86573 9.93595 9.87667 6.52085 15.4753 5.61016Z" fill="#E6E6E6"/>
<g filter="url(#filter1_di_260_90)">
<path d="M15.7972 2.30428C32.3794 -0.723407 160.262 -0.812457 176.584 2.30428C181.706 3.28383 185.179 6.84581 186.134 12.0997C187.61 19.758 188.391 27.4163 188.478 35.0745C188.391 42.7328 187.61 49.1971 186.134 56.8553C185.179 62.1092 181.706 65.6712 176.584 66.6508C160.262 69.7675 32.3794 69.6785 15.7972 66.6508C10.5881 65.7603 6.85492 62.1983 6.16038 56.8553C5.20538 49.1971 4.68447 42.4657 4.59766 34.8964C4.68447 27.3272 5.20538 19.758 6.16038 12.0997C6.85492 6.75676 10.5881 3.19478 15.7972 2.30428Z" fill="#0194DE"/>
</g>
<g filter="url(#filter2_i_260_90)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M176.584 2.30428C160.262 -0.812456 32.3794 -0.723407 15.7972 2.30428C10.5881 3.19478 6.85492 6.75676 6.16038 12.0997C5.20538 19.758 4.68447 27.3272 4.59766 34.8964C19.3464 37.4533 58.2677 41.0686 95.9631 35.0745C133.659 29.0805 173.346 32.577 188.478 35.0745C188.391 27.4163 187.61 19.758 186.134 12.0997C185.179 6.84581 181.706 3.28383 176.584 2.30428Z" fill="#4BA1E8"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.0037 10.4488C12.3408 5.97472 17.9139 3.00732 19.6301 3.90377C21.3161 4.78448 19.602 10.3166 17.2649 14.7906C14.9278 19.2647 10.504 24.5306 8.81793 23.6499C7.10178 22.7534 7.66667 14.9228 10.0037 10.4488Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M182.328 58.4399C179.991 62.914 174.418 65.8814 172.702 64.9849C171.016 64.1042 172.73 58.5721 175.067 54.098C177.404 49.624 181.828 44.3581 183.514 45.2388C185.23 46.1352 184.665 53.9658 182.328 58.4399Z" fill="white"/>
<defs>
<filter id="filter0_dd_260_90" x="0" y="3.44727" width="193.074" height="79.5527" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_260_90"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.901961 0 0 0 0 0.901961 0 0 0 0 0.901961 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_260_90" result="effect2_dropShadow_260_90"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_260_90" result="shape"/>
</filter>
<filter id="filter1_di_260_90" x="4.59766" y="-2" width="183.879" height="74.9551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.356863 0 0 0 0 0.764706 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_260_90"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_260_90" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="5.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0235294 0 0 0 0 0.664052 0 0 0 0 0.984314 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_260_90"/>
</filter>
<filter id="filter2_i_260_90" x="4.59766" y="0" width="183.879" height="42.3379" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0235294 0 0 0 0 0.664052 0 0 0 0 0.984314 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_260_90"/>
</filter>
</defs>
</svg>
