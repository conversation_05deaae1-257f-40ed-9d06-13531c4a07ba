[gd_scene load_steps=5 format=3 uid="uid://18yd5e7o4a8u"]

[ext_resource type="Script" uid="uid://b285ejhk40bf4" path="res://power_up.gd" id="1_anhxp"]
[ext_resource type="AudioStream" uid="uid://qwubao116huk" path="res://assets/SFX/power-up_F_major.wav" id="2_m6ao4"]
[ext_resource type="PackedScene" uid="uid://icsgqcgib8cl" path="res://speed_boost.tscn" id="3_f1u6k"]

[sub_resource type="SphereShape3D" id="SphereShape3D_7fad3"]

[node name="PowerupSpeed" type="Area3D"]
collision_mask = 3
script = ExtResource("1_anhxp")
power_up_sfx = ExtResource("2_m6ao4")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.1, 0)
shape = SubResource("SphereShape3D_7fad3")

[node name="speed_boost" parent="." instance=ExtResource("3_f1u6k")]
transform = Transform3D(1.2, 0, 0, 0, 1.2, 0, 0, 0, 1.2, 0, 0, 0)

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
