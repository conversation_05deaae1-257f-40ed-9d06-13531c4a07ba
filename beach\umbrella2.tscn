[gd_scene load_steps=8 format=4 uid="uid://coinib8qssshp"]

[ext_resource type="PackedScene" uid="uid://c6xgvsxheu3td" path="res://beach/beach_umbrella_-_low_poly.glb" id="1_reaj5"]
[ext_resource type="Texture2D" uid="uid://d17ghqa6acyru" path="res://beach/beach_umbrella_-_low_poly_0.png" id="2_hu8uc"]
[ext_resource type="Texture2D" uid="uid://bbwy72achauqu" path="res://beach/beach_umbrella_-_low_poly_1.png" id="3_yjd7u"]

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_reaj5"]
data = PackedVector3Array(0, 0.3253, 0.7853, 0.23, 0.23, 0.7853, 0, 0, 0.85, 0.23, 0.23, 0.7853, 0.3253, 0, 0.7853, 0, 0, 0.85, 0.3253, 0, 0.7853, 0.23, -0.23, 0.7853, 0, 0, 0.85, 0.23, -0.23, 0.7853, 0, -0.3253, 0.7853, 0, 0, 0.85, 0, -0.3253, 0.7853, -0.23, -0.23, 0.7853, 0, 0, 0.85, -0.23, -0.23, 0.7853, -0.3253, 0, 0.7853, 0, 0, 0.85, -0.3253, 0, 0.7853, -0.23, 0.23, 0.7853, 0, 0, 0.85, -0.23, 0.23, 0.7853, 0, 0.3253, 0.7853, 0, 0, 0.85, 0, 0.3215, 0.776, 0, 0, 0.84, 0.2273, 0.2273, 0.776, 0.2273, 0.2273, 0.776, 0, 0, 0.84, 0.3215, 0, 0.776, 0.3215, 0, 0.776, 0, 0, 0.84, 0.2273, -0.2274, 0.776, 0.2273, -0.2274, 0.776, 0, 0, 0.84, 0, -0.3215, 0.776, 0, -0.3215, 0.776, 0, 0, 0.84, -0.2274, -0.2274, 0.776, -0.2274, -0.2274, 0.776, 0, 0, 0.84, -0.3215, 0, 0.776, -0.3215, 0, 0.776, 0, 0, 0.84, -0.2274, 0.2273, 0.776, -0.2274, 0.2273, 0.776, 0, 0, 0.84, 0, 0.3215, 0.776, 0, 0.4916, 0.6932, 0.23, 0.23, 0.7853, 0, 0.3253, 0.7853, 0, 0.4916, 0.6932, 0.3476, 0.3476, 0.6932, 0.23, 0.23, 0.7853, 0, 0.601, 0.601, 0.5094, 0.5094, 0.4496, 0.425, 0.425, 0.601, 0, 0.601, 0.601, 0, 0.7204, 0.4496, 0.5094, 0.5094, 0.4496, 0.5094, 0.5094, 0.4496, 0.601, 0, 0.601, 0.425, 0.425, 0.601, 0.5094, 0.5094, 0.4496, 0.7204, 0, 0.4496, 0.601, 0, 0.601, 0.3476, 0.3476, 0.6932, 0.3253, 0, 0.7853, 0.23, 0.23, 0.7853, 0.3476, 0.3476, 0.6932, 0.4916, 0, 0.6932, 0.3253, 0, 0.7853, 0.4916, 0, 0.6932, 0.23, -0.23, 0.7853, 0.3253, 0, 0.7853, 0.4916, 0, 0.6932, 0.3476, -0.3477, 0.6932, 0.23, -0.23, 0.7853, 0.7204, 0, 0.4496, 0.425, -0.425, 0.601, 0.601, 0, 0.601, 0.7204, 0, 0.4496, 0.5094, -0.5094, 0.4496, 0.425, -0.425, 0.601, 0.3476, -0.3477, 0.6932, 0, -0.3253, 0.7853, 0.23, -0.23, 0.7853, 0.3476, -0.3477, 0.6932, 0, -0.4917, 0.6932, 0, -0.3253, 0.7853, 0.5094, -0.5094, 0.4496, 0, -0.601, 0.601, 0.425, -0.425, 0.601, 0.5094, -0.5094, 0.4496, 0, -0.7204, 0.4496, 0, -0.601, 0.601, 0, -0.7204, 0.4496, -0.425, -0.425, 0.601, 0, -0.601, 0.601, 0, -0.7204, 0.4496, -0.5094, -0.5094, 0.4496, -0.425, -0.425, 0.601, 0, -0.4917, 0.6932, -0.23, -0.23, 0.7853, 0, -0.3253, 0.7853, 0, -0.4917, 0.6932, -0.3477, -0.3477, 0.6932, -0.23, -0.23, 0.7853, -0.5094, -0.5094, 0.4496, -0.601, 0, 0.601, -0.425, -0.425, 0.601, -0.5094, -0.5094, 0.4496, -0.7204, 0, 0.4496, -0.601, 0, 0.601, -0.3477, -0.3477, 0.6932, -0.3253, 0, 0.7853, -0.23, -0.23, 0.7853, -0.3477, -0.3477, 0.6932, -0.4917, 0, 0.6932, -0.3253, 0, 0.7853, -0.4917, 0, 0.6932, -0.23, 0.23, 0.7853, -0.3253, 0, 0.7853, -0.4917, 0, 0.6932, -0.3477, 0.3476, 0.6932, -0.23, 0.23, 0.7853, -0.601, 0, 0.601, -0.5094, 0.5094, 0.4496, -0.425, 0.425, 0.601, -0.601, 0, 0.601, -0.7204, 0, 0.4496, -0.5094, 0.5094, 0.4496, -0.3477, 0.3476, 0.6932, 0, 0.3253, 0.7853, -0.23, 0.23, 0.7853, -0.3477, 0.3476, 0.6932, 0, 0.4916, 0.6932, 0, 0.3253, 0.7853, -0.5094, 0.5094, 0.4496, 0, 0.601, 0.601, -0.425, 0.425, 0.601, -0.5094, 0.5094, 0.4496, 0, 0.7204, 0.4496, 0, 0.601, 0.601, -0.425, 0.425, 0.601, 0, 0.4916, 0.6932, -0.3477, 0.3476, 0.6932, -0.425, 0.425, 0.601, 0, 0.601, 0.601, 0, 0.4916, 0.6932, -0.601, 0, 0.601, -0.3477, 0.3476, 0.6932, -0.4917, 0, 0.6932, -0.601, 0, 0.601, -0.425, 0.425, 0.601, -0.3477, 0.3476, 0.6932, -0.425, -0.425, 0.601, -0.4917, 0, 0.6932, -0.3477, -0.3477, 0.6932, -0.425, -0.425, 0.601, -0.601, 0, 0.601, -0.4917, 0, 0.6932, 0, -0.601, 0.601, -0.3477, -0.3477, 0.6932, 0, -0.4917, 0.6932, 0, -0.601, 0.601, -0.425, -0.425, 0.601, -0.3477, -0.3477, 0.6932, 0.425, -0.425, 0.601, 0, -0.4917, 0.6932, 0.3476, -0.3477, 0.6932, 0.425, -0.425, 0.601, 0, -0.601, 0.601, 0, -0.4917, 0.6932, 0.601, 0, 0.601, 0.3476, -0.3477, 0.6932, 0.4916, 0, 0.6932, 0.601, 0, 0.601, 0.425, -0.425, 0.601, 0.3476, -0.3477, 0.6932, 0.425, 0.425, 0.601, 0.4916, 0, 0.6932, 0.3476, 0.3476, 0.6932, 0.425, 0.425, 0.601, 0.601, 0, 0.601, 0.4916, 0, 0.6932, 0, 0.601, 0.601, 0.3476, 0.3476, 0.6932, 0, 0.4916, 0.6932, 0, 0.601, 0.601, 0.425, 0.425, 0.601, 0.3476, 0.3476, 0.6932, 0, 0.4858, 0.685, 0.2273, 0.2273, 0.776, 0.3435, 0.3435, 0.685, 0, 0.4858, 0.685, 0, 0.3215, 0.776, 0.2273, 0.2273, 0.776, 0, 0.5934, 0.5945, 0.5034, 0.5034, 0.4439, 0, 0.7119, 0.4439, 0, 0.5934, 0.5945, 0.4196, 0.4196, 0.5945, 0.5034, 0.5034, 0.4439, 0.5034, 0.5034, 0.4439, 0.5934, 0, 0.5945, 0.7119, 0, 0.4439, 0.5034, 0.5034, 0.4439, 0.4196, 0.4196, 0.5945, 0.5934, 0, 0.5945, 0.3435, 0.3435, 0.685, 0.3215, 0, 0.776, 0.4858, 0, 0.685, 0.3435, 0.3435, 0.685, 0.2273, 0.2273, 0.776, 0.3215, 0, 0.776, 0.4858, 0, 0.685, 0.2273, -0.2274, 0.776, 0.3435, -0.3435, 0.685, 0.4858, 0, 0.685, 0.3215, 0, 0.776, 0.2273, -0.2274, 0.776, 0.7119, 0, 0.4439, 0.4196, -0.4196, 0.5945, 0.5034, -0.5034, 0.4439, 0.7119, 0, 0.4439, 0.5934, 0, 0.5945, 0.4196, -0.4196, 0.5945, 0.3435, -0.3435, 0.685, 0, -0.3215, 0.776, 0, -0.4858, 0.685, 0.3435, -0.3435, 0.685, 0.2273, -0.2274, 0.776, 0, -0.3215, 0.776, 0.5034, -0.5034, 0.4439, 0, -0.5935, 0.5945, 0, -0.7119, 0.4439, 0.5034, -0.5034, 0.4439, 0.4196, -0.4196, 0.5945, 0, -0.5935, 0.5945, 0, -0.7119, 0.4439, -0.4196, -0.4196, 0.5945, -0.5034, -0.5034, 0.4439, 0, -0.7119, 0.4439, 0, -0.5935, 0.5945, -0.4196, -0.4196, 0.5945, 0, -0.4858, 0.685, -0.2274, -0.2274, 0.776, -0.3435, -0.3435, 0.685, 0, -0.4858, 0.685, 0, -0.3215, 0.776, -0.2274, -0.2274, 0.776, -0.5034, -0.5034, 0.4439, -0.5935, 0, 0.5945, -0.7119, 0, 0.4439, -0.5034, -0.5034, 0.4439, -0.4196, -0.4196, 0.5945, -0.5935, 0, 0.5945, -0.3435, -0.3435, 0.685, -0.3215, 0, 0.776, -0.4858, 0, 0.685, -0.3435, -0.3435, 0.685, -0.2274, -0.2274, 0.776, -0.3215, 0, 0.776, -0.4858, 0, 0.685, -0.2274, 0.2273, 0.776, -0.3435, 0.3435, 0.685, -0.4858, 0, 0.685, -0.3215, 0, 0.776, -0.2274, 0.2273, 0.776, -0.5935, 0, 0.5945, -0.5034, 0.5034, 0.4439, -0.7119, 0, 0.4439, -0.5935, 0, 0.5945, -0.4196, 0.4196, 0.5945, -0.5034, 0.5034, 0.4439, -0.3435, 0.3435, 0.685, 0, 0.3215, 0.776, 0, 0.4858, 0.685, -0.3435, 0.3435, 0.685, -0.2274, 0.2273, 0.776, 0, 0.3215, 0.776, -0.5034, 0.5034, 0.4439, 0, 0.5934, 0.5945, 0, 0.7119, 0.4439, -0.5034, 0.5034, 0.4439, -0.4196, 0.4196, 0.5945, 0, 0.5934, 0.5945, -0.4196, 0.4196, 0.5945, 0, 0.4858, 0.685, 0, 0.5934, 0.5945, -0.4196, 0.4196, 0.5945, -0.3435, 0.3435, 0.685, 0, 0.4858, 0.685, -0.5935, 0, 0.5945, -0.3435, 0.3435, 0.685, -0.4196, 0.4196, 0.5945, -0.5935, 0, 0.5945, -0.4858, 0, 0.685, -0.3435, 0.3435, 0.685, -0.4196, -0.4196, 0.5945, -0.4858, 0, 0.685, -0.5935, 0, 0.5945, -0.4196, -0.4196, 0.5945, -0.3435, -0.3435, 0.685, -0.4858, 0, 0.685, 0, -0.5935, 0.5945, -0.3435, -0.3435, 0.685, -0.4196, -0.4196, 0.5945, 0, -0.5935, 0.5945, 0, -0.4858, 0.685, -0.3435, -0.3435, 0.685, 0.4196, -0.4196, 0.5945, 0, -0.4858, 0.685, 0, -0.5935, 0.5945, 0.4196, -0.4196, 0.5945, 0.3435, -0.3435, 0.685, 0, -0.4858, 0.685, 0.5934, 0, 0.5945, 0.3435, -0.3435, 0.685, 0.4196, -0.4196, 0.5945, 0.5934, 0, 0.5945, 0.4858, 0, 0.685, 0.3435, -0.3435, 0.685, 0.4196, 0.4196, 0.5945, 0.4858, 0, 0.685, 0.5934, 0, 0.5945, 0.4196, 0.4196, 0.5945, 0.3435, 0.3435, 0.685, 0.4858, 0, 0.685, 0, 0.5934, 0.5945, 0.3435, 0.3435, 0.685, 0.4196, 0.4196, 0.5945, 0, 0.5934, 0.5945, 0, 0.4858, 0.685, 0.3435, 0.3435, 0.685, 0.5094, 0.5094, 0.4496, 0.7119, 0, 0.4439, 0.7204, 0, 0.4496, 0.5094, 0.5094, 0.4496, 0.5034, 0.5034, 0.4439, 0.7119, 0, 0.4439, 0.7204, 0, 0.4496, 0.5034, -0.5034, 0.4439, 0.5094, -0.5094, 0.4496, 0.7204, 0, 0.4496, 0.7119, 0, 0.4439, 0.5034, -0.5034, 0.4439, 0.5094, -0.5094, 0.4496, 0, -0.7119, 0.4439, 0, -0.7204, 0.4496, 0.5094, -0.5094, 0.4496, 0.5034, -0.5034, 0.4439, 0, -0.7119, 0.4439, 0, -0.7204, 0.4496, -0.5034, -0.5034, 0.4439, -0.5094, -0.5094, 0.4496, 0, -0.7204, 0.4496, 0, -0.7119, 0.4439, -0.5034, -0.5034, 0.4439, -0.5094, -0.5094, 0.4496, -0.7119, 0, 0.4439, -0.7204, 0, 0.4496, -0.5094, -0.5094, 0.4496, -0.5034, -0.5034, 0.4439, -0.7119, 0, 0.4439, -0.7204, 0, 0.4496, -0.5034, 0.5034, 0.4439, -0.5094, 0.5094, 0.4496, -0.7204, 0, 0.4496, -0.7119, 0, 0.4439, -0.5034, 0.5034, 0.4439, -0.5094, 0.5094, 0.4496, 0, 0.7119, 0.4439, 0, 0.7204, 0.4496, -0.5094, 0.5094, 0.4496, -0.5034, 0.5034, 0.4439, 0, 0.7119, 0.4439, 0, 0.7204, 0.4496, 0.5034, 0.5034, 0.4439, 0.5094, 0.5094, 0.4496, 0, 0.7204, 0.4496, 0, 0.7119, 0.4439, 0.5034, 0.5034, 0.4439, 0, 0.0134, -0.432, 0.0095, 0.0095, 0.868, 0, 0.0134, 0.868, 0, 0.0134, -0.432, 0.0095, 0.0095, -0.432, 0.0095, 0.0095, 0.868, 0.0095, 0.0095, -0.432, 0.0134, 0, 0.868, 0.0095, 0.0095, 0.868, 0.0095, 0.0095, -0.432, 0.0134, 0, -0.432, 0.0134, 0, 0.868, 0.0134, 0, -0.432, 0.0095, -0.0095, 0.868, 0.0134, 0, 0.868, 0.0134, 0, -0.432, 0.0095, -0.0095, -0.432, 0.0095, -0.0095, 0.868, 0.0095, -0.0095, -0.432, 0, -0.0134, 0.868, 0.0095, -0.0095, 0.868, 0.0095, -0.0095, -0.432, 0, -0.0134, -0.432, 0, -0.0134, 0.868, 0, -0.0134, -0.432, -0.0095, -0.0095, 0.868, 0, -0.0134, 0.868, 0, -0.0134, -0.432, -0.0095, -0.0095, -0.432, -0.0095, -0.0095, 0.868, -0.0095, -0.0095, -0.432, -0.0134, 0, 0.868, -0.0095, -0.0095, 0.868, -0.0095, -0.0095, -0.432, -0.0134, 0, -0.432, -0.0134, 0, 0.868, -0.0134, 0, -0.432, -0.0095, 0.0095, 0.868, -0.0134, 0, 0.868, -0.0134, 0, -0.432, -0.0095, 0.0095, -0.432, -0.0095, 0.0095, 0.868, -0.0095, 0.0095, -0.432, 0, 0.0134, 0.868, -0.0095, 0.0095, 0.868, -0.0095, 0.0095, -0.432, 0, 0.0134, -0.432, 0, 0.0134, 0.868, -0.0095, -0.0095, 0.868, -0.0134, 0, 0.868, -0.0095, 0.0095, 0.868, -0.0095, 0.0095, 0.868, 0, 0.0134, 0.868, -0.0095, -0.0095, 0.868, 0, 0.0134, 0.868, 0, -0.0134, 0.868, -0.0095, -0.0095, 0.868, 0, 0.0134, 0.868, 0.0095, -0.0095, 0.868, 0, -0.0134, 0.868, 0, 0.0134, 0.868, 0.0095, 0.0095, 0.868, 0.0095, -0.0095, 0.868, 0.0095, 0.0095, 0.868, 0.0134, 0, 0.868, 0.0095, -0.0095, 0.868)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_w3t4f"]
resource_name = "Material.001"
cull_mode = 2
albedo_color = Color(0.8, 0.8, 0.8, 1)
albedo_texture = ExtResource("2_hu8uc")
normal_enabled = true
normal_texture = ExtResource("3_yjd7u")

[sub_resource type="ArrayMesh" id="ArrayMesh_hdqeg"]
_surfaces = [{
"aabb": AABB(-0.720398, -0.720398, -0.431977, 1.4408, 1.4408, 1.3),
"format": 34896613377,
"index_count": 450,
"index_data": PackedByteArray("GwAwAAIAMQAwABsAMAAyAAIAMwAyADAAMQAzADAAMwA0ADIAHQAzADEANAA1ADIAMgA1AAIAHAA0ADMAHQAcADMANAA2ADUAHAAfADQAHwA2ADQAAQAfABwAHQABABwANgA3ADUANQA3AAIAHwAhADYANgA4ADcAIQA4ADYANwA5AAIAOAA5ADcABQAhAB8AAQAFAB8AIQAjADgAOAA6ADkAIwA6ADgAOQA7AAIAOgA7ADkAOwA8AAIAPAAbAAIAOgA9ADsAPQA8ADsAPgAbADwAPQA+ADwAPgAxABsAIgA9ADoAIwAiADoAIAA+AD0AIgAgAD0AHgAxAD4AIAAeAD4AHgAdADEAIAARAB4AEQAdAB4ADAAgACIACgAiACMACgAMACIACAAKACMACAAjACEABQAIACEADAAQACAAIAAQABEACgALAAwADAANABAADAALAA0AEAAPABEAEAANAA8ACwAkAA0AJAAPAA0ACgAJAAsACAAJAAoACQAlAAsACwAlACQACAAHAAkABQAHAAgACQAmACUABwAmAAkABQAEAAcAAQAEAAUABwAoACYABAAoAAcAAQADAAQABAApACgAAwApAAQAKAA/ACYAJgBAACUAJgA/AEAAKABBAD8AKQBBACgAPwBCAEAAJQBAAEMAJQBDACQAQABCAEQAQABEAEMAPwAqAEIAQQAqAD8AQgAGAEQAKgAGAEIAQwBEACcARAAGACcAQwAnAEUAJABDAEUAQQArACoAKwAGACoARgArAEEAKQBGAEEARwAGACsARgBHACsASABGACkAAwBIACkASQBHAEYASABJAEYALAAGAEcASQAsAEcASgBJAEgASgBIAAMASwAsAEkASgBLAEkALQAGACwASwAtACwAJwAGAC0ARQAnAC0ARQAtAEsATABFAEsATABLAEoAJABFAEwAJABMAA8ADwBMAEoADwBKAA4ASgADAA4AEQAPAA4AAAAOAAMAEQAOAAAAAAADAAEAEQAAAB0AHQAAAAEAEgAuABQAEgATAC4AFgASABQAEwBNAC4AEwAVAE0ALgBNAC8AFQAvAE0AFAAuAC8AFQAXAC8AFwBOAC8AFAAvAE4AFwAaAE4AFgAUAE8AGAAWAE8AFABOAFAAGgBQAE4ATwAUAFAAGgAZAFAAGABPAFEAGQAYAFEAUABRAE8AGQBRAFAA"),
"lods": [0.073119, PackedByteArray("GwAcAAIAHQAcABsAHgAbAAIAHgAdABsAHQABABwAEQAdAB4AAQAfABwAHAAfAAIAHQAAAAEAEQAAAB0AAQAFAB8AIAARAB4AIAAeAAIAHwAhAAIABQAhAB8AIgAgAAIAIQAjAAIAIwAiAAIACAAjACEABQAIACEACgAiACMACAAKACMADAAgACIACgAMACIADAAQACAAIAAQABEACgALAAwADAANABAADAALAA0AEAAPABEAEAANAA8ACAAJAAoACgAJAAsACAAHAAkABQAHAAgACwAkAA0AJAAPAA0ACQAlAAsACwAlACQABwAmAAkACQAmACUABQAEAAcAAQAEAAUAJAAnAA8ABAAoAAcABwAoACYAAQADAAQAAAADAAEABAApACgAAwApAAQAKAAqACYAJgAqACUAKQArACgAKAArACoAKwAGACoAKgAGACUAKQAGACsAJQAGACQAJAAGACcALAAGACkAAwAsACkAJwAGAC0ALQAGACwADwAnAC0ADgAsAAMADgAtACwADwAtAA4AAAAOAAMAEQAPAA4AEQAOAAAAEgAuABQAEgATAC4AEwAVAC4AFgASABQAGAAWABQAGQAYABQAGgAZABQAFQAvAC4AFAAuAC8AGgAUAC8AFQAXAC8AFwAaAC8A"), 0.118555, PackedByteArray("AAABAAIAAAADAAEAAQADAAQAAQAEAAUAAQAFAAIAAwAGAAQABQAEAAcABAAGAAcABQAHAAgABQAIAAIACAAHAAkABwAGAAkACAAJAAoACAAKAAIACgAJAAsACQAGAAsACgAMAAIACgALAAwACwAGAA0ADAALAA0ADgAGAAMAAAAOAAMADQAGAA8ADwAGAA4ADAANABAAEAANAA8ADAAQAAIAEQAOAAAAEQAPAA4AEQAAAAIAEAAPABEAEAARAAIAEgATABQAEwAVABQAFgASABQAFQAXABQAGAAWABQAGQAYABQAFwAaABQAGgAZABQAGgAUABQA")],
"name": "Material.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 82,
"vertex_data": PackedByteArray("/3///5qtAACB2oHamq0AAP9//39y/AAAb9lv2XusAAB7/v9/e6wAAP///3+arQAA/3//f3r6AABv2Y8me6wAAIHafSWarQAA/3+DAXusAAD/fwAAmq0AAI8mjyZ7rAAAfSV9JZqtAACDAf9/e6wAAP9/e/57rAAAjyZv2XusAAAAAP9/mq0AAH0lgdqarQAA/39iggAAAACvga+BAAAAAP9/YoL//wAAYoL/fwAAAABPfq+BAAAAAK+BT34AAAAAnH3/fwAAAABPfk9+AAAAAP9/nH0AAAAA/3/KubTvAACCy4LLbMsAAP9/yepsywAAfDSCy2zLAADK6v9/bMsAADUV/39sywAAgst8NGzLAAB8NHw0bMsAAP9/NRVsywAAjhb/fyTKAABwNXA1JMoAAP9/jhYkygAAmldkqOHtAACOynA1JMoAAHDp/38kygAA/3/fRuHtAABkqJpX4e0AAGSoZKjh7QAA/38fueHtAACvga+B//8AAK+BT37//wAA3ajdqLTvAAD/f1rXkN0AAMq5/3+07wAAxL3EvZDdAABa1/9/kN0AAN2oIVe07wAAxL06QpDdAAD/fzRGtO8AAP9/pCiQ3QAAIVchV7TvAAA6QjpCkN0AADRG/3+07wAAIVfdqLTvAACkKP9/kN0AADpCxL2Q3QAA/3+tKfbbAAD2QvZC9tsAAAi99kL22wAAmleaV+HtAACtKf9/9tsAAN9G/3/h7QAA9kIIvfbbAABR1v9/9tsAAB+5/3/h7QAAjsqOyiTKAAAIvQi99tsAAP9/cOkkygAA/39R1vbbAABwNY7KJMoAAGKC/3///wAA/3+cff//AABPfq+B//8AAE9+T37//wAAnH3/f///AAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_kr8pu"]
resource_name = "Sketchfab_Scene_Umbrella_Material_001_0"
_surfaces = [{
"aabb": AABB(-0.720398, -0.720398, -0.431977, 1.4408, 1.4408, 1.3),
"attribute_data": PackedByteArray("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"),
"format": ***********,
"index_count": 450,
"index_data": PackedByteArray("QwBuAAIAbwBwAAUAcQByAAgAcwB0AAsAdQB2AA4AdwB4ABEAeQB6ABQAewBTABcAVQAZAFYAVwAbAHwAfQAhAGgAZQAnAGQAYQAmAH4AfwAkAIAAgQAgAFwAWgAeAFkAggCDAIQAggCFAIMARQABAEQARQAAAAEAAwBHAEYAAwAEAEcAhgCHAIgAhgCJAIcAigCLAIwAigCNAIsABgBJAEgABgAHAEkAjgCPAJAAjgCRAI8ACQBLAEoACQAKAEsADABNAEwADAANAE0AkgCTAJQAkgCVAJMADwBPAE4ADwAQAE8AlgCXAJgAlgCZAJcAmgCbAJwAmgCdAJsAUAATAFEAUAASABMAngCfAKAAngChAJ8AFQBUAFIAFQAWAFQAogCjAKQAogClAKMApgCnAKgApgCpAKcAqgCrAKwAqgCtAKsArgCvALAArgCxAK8AsgCzALQAsgC1ALMAtgC3ALgAtgC5ALcAugC7ALwAugC9ALsAvgC/AMAAvgDBAL8AwgDDAMQAwgDFAMMAxgAaABgAxgDHABoAyABYABwAyADJAFgAygDLAMwAygDNAMsAzgDPANAAzgDRAM8A0gBmACIA0gBnAGYA0wDUANUA0wDWANQA1wBiACUA1wBjAGIA2ABfACMA2ABgAF8A2QDaANsA2QDcANoA3QBdAB8A3QBeAF0A3gDfAOAA3gDhAN8A4gDjAOQA4gDlAOMAWwAdAOYAWwDnAB0A6ADpAOoA6ADrAOkA7ADtAO4A7ADvAO0A8ADxAPIA8ADzAPEA9AD1APYA9AD3APUA+AD5APoA+AD7APkA/AD9AP4A/AD/AP0AAAEBAQIBAAEDAQEBBAEFAQYBBAEHAQUBCAEJAQoBCAELAQkBDAENAQ4BDAEPAQ0BKAApACoAKAAsACkAEAErAC0AEAERASsAEgEvADEAEgETAS8AFAEzADUAFAEVATMAFgE3ADYAFgEXATcAGAE0ADIAGAEZATQAGgEwAC4AGgEbATAAHAEdAR4BHAEfAR0BOABpADoAOAA5AGkAIAEhASIBIAE7ACEBIwFqACQBIwE8AGoAJQEmAScBJQE9ACYBKAEpASoBKAE/ACkBKwEsAS0BKwFAACwBLgEvATABLgFBAC8BMQE+ADIBMQFCAD4AMwE0ATUBNQFrADMBawA2ATMBawBtADYBawBsAG0AbAA3AW0A"),
"lods": [0.073119, PackedByteArray("QwBEAAIARQBEAEMARQABAEQARQAAAAEARgBHAAUAAwBHAEYAAwAEAEcASABJAAgABgBJAEgABgAHAEkASgBLAAsACQBLAEoACQAKAEsATABNAA4ADABNAEwADAANAE0ATgBPABEADwBPAE4ADwAQAE8AUABRABQAUAATAFEAUAASABMAUgBTABcAUgBUAFMAFQBUAFIAFQAWAFQAVQAZAFYAGABVAFYAGABWABoAGgBXAFgAVwAbAFgAGgBYABwAHQBZABgAHQBaAFkAWgAeAFkAWwAdAB8AWwBcAB0AWwAgAFwAIwBdAB8AIwBeAF0AXgAkAF0AJQBfACMAJQBgAF8AYABhAF8AYQAmAF8AIgBiACUAIgBjAGIAYwBkAGIAYwBlAGQAZQAnAGQAHABmACIAHABnAGYAZwBoAGYAZwAhAGgAKAApACoAKgApACsAKAAsACkAKgArAC0ALgAsACgALQArAC8ALgAwACwALQAvADEAMgAwAC4AMQAvADMAMgA0ADAAMQAzADUANgA0ADIANQAzADcANgA3ADQANQA3ADYAOABpADoAOAA5AGkAOQA7AGkAOwBqAGkAOwA8AGoAPAA9AGoAPQA+AGoAPQA/AD4APwBAAD4AQABBAD4AQQBCAD4AawBsAG0A"), 0.118555, PackedByteArray("AAABAAIAAwAEAAUABgAHAAgACQAKAAsADAANAA4ADwAQABEAEgATABQAFQAWABcAGAAZABoAGgAbABwAHQAeABgAHwAgAB0AHAAhACIAIwAkAB8AJQAmACMAIgAnACUAKAApACoAKgApACsAKAAsACkAKgArAC0ALgAsACgALQArAC8ALgAwACwALQAvADEAMgAwAC4AMQAvADMAMgA0ADAAMQAzADUANgA0ADIANQAzADcANgA3ADQANQA3ADYAOAA5ADoAOQA7ADoAOwA8ADoAPAA9ADoAPQA+ADoAPQA/AD4APwBAAD4AQABBAD4AQQBCAD4A")],
"material": SubResource("StandardMaterial3D_w3t4f"),
"name": "Material.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 312,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_hdqeg")

[node name="beach_umbrella_-_low_poly" instance=ExtResource("1_reaj5")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="." index="0"]
transform = Transform3D(1, 0, 0, 0, 0.216443, 0.976295, 0, -0.976295, 0.216443, 0, 0.426946, 0.0914305)
shape = SubResource("ConcavePolygonShape3D_reaj5")

[node name="Umbrella_Material_001_0" parent="Sketchfab_model/194d4b57bad6477f96e15ceb18f2d5d2_fbx/RootNode/Umbrella" index="0"]
mesh = SubResource("ArrayMesh_kr8pu")
