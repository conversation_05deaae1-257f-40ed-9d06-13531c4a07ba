[gd_scene load_steps=15 format=3 uid="uid://cy1rn35c7x2st"]

[ext_resource type="Script" uid="uid://kuh5dx2w4d44" path="res://scripts/UI/HUD/message_hud.gd" id="1_n0fkf"]
[ext_resource type="FontFile" uid="uid://b14qykl1gecp1" path="res://assets/icons/ranking/Light.ttf" id="2_p2s7v"]
[ext_resource type="Texture2D" uid="uid://dihabx7y12ls7" path="res://assets/icons/menu_buttons/close.png" id="4_hs18u"]
[ext_resource type="Texture2D" uid="uid://dntpu43mo4657" path="res://assets/icons/menu_buttons/check.png" id="5_upwok"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_e7gj8"]
bg_color = Color(0.277528, 0.277528, 0.277528, 0.6)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_h0o1l"]
bg_color = Color(0.106275, 0.462347, 0.653006, 1)
border_width_left = 4
border_width_top = 4
border_width_right = 4
border_width_bottom = 4
corner_radius_top_left = 16
corner_radius_top_right = 16
corner_radius_bottom_right = 16
corner_radius_bottom_left = 16

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_j7nwp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_u2vd2"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_nd2vy"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_43fkt"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_mmpt0"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6qleq"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_i1hd3"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_r2ac5"]

[node name="MessageHUD" type="CanvasLayer"]
layer = 2
script = ExtResource("1_n0fkf")
metadata/_edit_vertical_guides_ = [960.0]

[node name="Panel2" type="Panel" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_e7gj8")

[node name="Panel" type="Panel" parent="."]
anchors_preset = -1
anchor_left = 0.308
anchor_top = 0.222
anchor_right = 0.692
anchor_bottom = 0.588
offset_left = 0.639954
offset_top = 0.191986
offset_right = -0.640015
offset_bottom = -0.0320129
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_h0o1l")

[node name="Close" type="Button" parent="Panel"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 80.0
offset_top = -55.0
offset_right = 212.0
offset_bottom = 81.0
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(0.8, 0.8)
theme_override_styles/focus = SubResource("StyleBoxEmpty_j7nwp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_u2vd2")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_nd2vy")
theme_override_styles/normal = SubResource("StyleBoxEmpty_43fkt")
icon = ExtResource("4_hs18u")

[node name="Accept" type="Button" parent="Panel"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -187.0
offset_top = -55.0
offset_right = -55.0
offset_bottom = 81.0
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(0.8, 0.8)
theme_override_styles/focus = SubResource("StyleBoxEmpty_mmpt0")
theme_override_styles/hover = SubResource("StyleBoxEmpty_6qleq")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_i1hd3")
theme_override_styles/normal = SubResource("StyleBoxEmpty_r2ac5")
icon = ExtResource("5_upwok")

[node name="Message" type="Label" parent="Panel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -288.0
offset_top = -108.0
offset_right = 288.0
offset_bottom = 84.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 6
theme_override_fonts/font = ExtResource("2_p2s7v")
theme_override_font_sizes/font_size = 32
horizontal_alignment = 1
vertical_alignment = 1

[connection signal="pressed" from="Panel/Close" to="." method="_on_close_pressed"]
[connection signal="pressed" from="Panel/Accept" to="." method="_on_accept_pressed"]
