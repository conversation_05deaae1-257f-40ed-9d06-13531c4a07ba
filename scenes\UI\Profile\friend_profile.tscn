[gd_scene load_steps=43 format=4 uid="uid://qxcdjv1215kb"]

[ext_resource type="Script" uid="uid://chr1j0l81c1y0" path="res://scenes/UI/Friends/friend_profile.gd" id="1_dyhqx"]
[ext_resource type="PackedScene" uid="uid://uy4saus24gv8" path="res://scenes/Utils/ocean.tscn" id="2_0ce4m"]
[ext_resource type="Shader" uid="uid://yj6bs51fsu4t" path="res://shaders/sky.gdshader" id="3_rwk7m"]
[ext_resource type="PackedScene" uid="uid://crnq1makno6d1" path="res://assets/Kenny/block-grass-overhang-large.glb" id="4_218cd"]
[ext_resource type="Texture2D" uid="uid://bymde2j4vycbh" path="res://assets/Kenny/Textures/colormap.png" id="5_3irio"]
[ext_resource type="PackedScene" uid="uid://cxc3n0k7aovh8" path="res://scenes/NewPlayer/model/model.tscn" id="6_53x0p"]
[ext_resource type="Script" uid="uid://cr2twkljoyot8" path="res://scripts/UI/responsive_ui.gd" id="7_lpo11"]
[ext_resource type="Texture2D" uid="uid://dckdyxwukw2t6" path="res://assets/new/ChatGPT Image Jul 23, 2025, 10_56_15 AM 1.png" id="8_ebkjg"]
[ext_resource type="Shader" uid="uid://bgt1w5dofdqng" path="res://shaders/profile.gdshader" id="9_a11os"]
[ext_resource type="Texture2D" uid="uid://bd42b8m7n3mdx" path="res://assets/images/profile_placeHolder.png" id="10_ltyc6"]
[ext_resource type="Texture2D" uid="uid://bu5itwu52tp2t" path="res://assets/icons/profile_buttons/profile_name.png" id="11_d7k0s"]
[ext_resource type="Texture2D" uid="uid://d3l6l02183ucw" path="res://assets/icons/profile_buttons/copy_profile_name.png" id="13_vqoka"]
[ext_resource type="Texture2D" uid="uid://p1iiqf3enirk" path="res://assets/icons/profile_buttons/copy_check.png" id="14_xjcgk"]
[ext_resource type="FontFile" uid="uid://b14qykl1gecp1" path="res://assets/icons/ranking/Light.ttf" id="15_oopj3"]
[ext_resource type="Texture2D" uid="uid://dc0oy0moqi6cv" path="res://assets/icons/profile_buttons/coin_long.png" id="16_tjyu0"]
[ext_resource type="Texture2D" uid="uid://bsmspoeic4i1" path="res://assets/icons/profile_buttons/cup_long.png" id="17_sfk6s"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_557ia"]
sky_top_color = Color(0.228725, 0.486073, 0.75, 1)
sky_horizon_color = Color(0.543182, 0.630007, 0.6708, 1)
sky_curve = 0.0861524
ground_bottom_color = Color(0.227451, 0.486275, 0.74902, 1)
ground_horizon_color = Color(0.545098, 0.631373, 0.670588, 1)
ground_curve = 0.0207053

[sub_resource type="Sky" id="Sky_oiqfe"]
sky_material = SubResource("ProceduralSkyMaterial_557ia")

[sub_resource type="Environment" id="Environment_xm84u"]
background_mode = 2
background_energy_multiplier = 1.5
sky = SubResource("Sky_oiqfe")
ambient_light_color = Color(1, 1, 1, 1)
ambient_light_sky_contribution = 0.0
ambient_light_energy = 1.3

[sub_resource type="ShaderMaterial" id="ShaderMaterial_xwv0j"]
render_priority = 0
shader = ExtResource("3_rwk7m")
shader_parameter/curve_amount = 0.0
shader_parameter/cloud_density = 0.512
shader_parameter/cloud_speed = Vector2(0.05, 0)
shader_parameter/scale_x = 20.0
shader_parameter/scale_y = 25.0
shader_parameter/softness = 0.087

[sub_resource type="SphereMesh" id="SphereMesh_asiay"]
flip_faces = true
radius = 20.0
height = 15.0
is_hemisphere = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_2voq8"]
resource_name = "colormap"
cull_mode = 2
albedo_color = Color(0.601223, 0.601223, 0.601223, 1)
albedo_texture = ExtResource("5_3irio")
texture_filter = 2

[sub_resource type="ArrayMesh" id="ArrayMesh_4ngdb"]
_surfaces = [{
"aabb": AABB(-1.04106, -4.56364e-30, -1.04106, 2.08212, 1, 2.08212),
"format": 34896613377,
"index_count": 432,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABAAGAAUABgAHAAUABgAIAAcACAAJAAcACAAKAAkACgALAAkADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAEwARABwAHAAdABMAFwAVAB4AHgAfABcAIgAgACEAIQAjACIAGQAgACIAIgAbABkAFAAWABAAEAASABQAIQAfAB4AHgAjACEADAAdABwAHAANAAwADgAPABgAGAAaAA4AHgAVAAsACwAKAB4AHgAKAAgACAAjAB4ABgAiACMAIwAIAAYAGwAiAAYABgAEABsAAgAMAA4ADgAAAAIABQATAB0AHQADAAUABwASABMAEwAFAAcAAwAdAAwADAACAAMACQAUABIAEgAHAAkAFQAUAAkACQALABUAAQAaABsAGwAEAAEAAQAAAA4ADgAaAAEAJQAkAA4AJgAlAA4ADgAnACYAKQAoACcAKgApACcAJwArACoAKwAMACwALAAtACsALQAuACsAGgAOAC8ALwAwABoAMwAxADIAMgA0ADMANAA1ADMANQAbADYANwA1ADYANgA4ADcAOQAiADQAOQA0ADoAOgA7ADkAGgAwADwAPAAbABoAPQAeACMAIwA+AD0APwA+ACMAIwAiAD8AQQAVAEAAQABCAEEAQABDAEIARQBAAEQARABGAEUARABHAEYASABEAB4AHgBJAEgAHgBKAEkASwAUABUAFQBMAEsATwBNAE4ATgBQAE8AUABRAE8AUQASAFIAUwBRAFIAUgBUAFMAVQATAFAAVQBQAFYAVgBXAFUADAAdAFgAWABZAAwAWgBYAB0AHQATAFoAFABLAFsAWwASABQAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZQBnAGYAZwBlAGgAaABpAGcAZABqAGsAawBdAGQAXQBlAGQAXQBcAGUAXABoAGUAXABsAGgAbABtAGgAbABgAG0AYABuAG0AYABiAG4AbwBsAFwAXABeAG8AaQBoAG0AbQBwAGkAZgBxAGoAagBkAGYAbgBiAGMAYwByAG4AbQBuAHIAcgBwAG0AcQBzAGsAawBqAHEAYQBgAGwAbABvAGEAcwBfAF0AXQBrAHMA"),
"lods": [1.29755, PackedByteArray("DgAMAA0ADQAPAA4AHAANAAwADgAPABgADAATABwAEwARABwAWABZAAwADAATAFgAWgBYABMAKwAMACwALAAtACsALQAuACsAEQATABIAEgAQABEABwASABMAEgAWABAAJwArACoAKgApACcAKQAoACcAFQASAAcAFgASABUAFQAXABYASwASABUAEgBLAFsAFQBMAEsAFwAVAB4AHgAfABcAQQAVAEAAQABCAEEAQABDAEIAIQAfAB4ARQBAAEQARABGAEUARABHAEYASABEAB4AHgBJAEgAHgBKAEkAHgAiACEAIgAgACEAPQAeACIAIgA+AD0APwA+ACIAGQAgACIAIgAbABkAGwAYABkAOQAiADQAOQA0ADoAOgA7ADkAMgA0ADMAMwAxADIANAA1ADMANQAbADYANwA1ADYANgA4ADcAGwAwADwALwAwABsAGwAOAC8ADgAbAAEAGAAbAA4AJQAkAA4AJgAlAA4ADgAnACYAHgAVAAcABwAiAB4AGwAiAAcAGwAHAAEADgABAAcABwAMAA4ABwATAAwATwBNAE4ATgBQAE8AUABRAE8AUQASAFIAUwBRAFIAUgBUAFMAVQATAFAAVQBQAFYAVgBXAFUAbABfAF4AbABeAG8AbABvAGEAYQBjAGwAYwByAGwAcgBwAGwAcwBfAGwAcQBzAGwAbABwAGkAbABpAGcAbABnAGYAZgBxAGwA")],
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 116,
"vertex_data": PackedByteArray("8vr//0rZAAAW8f//FvEAAPL6//+0JgAAFvH//+gOAABK2f//8voAAErZ//8MBQAAtCb///L6AAC0Jv//DAUAAOgO//8W8QAA6A7//+gOAAAMBf//StkAAAwF//+0JgAA////31cjAADy+v+/tCYAAP///9+n3AAA8vr/v0rZAAC0Jv+/DAUAAErZ/78MBQAAVyP/3wAAAACn3P/fAAAAAFkK/99ZCgAAAAD/31cjAADoDv+/6A4AAAwF/7+0JgAAFvH/vxbxAABK2f+/8voAAKX1/9+l9QAAp9z/3///AAAW8f+/6A4AAKX1/99ZCgAAAAD/36fcAAAMBf+/StkAALQm/7/y+gAA6A7/vxbxAABXI//f//8AAFkK/9+l9QAA//+wrrbUAAD//6yZxb0AAP//sK7TpgAA////3+KeAAD//7Cu8ZYAAP//rJn/fwAA//+wrg1pAAD////fHGEAAP//sK5IKwAA//+smTlCAAD//7CuK1kAADL9Ha5q4wAApfWsmaX1AAD/f6yZ//8AAA1psK7//wAA8Zawrv//AAAcYf/f//8AAOKe/9///wAAttSwrv//AADTprCu//8AAMW9rJn//wAASCuwrv//AAArWbCu//8AADlCrJn//wAAauMdrjL9AADMAh2uauMAAFkKrJml9QAAlBwdrjL9AAAAAP/fHGEAAAAAsK5IKwAAAACsmTlCAAAAALCuK1kAAAAA/9/ingAAAACwrg1pAAAAAKyZ/38AAAAAsK7xlgAAAACwrtOmAAAAAKyZxb0AAAAAsK621AAAWQqsmVkKAADMAh2ulBwAAP9/rJkAAAAA8ZawrgAAAAANabCuAAAAAOKe/98AAAAAHGH/3wAAAABIK7CuAAAAACtZsK4AAAAAOUKsmQAAAAC21LCuAAAAANOmsK4AAAAAxb2smQAAAACl9ayZWQoAADL9Ha6UHAAAauMdrswCAACUHB2uzAIAAErZAAAMBQAAFvEAAOgOAABK2f+/DAUAABbx/7/oDgAA6A4AAOgOAADoDv+/6A4AAAwFAAC0JgAADAX/v7QmAAAW8QAAFvEAAErZAADy+gAAFvH/vxbxAABK2f+/8voAALQmAADy+gAAtCb/v/L6AADy+gAAStkAAPL6AAC0JgAAtCYAAAwFAADoDgAAFvEAAAwFAABK2QAAtCb/vwwFAADoDv+/FvEAAPL6/79K2QAADAX/v0rZAADy+v+/tCYAAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_dtoc6"]
resource_name = "block-grass-overhang-large_block-grass-overhang-large"
_surfaces = [{
"aabb": AABB(-1.04106, -4.56364e-30, -1.04106, 2.08212, 1, 2.08212),
"attribute_data": PackedByteArray("/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zf/n/83mLn/N/+f/zeYuf83mLn/N5i5/zf/n/83/5//N/+f/zf/n/83mLn/N5i5/zeYuf83mLn/N/+f/zf/n/83mLn/N5i5/zf/n/83/5//N/+f/zf/n/83mLn/N5i5/zeYuf83mLn/N/+f/zf/n/83mLn/N/+f/zeYuf83/5//N5i5/zeYuf83/5//N/+f/zeYuf83/5//N5i5/zf/n/83/5//N5i5/zf/n/83mLn/N5i5/zeYuf83/5//N/+f/zf/n/83ZYb/N/+f/zdlhv83ZYb/N2WG/zf/n/83/5//N/+f/zf/n/83ZYb/N2WG/zf/n/83ZYb/N/+f/zdlhv83/5//N/+f/zdlhv83ZYb/N/+f/zf/n/83ZYb/N2WG/zf/n/83/5//N2WG/zdlhv83/5//N/+f/zdlhv83ZYb/N/+f/zf/n/83ZYb/N2WG/zf/n/83ZYb/N/+f/zdlhv83/5//N/+f/zdlhv83ZYb/N2WG/zf/n/83ZYb/N/+f/zfysf83/5//N5i5/zfysf83/5//N/Kx/zeYuf838rH/N/+f/zf/n/838rH/N5i5/zfysf83/5//Nyey/zf/n/83mLn/N5i5/zfysf838rH/N/+f/zf/n/83/5//N/Kx/zfysf83mLn/N/+f/zfysf838rH/N5i5/zeYuf83J7L/N/+f/zf/n/83/5//N/+f/zcnsv83mLn/N5i5/zf/n/83J7L/N/+f/zf/n/83/5//N/Kx/zeYuf838rH/N/+f/zfysf83mLn/N/Kx/zf/n/838rH/N5i5/zfysf83/5//N/+f/zeYuf83J7L/N5i5/zfysf838rH/N/+f/zf/n/83/5//N/Kx/zfysf83mLn/N/+f/zfysf838rH/N5i5/zf/n/83mLn/N/+f/zcnsv83mLn/N/+f/zcnsv83/5//N5i5/zcnsv83/5//N/+f/7dlxv+3Zcb/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3mPn/t2XG/7dlxv+3mPn/t5j5/7dlxv+3Zcb/t5j5/7eY+f+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7eY+f+3mPn/t2XG/7dlxv+3mPn/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3Zcb/t5j5/7dlxv+3mPn/t2XG/7eY+f+3Zcb/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3Zcb/t2XG/7eY+f+3mPn/t5j5/7dlxv+3mPn/t2XG/zf/n/83/5//N/+f/zf/n/83/5//N5i5/zeYuf83/5//N/+f/zeYuf83/5//N/+f/zf/n/83ZYb/N2WG/zcnsv83J7L/t5j5/7eY+f+3mPn/t5j5/7dlxv+3Zcb/t5j5/7eY+f+3mPn/t5j5/7eY+f+3mPk="),
"format": 34896613399,
"index_count": 432,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABAAGAAUABgAHAAUABgAIAAcACAAJAAcACAAKAAkACgALAAkADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIANgA0ADUANQA3ADYAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIARgBEAEUARQBHAEYASgBIAEkASQBLAEoATgBMAE0ATQBPAE4AUgBQAFEAUQBTAFIAVgBUAFUAVQBXAFYAWgBYAFkAWQBbAFoAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZQBnAGYAagBoAGkAaQBrAGoAbgBsAG0AbwBuAG0AbQBwAG8AcgBxAHAAcwByAHAAcAB0AHMAdAB1AHYAdgB3AHQAdwB4AHQAewB5AHoAegB8AHsAfwB9AH4AfgCAAH8AgACBAH8AgQCCAIMAhACBAIMAgwCFAIQAhwCGAIAAhwCAAIgAiACJAIcAjACKAIsAiwCNAIwAkACOAI8AjwCRAJAAlACSAJMAkwCVAJQAmACWAJcAlwCZAJgAlwCaAJkAnACXAJsAmwCdAJwAmwCeAJ0AoACbAJ8AnwChAKAAnwCiAKEApQCjAKQApACmAKUAqQCnAKgAqACqAKkAqgCrAKkAqwCsAK0ArgCrAK0ArQCvAK4AsQCwAKoAsQCqALIAsgCzALEAtgC0ALUAtQC3ALYAugC4ALkAuQC7ALoAvgC8AL0AvQC/AL4AwgDAAMEAwQDDAMIAxgDEAMUAxQDHAMYAygDIAMkAyQDLAMoAzgDMAM0AzQDPAM4A0gDQANEA0QDTANIA0wDUANIA0wDVANQA1QDWANQA1QDXANYA1wDYANYA1wDZANgA2QDaANgA2QDbANoA3gDcAN0A3QDfAN4A4gDgAOEA4QDjAOIA5gDkAOUA5QDnAOYA6gDoAOkA6QDrAOoA7gDsAO0A7QDvAO4A8gDwAPEA8QDzAPIA9gD0APUA9QD3APYA+gD4APkA+QD7APoA"),
"lods": [1.29755, PackedByteArray("DgAMAA0ADQAPAA4ABQENAAwADgAPAAIBDAATAAUBEwARAAUBtQC3AAwADAATALUAugC1ABMAdAAMAHYAdgB3AHQAdwB4AHQAEQATABIAEgAQABEAVgASABMAEgABARAAcAB0AHMAcwByAHAAcgBxAHAA/wASAFYAAQESAP8A/wAXAAEBpQASAP8AEgClAL0A/wCmAKUAFwD/ACEAIQAjABcAmAD/AJcAlwCZAJgAlwCaAJkAJQAjACEAnACXAJsAmwCdAJwAmwCeAJ0AoACbACEAIQChAKAAIQCiAKEAIQAHASUABwEkACUADAEhAAcBBwGRAAwBlACRAAcBGQAkAAcBBwEDARkAAwECARkAhwAHAYAAhwCAAIgAiACJAIcAfgCAAH8AfwB9AH4AgACBAH8AgQADAYMAhACBAIMAgwCFAIQAAwF8AIsACwF8AAMBAwEOAAsBDgADAWYAAgEDAQ4AbgBsAA4AbwBuAA4ADgBwAG8ABgEAAQkBCQEIAQYBBAEIAQkBBAEJAQoB/QAKAQkBCQH8AP0ACQH+APwAqQCnAKgAqACqAKkAqgCrAKkAqwASAK0ArgCrAK0ArQCvAK4AsQATAKoAsQCqALIAsgCzALEA3AANAcIA3ADCAN4A3ADeAMUAxQDHANwAxwDrABIB6wAUARIBGAENAREBFgEXAREBEQETARABEQEQAQ8BEQEPAQ4BDgEVAREB")],
"material": SubResource("StandardMaterial3D_2voq8"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 281,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_4ngdb")

[sub_resource type="BoxShape3D" id="BoxShape3D_p7nmn"]
size = Vector3(2.04535, 0.953339, 1.5011)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6g3vb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_4ygdr"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_44lvw"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_i72mq"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_xwv0j"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_asiay"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_dpqqt"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_kll0j"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_w2hl0"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_rwfgf"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_5ywx8"]
shader = ExtResource("9_a11os")

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_votfh"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_esgkt"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gc4dp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sbc4m"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_0yk8j"]
bg_color = Color(0.345098, 0.345098, 0.345098, 0.6)
corner_radius_top_left = 16
corner_radius_top_right = 16
corner_radius_bottom_right = 16
corner_radius_bottom_left = 16

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_nv703"]
bg_color = Color(0.345098, 0.345098, 0.345098, 0.6)
corner_radius_top_left = 16
corner_radius_top_right = 16
corner_radius_bottom_right = 16
corner_radius_bottom_left = 16

[node name="FriendProfile" type="Node3D"]
script = ExtResource("1_dyhqx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_xm84u")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.887202, -0.423769, 0.182463, -0.0922675, 0.224523, 0.970091, -0.452062, -0.877502, 0.160097, 0, 0, 0)
light_energy = 2.25
light_specular = 2.5

[node name="Cloud" type="MeshInstance3D" parent="."]
transform = Transform3D(50, 0, 0, 0, 50, 0, 0, 0, 50, 3.39157, -53.3244, -0.590649)
material_override = SubResource("ShaderMaterial_xwv0j")
cast_shadow = 0
mesh = SubResource("SphereMesh_asiay")
skeleton = NodePath("../..")

[node name="Ocean" parent="." instance=ExtResource("2_0ce4m")]
transform = Transform3D(8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, -187.752)

[node name="block-grass-overhang-large" parent="." instance=ExtResource("4_218cd")]
transform = Transform3D(3.83022, 0, 3.21393, 0, 5, 0, -3.21393, 0, 3.83022, 0, -1.42524, 0)

[node name="block-grass-overhang-large" parent="block-grass-overhang-large" index="0"]
transform = Transform3D(0.819152, 0, 0.573576, 0, 1, 0, -0.573576, 0, 0.819152, -0.0880596, -3.30687e-05, 0.0379544)
mesh = SubResource("ArrayMesh_dtoc6")

[node name="CollisionShape3D" type="CollisionShape3D" parent="block-grass-overhang-large"]
transform = Transform3D(0.819152, 0, 0.573576, 0, 1, 0, -0.573576, 0, 0.819152, 0.0916301, 0.520737, 0.212595)
shape = SubResource("BoxShape3D_p7nmn")

[node name="Model" parent="block-grass-overhang-large" instance=ExtResource("6_53x0p")]
unique_name_in_owner = true
transform = Transform3D(0.917725, 0, -1.31064, 0, 1.6, 0, 1.31064, 0, 0.917725, -0.128416, 0.997407, 0.143921)

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 0.997526, 0.0702962, 0, -0.0702962, 0.997526, -12.8059, 9.7188, 17.9242)

[node name="GUI" type="CanvasLayer" parent="."]
script = ExtResource("7_lpo11")

[node name="back" type="Button" parent="GUI"]
z_index = 7
offset_left = 48.0
offset_top = 32.0
offset_right = 176.0
offset_bottom = 160.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_6g3vb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_4ygdr")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_44lvw")
theme_override_styles/normal = SubResource("StyleBoxEmpty_i72mq")
icon = ExtResource("8_ebkjg")

[node name="Profile" type="Control" parent="GUI"]
layout_mode = 3
anchors_preset = 0
offset_left = 163.0
offset_right = 1064.0
offset_bottom = 864.0
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ProfileReveal" type="Button" parent="GUI/Profile"]
layout_mode = 0
offset_left = 9.0
offset_top = 60.0
offset_right = 330.0
offset_bottom = 387.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_xwv0j")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_asiay")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_dpqqt")
theme_override_styles/hover = SubResource("StyleBoxEmpty_kll0j")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_w2hl0")
theme_override_styles/normal = SubResource("StyleBoxEmpty_rwfgf")

[node name="ProfilePicture" type="TextureRect" parent="GUI/Profile/ProfileReveal"]
material = SubResource("ShaderMaterial_5ywx8")
layout_mode = 1
anchors_preset = -1
anchor_left = -0.177
anchor_right = -0.177
offset_left = 63.817
offset_top = 11.0
offset_right = 1087.82
offset_bottom = 1035.0
scale = Vector2(0.3, 0.3)
texture = ExtResource("10_ltyc6")
expand_mode = 2

[node name="TextureRect2" type="TextureRect" parent="GUI/Profile"]
layout_mode = 1
anchors_preset = -1
anchor_left = -0.183
anchor_right = -0.183
offset_left = 565.883
offset_top = 88.0
offset_right = 1457.64
offset_bottom = 303.0
scale = Vector2(0.55, 0.55)
texture = ExtResource("11_d7k0s")
expand_mode = 2

[node name="CopyUserName" type="Button" parent="GUI/Profile/TextureRect2"]
layout_mode = 1
anchors_preset = -1
anchor_top = 0.449
anchor_bottom = 0.449
offset_left = 43.75
offset_top = -44.535
offset_right = 131.75
offset_bottom = 43.465
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_votfh")
theme_override_styles/hover = SubResource("StyleBoxEmpty_esgkt")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_gc4dp")
theme_override_styles/normal = SubResource("StyleBoxEmpty_sbc4m")
icon = ExtResource("13_vqoka")

[node name="Copy_Check" type="Button" parent="GUI/Profile/TextureRect2"]
layout_mode = 1
anchors_preset = -1
anchor_top = 0.449
anchor_bottom = 0.449
offset_left = 43.75
offset_top = -44.535
offset_right = 131.75
offset_bottom = 43.465
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_votfh")
theme_override_styles/hover = SubResource("StyleBoxEmpty_esgkt")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_gc4dp")
theme_override_styles/normal = SubResource("StyleBoxEmpty_sbc4m")
icon = ExtResource("14_xjcgk")

[node name="UserName" type="Label" parent="GUI/Profile/TextureRect2"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -192.5
offset_top = -55.0
offset_right = 192.5
offset_bottom = 37.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 20
theme_override_fonts/font = ExtResource("15_oopj3")
theme_override_font_sizes/font_size = 80
text = "Nima Belak"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Coin_img" type="TextureRect" parent="GUI/Profile/TextureRect2"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.461
anchor_top = 1.0
anchor_right = 0.461
anchor_bottom = 1.0
offset_left = -486.212
offset_top = -6.25002
offset_right = 471.788
offset_bottom = 177.75
grow_horizontal = 2
grow_vertical = 0
texture = ExtResource("16_tjyu0")

[node name="coin" type="Label" parent="GUI/Profile/TextureRect2/Coin_img"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.573
anchor_top = 0.5
anchor_right = 0.573
anchor_bottom = 0.5
offset_left = -350.187
offset_top = -37.1137
offset_right = 398.398
offset_bottom = 37.8863
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("15_oopj3")
theme_override_font_sizes/font_size = 64
horizontal_alignment = 1
vertical_alignment = 1

[node name="Cup_img" type="TextureRect" parent="GUI/Profile/TextureRect2"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -453.5
offset_top = 187.5
offset_right = 438.5
offset_bottom = 339.5
grow_horizontal = 2
grow_vertical = 0
texture = ExtResource("17_sfk6s")

[node name="cup" type="Label" parent="GUI/Profile/TextureRect2/Cup_img"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.567
anchor_top = 0.5
anchor_right = 0.567
anchor_bottom = 0.5
offset_left = -347.631
offset_top = -29.4091
offset_right = 347.311
offset_bottom = 29.4091
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("15_oopj3")
theme_override_font_sizes/font_size = 64
horizontal_alignment = 1
vertical_alignment = 1

[node name="Bio" type="Panel" parent="GUI/Profile"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_top = 0.47
anchor_right = 0.988
anchor_bottom = 0.7
offset_left = 0.0990152
offset_top = -0.0800171
offset_right = 380.812
offset_bottom = 85.2
scale = Vector2(0.7, 0.7)
theme_override_styles/panel = SubResource("StyleBoxFlat_0yk8j")

[node name="Title" type="Label" parent="GUI/Profile/Bio"]
layout_mode = 0
offset_left = 25.0
offset_top = 13.0
offset_right = 143.0
offset_bottom = 88.0
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 14
theme_override_fonts/font = ExtResource("15_oopj3")
theme_override_font_sizes/font_size = 60
text = "Bio :"

[node name="Bio" type="Label" parent="GUI/Profile/Bio"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.48
anchor_top = 0.576
anchor_right = 0.48
anchor_bottom = 0.576
offset_left = -479.6
offset_top = -79.584
offset_right = 480.4
offset_bottom = 79.416
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("15_oopj3")
theme_override_font_sizes/font_size = 44
text = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt."
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="Panel2" type="Panel" parent="GUI/Profile"]
layout_mode = 1
anchors_preset = -1
anchor_top = 0.714
anchor_right = 0.99
anchor_bottom = 0.955
offset_left = 1.52588e-05
offset_top = 0.103943
offset_right = 382.01
offset_bottom = 88.8799
scale = Vector2(0.7, 0.7)
theme_override_styles/panel = SubResource("StyleBoxFlat_nv703")

[node name="VBoxContainer" type="VBoxContainer" parent="GUI/Profile/Panel2"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 23.0
offset_top = -93.0
offset_right = 230.0
offset_bottom = 93.0
grow_vertical = 2
theme_override_constants/separation = 24

[node name="kol" type="Label" parent="GUI/Profile/Panel2/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 14
theme_override_fonts/font = ExtResource("15_oopj3")
theme_override_font_sizes/font_size = 44
text = "Games Played :"

[node name="win" type="Label" parent="GUI/Profile/Panel2/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 14
theme_override_fonts/font = ExtResource("15_oopj3")
theme_override_font_sizes/font_size = 44
text = "Games Won :"

[node name="age" type="Label" parent="GUI/Profile/Panel2/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 14
theme_override_fonts/font = ExtResource("15_oopj3")
theme_override_font_sizes/font_size = 44
text = "Account Age :"

[node name="LongPressTimer" type="Timer" parent="."]

[connection signal="pressed" from="GUI/back" to="." method="_on_back_pressed"]
[connection signal="pressed" from="GUI/Profile/ProfileReveal" to="." method="_on_profile_reveal_pressed"]
[connection signal="pressed" from="GUI/Profile/TextureRect2/CopyUserName" to="." method="_on_copy_user_name_pressed"]

[editable path="block-grass-overhang-large"]
