shader_type canvas_item;

uniform float lod: hint_range(0.0, 5.0) = 0.0;
uniform vec4 tint_color: source_color = vec4(1.0, 1.0, 1.0, 0.0);
uniform sampler2D screen_texture : hint_screen_texture, repeat_disable, filter_linear_mipmap;

void fragment(){
    vec4 color = texture(screen_texture, SCREEN_UV, lod);
    vec4 final_color = color;
    final_color.rgb += tint_color.rgb * tint_color.a;
    final_color.a = color.a; // Preserve the original alpha for transparency
    COLOR = final_color;
}