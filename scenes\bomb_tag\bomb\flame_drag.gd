extends Marker3D

@onready var attractor: GPUParticlesAttractorSphere3D = %GPUParticlesAttractorSphere3D
@export var max_ditance := 0.5
@export var speed := 8.0
@export var max_strengh := 10.0
@onready var bomb: Node3D = $".."

func _process(delta: float) -> void:
	var parent: Node3D = get_parent()
	var parent_pos: Vector3 = parent.global_position
	var distance: float = attractor.global_position.distance_to(parent_pos)
	
	# Calculate offset direction using parent's rotation
	var offset: Vector3 = Vector3(-1, 1, 0)  # Base offset
	var rotated_offset: Vector3 = parent.global_transform.basis * offset
	
	# Get direction from parent to attractor and add rotated offset
	var base_direction: Vector3 = parent_pos.direction_to(attractor.global_position)
	var offset_direction: Vector3 = (base_direction + rotated_offset).normalized()
	
	# Handle maximum distance constraint
	if distance > max_ditance:
		attractor.global_position = parent_pos + offset_direction * max_ditance
	
	# Smooth movement towards target
	var target_pos: Vector3 = parent_pos + offset_direction * min(distance, max_ditance)
	attractor.global_position = attractor.global_position.move_toward(target_pos, speed * delta)
	
	# Update attractor strength based on distance
	var strength_factor: float = clamp(distance / max_ditance, 0.0, 1.0)
	attractor.strength = max_strengh * strength_factor
