extends Node3D

@onready var panel: Panel = $CanvasLayer/Panel
@onready var line_edit: LineEdit = $CanvasLayer/Panel/LineEdit
@onready var send: Button = $CanvasLayer/Panel/Send
#@onready var character: MeshInstance3D = $Player/RootNode/CharacterArmature/Skeleton3D/Character
var local_message_hud = preload("res://send_local_message.tscn")

func _ready():
	pass
	#TEST
	if Global.server:
		MultiplayerController.start_server()
	else:
		MultiplayerController.start_client()
	#if multiplayer.is_server():
		#print("hello")

#@rpc("any_peer","call_local")
#func peer_connected(id: int):
	#print("Game Logic: Acknowledging new player: " + str(id))
	##set_peer_skin(id)
	##sync_world_objects(id)
#
	## Wait a bit for the player to be fully spawned and initialized
	#await get_tree().create_timer(0.5).timeout
#
	## Request TCP ID from the newly connected player
	#if multiplayer.is_server():
		#print("PEER CONNECTED WITH ID: " + str(multiplayer.get_unique_id()))
		##request_tcp_id_from_player.rpc_id(id, id)
#
	##add_players_left()




func _on_button_pressed() -> void:
	panel.visible = false

func _on_send_pressed() -> void:
	#message.text = line_edit.text
	if line_edit.text.length() > 0:
		panel.visible = false

		var clear_timer = Timer.new()
		clear_timer.one_shot = true
		clear_timer.wait_time = 5
		clear_timer.timeout.connect(_on_delete_text_timer_timeout.bind(clear_timer))
		add_child(clear_timer) 
		clear_timer.start()

	line_edit.clear()

# Function to clear the text when the timer times out
func _on_delete_text_timer_timeout(timer: Timer) -> void:
	#message.text = ""
	timer.queue_free()


func _on_local_chat_pressed() -> void:
	var message = local_message_hud.instantiate()
	message.connect("send_message",_on_message_sent)
	add_child(message)
	#panel.visible = !panel.visible
	
func _on_message_sent(message:String):
	print(message)


func _on_global_chat_pressed() -> void:
	print("GLOBAL")
	
