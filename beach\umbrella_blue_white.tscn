[gd_scene load_steps=4 format=3 uid="uid://dpp7dy6ql6eh7"]

[ext_resource type="PackedScene" uid="uid://capwq1pvisl5j" path="res://beach/umbrella_blue_white.glb" id="1_7hv48"]

[sub_resource type="ConvexPolygonShape3D" id="ConvexPolygonShape3D_ct6x3"]
points = PackedVector3Array(-0.0131862, 0.375897, 0.0889061, -0.0217556, 0.210182, -0.228746, 0.283304, 0.133623, 0.0243918, -0.275146, 0.151662, 0.114507, 0.0850588, 0.151662, 0.258602, -0.0463197, 0.368341, -0.100767, -0.203082, 0.142643, -0.182678, 0.223705, 0.260509, -0.0695152, 0.195711, 0.252322, 0.148854, -0.238537, 0.269079, -0.034931, -0.154936, 0.269083, 0.181759, 0.16841, 0.13887, -0.184108, 0.102072, 0.367855, -0.0425467, 0.130737, 0.260494, -0.171387, -0.0468271, 0.227176, 0.249659, -0.111168, 0.367189, 0.0476403, -0.0859862, 0.151662, 0.258602, -0.12199, 0.133623, -0.236757, -0.172077, 0.277365, -0.13536, 0.0955772, 0.269036, 0.207799, 0.220213, 0.142643, 0.186529, -0.284118, 0.151662, -0.0656232, 0.0946972, 0.368696, 0.0648663, 0.239997, 0.252109, 0.0488685, -0.212915, 0.285676, 0.0898838, 0.0034306, 0.403577, -0.0100614, 0.0490553, 0.133623, -0.245804, -0.107194, 0.209334, -0.223411, 0.045626, 0.260587, -0.20418, 0.254521, 0.182418, -0.0798637, 0.0693976, 0.368217, -0.0841131, -0.112647, 0.360223, -0.0512344)

[sub_resource type="CylinderShape3D" id="CylinderShape3D_opryp"]
height = 0.565063
radius = 0.0131836

[node name="umbrella_blue_white" instance=ExtResource("1_7hv48")]

[node name="CollisionShape3D2" type="CollisionShape3D" parent="." index="2"]
shape = SubResource("ConvexPolygonShape3D_ct6x3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="." index="3"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00395799, 0.0139771, 0.00384632)
shape = SubResource("CylinderShape3D_opryp")
