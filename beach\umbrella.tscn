[gd_scene load_steps=10 format=4 uid="uid://b3avlr7pwo1l7"]

[ext_resource type="PackedScene" uid="uid://devioekmqj57f" path="res://beach/umbrella_red_white.glb" id="1_8sknd"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_8sknd"]
resource_name = "mat21"
albedo_color = Color(0.800781, 0.800781, 0.800781, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_4jb7c"]
resource_name = "mat8"
albedo_color = Color(0.959347, 0.247801, 0.189748, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_6pgbo"]
resource_name = "mat16"
albedo_color = Color(0.473121, 0.565728, 0.618031, 1)
emission_enabled = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_pcswo"]
resource_name = "mat22"
albedo_color = Color(0.62621, 0.62621, 0.62621, 1)
emission_enabled = true

[sub_resource type="ArrayMesh" id="ArrayMesh_crcxn"]
_surfaces = [{
"aabb": AABB(-0.288685, 0.138089, -0.241335, 0.567482, 0.267008, 0.506891),
"format": 34896613377,
"index_count": 126,
"index_data": PackedByteArray("AAABAAIAAgADAAAAAwACAAQABAAFAAMABQAEAAYABwAFAAYABQAHAAgACQABAAoACgALAAkACwAKAAwADAANAAsADQAMAA4ADwANAA4ADQAPABAAEQABABIAEgATABEAEwASABQAFAAVABMAFQAUABYAFwAVABYAFQAXABgAGQABABoAGgAbABkAGwAaABwAHAAdABsAHQAcAB4AHwAdAB4AHQAfACAAIQABACIAIgAjACEAIwAiACQAJAAlACMAJQAkACYAJwAlACYAJQAnACgAKQABACoAKgArACkAKwAqACwALAAtACsALQAsAC4ALwAtAC4ALQAvADAA"),
"name": "mat21",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 49,
"vertex_data": PackedByteArray("SIpc2GpDAAAogv//8XUAAFRuMtvSQgAAYJFId9QQAACcWlp64xgAAO+QbjYAAAAAFFA/O2kGAAAETqsDbgYAAEaRlwLZAAAAPrPh3LNpAAAKo7zc8E8AANbnxnMBVQAA6cCteLAlAADd9L40olEAAEDNDDSIGwAAm80AABocAACY9g8AP1IAALWuUt25mAAAdrSR3MmAAADM2Y52FcIAAGLvKXAnjgAA7ePUPVLQAABW/184r5MAAP//xQO7lQAAROTiC4XTAAALfrjc0rQAAJOYZds/sAAAIHS2gi/qAADXq21+rOMAAA5vm0hh/QAA6a/KQ/f2AAAIr2UT8vYAAFRv7hT//wAAvE3T3M+MAADaXgDdoqcAABMfLIuCpQAAdj51h63TAABAC5FCNqwAAL4y/UrY4QAA2TIAF0/jAABQCHcVxa0AABdWxNYRVAAAEkvK2QhwAAA+LneFJzkAAJ0XwYYVbQAAEBw1QQ4tAADTArdGkmkAAAAAUhZiagAA9xvHEHAtAAA=")
}, {
"aabb": AABB(-0.288685, 0.138089, -0.241335, 0.567482, 0.267008, 0.506891),
"format": 34896613377,
"index_count": 129,
"index_data": PackedByteArray("AAABAAIAAgADAAAAAwACAAQABAAFAAMABQAEAAYABwAFAAYABQAHAAgACQABAAoACgALAAkACwAKAAwADAANAAsADQAMAA4ADwANAA4ADwAOABAAEQABABIAEgATABEAEwASABQAFAAVABMAFQAUABYAFwAVABYAFQAXABgAGQABABoAGwAZABoAHAAZABsAGwAdABwAHQAbAB4AHwAdAB4AHQAfACAAIAAhAB0AIgABACMAIwAkACIAJAAjACUAJgAkACUAJwAkACYAKAAnACYAJwAoACkAKgABACsAKwAsACoALAArAC0ALQAuACwALgAtAC8AMAAuAC8ALgAwADEA"),
"name": "mat8",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 50,
"vertex_data": PackedByteArray("VG4y29JCAAAogv//8XUAABdWxNYRVAAAnFpaeuMYAAA+LneFJzkAABRQPztpBgAAEBw1QQ4tAAD3G8cQcC0AAAROqwNuBgAACqO83PBPAABIilzYakMAAOnArXiwJQAAYJFId9QQAABAzQw0iBsAAO+QbjYAAAAAm80AABocAABGkZcC2QAAAHa0kdzJgAAAPrPh3LNpAABi7ylwJ44AANbnxnMBVQAAVv9fOK+TAADd9L40olEAAJj2DwA/UgAA///FA7uVAACTmGXbP7AAALWuUt25mAAAzNmOdhXCAADXq21+rOMAAOmvykP39gAA7ePUPVLQAAAK5N86ndAAAETk4guF0wAACK9lE/L2AADaXgDdoqcAAAt+uNzStAAAdj51h63TAAAgdLaCL+oAAA5vm0hh/QAAvjL9StjhAABUb+4U//8AANkyABdP4wAAEkvK2QhwAAC8TdPcz4wAAJ0XwYYVbQAAEx8si4KlAADTArdGkmkAAEALkUI2rAAAUAh3FcWtAAAAAFIWYmoAAA==")
}, {
"aabb": AABB(-0.288685, 0.138089, -0.239654, 0.567482, 0.173715, 0.505211),
"format": 34896613377,
"index_count": 252,
"index_data": PackedByteArray("AAABAAIAAAADAAEAAAACAAQABAACAAUAAwAAAAYABAAFAAcABwAFAAgACQADAAYACQAKAAMACwAGAAAADAAKAAkADAANAAoADAAOAA0ADwAJAAYABgALAA8AEAAMAAkACQAPABAADgAMABEADAAQABIAEgARAAwAEwAOABEADgATABQAEQASABUAFQATABEAEwAWABQAFwAVABIAGAAWABMAFwASABkAGQASABAAGgAWABgAGgAbABYAHAAYABMAHAAaABgAGQAQAB0AHQAQAA8AHgAZAB0AHgAXABkAHQAPAB8AHgAdAB8AHwAPAAsAHwALACAAHgAfACAACwAhACAAHgAgACEAIQALACIAAAAiAAsAIgAAAAQABAAjACIAIQAiACMAIwAEAAcAIQAjACQAHgAhACQABwAlACMAJAAjACUAJQAHACYABwAIACYAJgAIACcAJAAlACgAHgAkACgAJgApACUAKAAlACkAJgAnACoAKQAmACoAKgAnABsAKgAbABoAKwAqABoAKgArACkAGgAcACsAKAApACwALAApACsAHgAoACwALAArAC0AHgAsAC0ALQArABwAHgAtAC4ALQAcAC4AHgAuAC8AHgAvABcALwAVABcALgAwAC8ALwAwABUALgAcADAAEwAVADAAHAATADAA"),
"name": "mat16",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 49,
"vertex_data": PackedByteArray("V5E2JU8AAABGkfsDAAAAAAROpAWZBQAAm80AAFgbAAAvUCctkQcAAPcbyRm9LAAARsuaJXYfAADUHVBAsi4AAAAATyLjaQAAffPfJ4RTAACY9hcAq1EAAMSJkyrrFQAA4PvnLR2UAAD//8sFYZUAAETkRRJf0wAAPL78NjQzAACK3oA3QF8AAEPfZjvXzwAA8eOMTF6VAAAkrUxARe8AAAiv0B3q9gAAss94Q9HDAABUbywg//8AAC65J2B3tAAA324BVoP8AABwzAReUY0AAHoyDFfI3wAA2TJaIzbjAABZa5pgAd4AABjKtVUmZAAAEoH//9d1AADPqq1cBUQAABKH11caLQAAsV2uXQUyAACKVSwyBhsAANEtZD2NOwAA9z0eYVVMAAAnISFKRWsAAPkEDEh8aAAAUAj+IH+tAAAUNBtbmHAAAKkiI1QcmwAAOQriUTOnAAC0PApdfscAAMM3lWNolgAAR00KYwm4AABWcHJoucoAALmVfGllyQAA6aK1XWHiAAA=")
}, {
"aabb": AABB(0.216575, 0.150486, 0.171162, 0.000757709, 0.0520923, 0.00632879),
"format": 34896613377,
"index_count": 3,
"index_data": PackedByteArray("AAABAAIA"),
"name": "mat22",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 3,
"vertex_data": PackedByteArray("AAD//wAAAABxVdbwMRcAAP//AAD//wAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_v4ohu"]
resource_name = "umbrella_red_white_mesh822346014"
_surfaces = [{
"aabb": AABB(-0.288685, 0.138089, -0.241335, 0.567482, 0.267008, 0.506891),
"attribute_data": PackedByteArray("p4em9zqD+/cKiED4o5m/7p6V8O2clxzvIMMD7TbDvutzwNrsN9ll8Pfdv+9q2NjvP86x9d/STfbNz1/1Uqmc+XepwfrQq9D5Ckbg8pA/cPPNRAn0c4EM5297eea9fcXny9IC5dzQdeMZzrbkbPV06/T4B+rs8mPq1/Gg7rP5aO5C85Dt4m3P/Shsmv9WccT+KzFP8WksQfJGL6by6min4MFi0eCfZH3hkdu+3XTXjtww1jfd2fzZ6Yv8Vui9+aHo+/py5HH9KuOG+iLjBxaC8VAS0/KrFdzyFwe/69MHNur3BHXrCjaf2QY4S9hvNErZGvK72hj4Idt5807ah/Yg6kf7JOtq+OXpdNfs2ITcutlk2aDYsQm12xINftpPCEjbvvo+7ubyfe7b+a3uXYL//+qCKf4wgKb/O0fR9qBNNfaORzf2CXuy5g2BRucXfVfm0cqv49LMO+V+zdjjT/oK6tD2eOtr+5DqWP1I6Kn9y+n//1Xpqvty4LX5xeF3/IrhWAdP7vgLKO3rCB7tcCaW8Eorq+84KHbvqWKj4NNoeeBBZuPfLtpr3Djent3R3sPcrAXl6eAEbuu8ByDquCnx2DAoTdqAKzfZyfTc27nuhdvL80Tcg/1P6rP4UOl2+4zqReGM2Vvcs9hv39bZ0wn628gGPd1oC1/c"),
"format": 34896613399,
"index_count": 126,
"index_data": PackedByteArray("AAABAAIAAwAEAAUABgAHAAgACQAKAAsADAANAA4ADwAQABEAEgATABQAFQAWABcAGAAZABoAGwAcAB0AHgAfACAAIQAiACMAJAAlACYAJwAoACkAKgArACwALQAuAC8AMAAxADIAMwA0ADUANgA3ADgAOQA6ADsAPAA9AD4APwBAAEEAQgBDAEQARQBGAEcASABJAEoASwBMAE0ATgBPAFAAUQBSAFMAVABVAFYAVwBYAFkAWgBbAFwAXQBeAF8AYABhAGIAYwBkAGUAZgBnAGgAaQBqAGsAbABtAG4AbwBwAHEAcgBzAHQAdQB2AHcAeAB5AHoAewB8AH0A"),
"material": SubResource("StandardMaterial3D_8sknd"),
"name": "mat21",
"primitive": 3,
"uv_scale": Vector4(0.604168, 2.62005, 0, 0),
"vertex_count": 126,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.288685, 0.138089, -0.241335, 0.567482, 0.267008, 0.506891),
"attribute_data": PackedByteArray("CpKF9VONR/UwkST2z6pr8ryoNPF8qInyJtDF87HSlvL/zTzzHteW94Tce/fa1+v2mcTY/FzH3P1exq/8/YfH/Q2Fp/5ZiUz+11tS9JJVWvRUWVr11ahL6IKkBuc8pHvo7uk27p3qZ+wP5lXtjPSK8V36HfGT81Dwg9iI+w/flvy43AD7Z07X/khJ//8xT/z/5UMk72A+xe/5QFzwI6Yp3VKglNzJoHrdh/VC60T3pOpI8TnqhPlO7YD6y+u59/Xr1eeO2MnpHtfX5jfXTh709xsaGvn8HGX5mBTR6nAVV+m6EpbqPo0h0uWRMtG5jKnRfPpN7OL/M+1e/AHslfEJ6S70Teq08QbpgXkmzWx/GM1Jep7MTAY47WME7uwUASjuZPIH8SL52vEw+SnxnuND++DcSPoP4of7REjN/2pNqP6qRzT/OVV88H5bdPBJVuPvE6Do52+kLOlmosvn2udM7C7nG+4Q6tHsLvAj6yz2jOtn8qTqiPqi65T5Ju0+/M/sJvx053r3b+hL+ojoJBba94IavvY9F7r2rEGI8TBH5vBRRIXw65xN3sSi3d6BoRfeKwYV7e8AA+7VAk7uERow6SYZquokHH3p+4630GWKq9GijzbR//+A7aj6lewd/s3tWuf96RvqOOvz6/vq9n8Ezgt6EM5Ef43Oy/Ae6hTu3uhH7Bvp"),
"format": 34896613399,
"index_count": 129,
"index_data": PackedByteArray("AAABAAIAAwAEAAUABgAHAAgACQAKAAsADAANAA4ADwAQABEAEgATABQAFQAWABcAGAAZABoAGwAcAB0AHgAfACAAIQAiACMAJAAlACYAJwAoACkAKgArACwALQAuAC8AMAAxADIAMwA0ADUANgA3ADgAOQA6ADsAPAA9AD4APwBAAEEAQgBDAEQARQBGAEcASABJAEoASwBMAE0ATgBPAFAAUQBSAFMAVABVAFYAVwBYAFkAWgBbAFwAXQBeAF8AYABhAGIAYwBkAGUAZgBnAGgAaQBqAGsAbABtAG4AbwBwAHEAcgBzAHQAdQB2AHcAeAB5AHoAewB8AH0AfgB/AIAA"),
"material": SubResource("StandardMaterial3D_4jb7c"),
"name": "mat8",
"primitive": 3,
"uv_scale": Vector4(0.620697, 2.49873, 0, 0),
"vertex_count": 129,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.288685, 0.138089, -0.239654, 0.567482, 0.173715, 0.505211),
"attribute_data": PackedByteArray("g/Rj66rzfOtp9s7svtix5sPX1ebp2vLnFr4p2y69UdvzwFDcTGDS1RFgGNaGZWrWTRYq3TIXct15HM/cEgDJ51QB+eclBdzm4AN66kIFpOoHB03p/gX650QJ3eb+B9HmWBL13HsRSd6DEwbdGkcAy+ZHNsuzSzrKivlx5n34kOZg+8Tncv6s6mv6mOmm/cvqikkU9g9JrvTaRxT2dkFa93g+Tvb5P5r30EpC9vdF9vXISdP2QU/R9WxKIfZ/T2X2N0qj8yBGd/THSwL0LkKg8lpAyvOlRMfynDyk9RA/z/VxQHX0LkDn8jVC7vOsQa/yZysK9GwvsPSRLI3z/UbN9QNMi/W+Rkj1zlFz97JVfvaLUA/3Q0e79OBJbfPHRXH0RZIk+KiPKvf9j074/4fW8bKF+/Hvh+vyB2zY7CdqJe26baHt1VW38N5UGPGzWFPx6UeY8J9HEvHgS9HwtkSX811FCfShSD3zxDwW8xA+VvNqQEHy1D+M9jlCePaaQVL1LyyC82wuNvPyK2TyW1Tq+J1VdPguUTz4CGSX++djF/vDX577JIQy+piCuvnYf9X6Xqhy9rKgYPY9oUb3g5UW7zSO0u/5j5PwTmyP5p9mBOg0aW7oll5d5ZBbPueoXmDnwjUU5iM2FuhyOePnSiGn6A4ldOrEJ+bppBIr7AQZce2hGrjsJAz47gwUle+wFLjuBg/p8YoXnPEwFs3wejDN+uU1PPnRMuT4Mor//1KHIP7NhNb+2KXf/K+f9Pstnsf8t5LQ9mGVyfcDleL2SZDX+y2MjPx2jqH85WMy+6FfOfvBYMD7AUMV+SpAU/jLP9H4HTrM9H45sfPvNxn02yfX8kwqrvEHKPfx2zqg8oc+zfEhPN7xGkIu8gdG1vG1RJnx1kPB88pH/PMgR4rzp1Mu8YNWyvG+VlDxOE+0789QgPDIUSDw0nzK8aV83/J8fo7yOxPD68cXuuoiFsLq3Ro98eUZIfA4GUbxTUGQ8gU9BfLOP8jy6VIX+fBNZvlaUqP59VHP9fxNv/ZVUlP2/2Ds9fFeSPdiYkr2REWD9KZF6vW+Rs303Efc9QZL4PaLSdz10UEW+KVGZ/hMQ9b33ksP97FQvvbiTH32B05d9ghShPXLTcn1r0eV9GZJaPMkRjX0ogic6egG8+q2Cbfpzgg86NAFZOngBoLppAts4dELFOBjClvh2/S765r1He2j9gDtx+iW8XDr4fLj6Yfxr1E2ypNWi8mwVV3JAADR5xAF4+bAA7HmWA+82mUVR9p0FAHaJHnMzvl9js8MfjTP0q9J3PayZt2UsyrdjNmi5rbb4+et3MHnNezQ7OTtP+7b7hnu"),
"format": 34896613399,
"index_count": 252,
"index_data": PackedByteArray("AAABAAIAAwAEAAUABgAHAAgACQAKAAsADAANAA4ADwAQABEAEgATABQAFQAWABcAGAAZABoAGwAcAB0AHgAfACAAIQAiACMAJAAlACYAJwAoACkAKgArACwALQAuAC8AMAAxADIAMwA0ADUANgA3ADgAOQA6ADsAPAA9AD4APwBAAEEAQgBDAEQARQBGAEcASABJAEoASwBMAE0ATgBPAFAAUQBSAFMAVABVAFYAVwBYAFkAWgBbAFwAXQBeAF8AYABhAGIAYwBkAGUAZgBnAGgAaQBqAGsAbABtAG4AbwBwAHEAcgBzAHQAdQB2AHcAeAB5AHoAewB8AH0AfgB/AIAAgQCCAIMAhACFAIYAhwCIAIkAigCLAIwAjQCOAI8AkACRAJIAkwCUAJUAlgCXAJgAmQCaAJsAnACdAJ4AnwCgAKEAogCjAKQApQCmAKcAqACpAKoAqwCsAK0ArgCvALAAsQCyALMAtAC1ALYAtwC4ALkAugC7ALwAvQC+AL8AwADBAMIAwwDEAMUAxgDHAMgAyQDKAMsAzADNAM4AzwDQANEA0gDTANQA1QDWANcA2ADZANoA2wDcAN0A3gDfAOAA4QDiAOMA5ADlAOYA5wDoAOkA6gDrAOwA7QDuAO8A8ADxAPIA8wD0APUA9gD3APgA+QD6APsA"),
"material": SubResource("StandardMaterial3D_6pgbo"),
"name": "mat16",
"primitive": 3,
"uv_scale": Vector4(0.612974, 2.60247, 0, 0),
"vertex_count": 252,
"vertex_data": PackedByteArray("V5E2JU8Avi1GkfsDAAC+LQROpAWZBb4tL1AnLZEHaCwETqQFmQVoLPcbyRm9LGgs1B1QQLIuuCL3G8kZvSy4IgAATyLjabgi+QQMSHxoAhMAAE8i42kCE1AI/iB/rQITOQriUTOnWQRQCP4gf61ZBNkyWiM241kEejIMV8jfDAHZMlojNuMMAVRvLCD//wwB324BVoP85wpUbywg///nCiStTEBF7+cKJK1MQEXvthhE5EUSX9O2GEPfZjvXz7YY4PvnLR2UvyNE5EUSX9O/I///ywVhlb8j4PvnLR2U9SH//8sFYZX1IZj2FwCrUfUhffPfJ4RTzCGY9hcAq1HMIZvNAABYG8whV5E2JU8ArSibzQAAWButKEaR+wMAAK0oilUsMgYboTRXkTYlTwChNC9QJy2RB6E00S1kPY07DzUvUCctkQcPNdQdUECyLg81JyEhSkVr1DLUHVBAsi7UMvkEDEh8aNQyqSIjVByb+DH5BAxIfGj4MTkK4lEzp/gxtDwKXX7HNS85CuJRM6c1L3oyDFfI3zUvWWuaYAHeZC56MgxXyN9kLt9uAVaD/GQuWWuaYAHe3zLfbgFWg/zfMiStTEBF798yss94Q9HDAjIkrUxARe8CMkPfZjvXzwIy8eOMTF6VSzdD32Y7189LN+D75y0dlEs3it6AN0Bf2zfg++ctHZTbN33z3yeEU9s3PL78NjQzFDl9898nhFMUOUbLmiV2HxQ5xImTKusVVTRGy5oldh9VNFeRNiVPAFU0sV2uXQUyWDfEiZMq6xVYN4pVLDIGG1g3sV2uXQUyFzKKVSwyBhsXMtEtZD2NOxcy9z0eYVVMmivRLWQ9jTuaKychIUpFa5orFDQbW5hwSy0nISFKRWtLLakiI1Qcm0stwzeVY2iW1SupIiNUHJvVK7Q8Cl1+x9UrR00KYwm4hS+0PApdfseFL1lrmmAB3oUvVnByaLnKQy9Za5pgAd5DL+mitV1h4kMvuZV8aWXJ9zXporVdYeL3NbLPeEPRw/c1LrknYHe0XjOyz3hD0cNeM/HjjExelV4zcMwEXlGNEjvx44xMXpUSO4regDdAXxI7GMq1VSZkKT6K3oA3QF8pPjy+/DY0Myk+z6qtXAVEsTo8vvw2NDOxOsSJkyrrFbE6EoH//9d1qzYSh9dXGi2rNrFdrl0FMqs2EoH//9d1tDCxXa5dBTK0MPc9HmFVTLQwEoH//9d1uSb3PR5hVUy5JhQ0G1uYcLkmEoH//9d1LiQUNBtbmHAuJMM3lWNoli4kEoH//9d1bx/DN5VjaJZvH0dNCmMJuG8fEoH//9d1aB9HTQpjCbhoH1Zwcmi5ymgfEoH//9d1nyNWcHJoucqfI7mVfGllyZ8jEoH//9d1viu5lXxpZcm+Ky65J2B3tL4rEoH//9d1+zQuuSdgd7T7NHDMBF5Rjfs0EoH//9d18EBwzAReUY3wQBjKtVUmZPBAEoH//9d1gD8YyrVVJmSAP8+qrVwFRIA/EoH//9d1ejvPqq1cBUR6OxKH11caLXo7xImTKusVEDexXa5dBTIQNxKH11caLRA3z6qtXAVE7jvEiZMq6xXuOxKH11caLe47GMq1VSZknD08vvw2NDOcPc+qrVwFRJw9cMwEXlGNAD2K3oA3QF8APRjKtVUmZAA9LrknYHe0tzTx44xMXpW3NHDMBF5Rjbc0uZV8aWXJgTCyz3hD0cOBMC65J2B3tIEwVnByaLnKhC7porVdYeKELrmVfGllyYQuR00KYwm4hC1Za5pgAd6ELVZwcmi5yoQtwzeVY2iWjC+0PApdfseML0dNCmMJuIwvFDQbW5hwpy2pIiNUHJunLcM3lWNolqct9z0eYVVMYiwnISFKRWtiLBQ0G1uYcGIssV2uXQUypzHRLWQ9jTunMfc9HmFVTKcxWWuaYAHeWR4krUxARe9ZHumitV1h4lkeJK1MQEXvCTCyz3hD0cMJMOmitV1h4gkwQ99mO9fP0DHx44xMXpXQMbLPeEPRw9Ax4PvnLR2UOz2K3oA3QF87PfHjjExelTs9ffPfJ4RTPDg8vvw2NDM8OIregDdAXzw4RsuaJXYfZDfEiZMq6xVkNzy+/DY0M2Q3V5E2JU8AbTSKVSwyBhttNMSJkyrrFW00L1AnLZEHWTTRLWQ9jTtZNIpVLDIGG1k01B1QQLIutTUnISFKRWu1NdEtZD2NO7U1+QQMSHxoKTOpIiNUHJspMychIUpFaykzOQriUTOn5DG0PApdfsfkMakiI1Qcm+QxejIMV8jfxy9Za5pgAd7HL7Q8Cl1+x8cvJK1MQEXvDhdUbywg//8OFwiv0B3q9g4XRORFEl/TmR8krUxARe+ZHwiv0B3q9pkfRORFEl/T6Sfg++ctHZTpJ0PfZjvXz+knffPfJ4RTOiebzQAAWBs6J0bLmiV2Hzonm80AAFgblC9XkTYlTwCUL0bLmiV2H5Qv4PvnLR2UGB6Y9hcAq1EYHn3z3yeEUxgeejIMV8jfTwFUbywg//9PAd9uAVaD/E8BOQriUTOngAXZMlojNuOABXoyDFfI34AF+QQMSHxobA9QCP4gf61sDzkK4lEzp2wP1B1QQLIu0yIAAE8i42nTIvkEDEh8aNMiL1AnLZEHYCz3G8kZvSxgLNQdUECyLmAsV5E2JU8ANS8ETqQFmQU1Ly9QJy2RBzUvIgGJBSIBiQUiAYkFnwYSIZ8GEiGfBhIhCA1CLggNQi4IDUIuLwQpvC8EKbwvBCm8QRZ2uUEWdrlBFna5AMndWgDJ3VoAyd1amjIcr5oyHK+aMhyvgzZwrYM2cK2DNnCt4EGSpOBBkqTgQZKk10dwjtdHcI7XR3COauMz5WrjM+Vq4zPlNPSh7TT0oe009KHt4yUZzuMlGc7jJRnO4ie6zOInusziJ7rMsSRozLEkaMyxJGjMwyMAzMMjAMzDIwDM9iInyfYiJ8n2IifJgyQZx4MkGceDJBnHWSkRyFkpEchZKRHIQSgHyEEoB8hBKAfI0zDUxdMw1MXTMNTFNivizTYr4s02K+LNSSrg0Ukq4NFJKuDRJCZYzSQmWM0kJljNxCCq3cQgqt3EIKrdahku1WoZLtVqGS7VLRf8yy0X/MstF/zLtR2CyrUdgsq1HYLK0R+5x9EfucfRH7nHKCSYyCgkmMgoJJjIDibMxg4mzMYOJszGiSsqyokrKsqJKyrKQy2exEMtnsRDLZ7EaS1Z0mktWdJpLVnSAzDS2QMw0tkDMNLZESiG3hEoht4RKIbeeR5x4HkeceB5HnHgYRaY1GEWmNRhFpjUmBM3yJgTN8iYEzfIixTUxYsU1MWLFNTF+Br4wPga+MD4GvjAcSDPvnEgz75xIM++UifcvFIn3LxSJ9y8hS6cu4UunLuFLpy7TzTovU806L1PNOi9bTl8zm05fM5tOXzO0zN959MzfefTM33nYiue6WIrnuliK57pAiAA3gIgAN4CIADe6ipY5OoqWOTqKljkGi8q2RovKtkaLyrZMjKczzIynM8yMpzPbStpyG0rachtK2nI/yutwv8rrcL/K63C3CUtxtwlLcbcJS3GByNLxwcjS8cHI0vHuCP5yLgj+ci4I/nIdB53ynQed8p0HnfKsB2HybAdh8mwHYfJpRob06UaG9OlGhvT5CaiuuQmorrkJqK6cDA/vXAwP71wMD+9Kijfxyoo38cqKN/HpjBR06YwUdOmMFHT1yl50NcpedDXKXnQ+iXB0/olwdP6JcHTUyZQzVMmUM1TJlDNmSXrzZkl682ZJevNPChxzTwocc08KHHNqiTtzKok7cyqJO3M8CO8y/AjvMvwI7zLOiTWyDok1sg6JNbI1Ct/tNQrf7TUK3+0qDR6r6g0eq+oNHqva0Cfp2tAn6drQJ+nE96w6RPesOkT3rDpOuFV+DrhVfg64VX41EcFjdRHBY3URwWNAMnRWgDJ0VoAydFacscea3LHHmtyxx5r5geNP+YHjT/mB40/yQdhMskHYTLJB2EyAge+IAIHviACB74ghwX79IcF+/SHBfv0")
}, {
"aabb": AABB(0.216575, 0.150486, 0.171162, 0.000757709, 0.0520923, 0.00632879),
"attribute_data": PackedByteArray("//+0/97/uP/s/f//"),
"format": 34896613399,
"index_count": 3,
"index_data": PackedByteArray("AAABAAIA"),
"material": SubResource("StandardMaterial3D_pcswo"),
"name": "mat22",
"primitive": 3,
"uv_scale": Vector4(0.575074, 2.13267, 0, 0),
"vertex_count": 3,
"vertex_data": PackedByteArray("AAD//wAA1itxVdbwMRfWK///AAD//9Yr5fi1A+X4tQPl+LUD")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_crcxn")

[sub_resource type="ConvexPolygonShape3D" id="ConvexPolygonShape3D_4jb7c"]
points = PackedVector3Array(-0.0131862, 0.375897, 0.0889061, -0.0217556, 0.210182, -0.228746, 0.283304, 0.133623, 0.0243918, -0.275146, 0.151662, 0.114507, 0.0850588, 0.151662, 0.258602, -0.0463197, 0.368341, -0.100767, -0.203082, 0.142643, -0.182678, 0.223705, 0.260509, -0.0695152, 0.195711, 0.252322, 0.148854, -0.238537, 0.269079, -0.034931, -0.154936, 0.269083, 0.181759, 0.16841, 0.13887, -0.184108, 0.102072, 0.367855, -0.0425467, 0.130737, 0.260494, -0.171387, -0.0468271, 0.227176, 0.249659, -0.111168, 0.367189, 0.0476403, -0.0859862, 0.151662, 0.258602, -0.12199, 0.133623, -0.236757, -0.172077, 0.277365, -0.13536, 0.0955772, 0.269036, 0.207799, 0.220213, 0.142643, 0.186529, -0.284118, 0.151662, -0.0656232, 0.0946972, 0.368696, 0.0648663, 0.239997, 0.252109, 0.0488685, -0.212915, 0.285676, 0.0898838, 0.0034306, 0.403577, -0.0100614, 0.0490553, 0.133623, -0.245804, -0.107194, 0.209334, -0.223411, 0.045626, 0.260587, -0.20418, 0.254521, 0.182418, -0.0798637, 0.0693976, 0.368217, -0.0841131, -0.112647, 0.360223, -0.0512344)

[sub_resource type="CylinderShape3D" id="CylinderShape3D_v4ohu"]
height = 0.565063
radius = 0.0131836

[node name="Red_2FWhite Beach Umbrella" instance=ExtResource("1_8sknd")]

[node name="group822346014" parent="." index="1"]
mesh = SubResource("ArrayMesh_v4ohu")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="." index="2"]
shape = SubResource("ConvexPolygonShape3D_4jb7c")

[node name="CollisionShape3D" type="CollisionShape3D" parent="." index="3"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00395799, 0.0139771, 0.00384632)
shape = SubResource("CylinderShape3D_v4ohu")
