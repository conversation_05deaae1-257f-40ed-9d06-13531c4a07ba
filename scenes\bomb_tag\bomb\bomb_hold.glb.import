[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://dcq3pswc2xnuo"
path="res://.godot/imported/bomb_hold.glb-88e8b0122fbb44760e86e70f25f6490d.scn"

[deps]

source_file="res://scenes/bomb_tag/bomb/bomb_hold.glb"
dest_files=["res://.godot/imported/bomb_hold.glb-88e8b0122fbb44760e86e70f25f6490d.scn"]

[params]

nodes/root_type=""
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
nodes/import_as_skeleton_bones=false
nodes/use_node_type_suffixes=true
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
animation/import_rest_as_RESET=false
import_script/path=""
_subresources={}
gltf/naming_version=1
gltf/embedded_image_handling=1
