[gd_scene load_steps=78 format=4 uid="uid://denplj2vogy62"]

[ext_resource type="Script" uid="uid://c4mkux42otsiy" path="res://scripts/UI/menu_pages/profile.gd" id="1_5x80j"]
[ext_resource type="PackedScene" uid="uid://uy4saus24gv8" path="res://scenes/Utils/ocean.tscn" id="2_yym5i"]
[ext_resource type="Shader" uid="uid://yj6bs51fsu4t" path="res://shaders/sky.gdshader" id="3_q0gxe"]
[ext_resource type="PackedScene" uid="uid://crnq1makno6d1" path="res://assets/Kenny/block-grass-overhang-large.glb" id="4_i0c74"]
[ext_resource type="Texture2D" uid="uid://bymde2j4vycbh" path="res://assets/Kenny/Textures/colormap.png" id="5_e5ac4"]
[ext_resource type="PackedScene" uid="uid://cxc3n0k7aovh8" path="res://scenes/NewPlayer/model/model.tscn" id="6_gsbxc"]
[ext_resource type="Texture2D" uid="uid://d3ube1cb3xpav" path="res://assets/icons/profile_buttons/edit.png" id="7_3i52n"]
[ext_resource type="Script" uid="uid://cr2twkljoyot8" path="res://scripts/UI/responsive_ui.gd" id="8_ae7pf"]
[ext_resource type="Texture2D" uid="uid://dckdyxwukw2t6" path="res://assets/new/ChatGPT Image Jul 23, 2025, 10_56_15 AM 1.png" id="9_2d2ru"]
[ext_resource type="Texture2D" uid="uid://d3itttak7cmn6" path="res://assets/Rectangle 56.png" id="10_2d2ru"]
[ext_resource type="Script" uid="uid://c8s1p2pbt0g56" path="res://scripts/category_selector.gd" id="10_hb3as"]
[ext_resource type="FontFile" uid="uid://cdh63neq4ginm" path="res://addons/toastparty/fonts/Light.ttf" id="11_hb3as"]
[ext_resource type="Texture2D" uid="uid://bwmx55f0nb8yt" path="res://assets/images/Group 213.png" id="11_yym5i"]
[ext_resource type="Texture2D" uid="uid://b7yuwa78ig386" path="res://assets/images/Group 214.png" id="12_yym5i"]
[ext_resource type="Texture2D" uid="uid://ykc4jv21udh2" path="res://assets/icons/ranking/coin_alone2.png" id="13_ae7pf"]
[ext_resource type="Texture2D" uid="uid://bc5e34tss4t7t" path="res://assets/images/Group 215.png" id="13_q0gxe"]
[ext_resource type="Texture2D" uid="uid://qird3ftr1424" path="res://assets/images/Group 216.png" id="14_i0c74"]
[ext_resource type="Shader" uid="uid://bgt1w5dofdqng" path="res://shaders/profile.gdshader" id="14_pb55e"]
[ext_resource type="Texture2D" uid="uid://bd42b8m7n3mdx" path="res://assets/images/profile_placeHolder.png" id="15_8y6ae"]
[ext_resource type="Texture2D" uid="uid://cbk13my08q56g" path="res://assets/images/Group 217.png" id="15_e5ac4"]
[ext_resource type="Texture2D" uid="uid://bkoif45lgn8b4" path="res://assets/images/Group 218.png" id="16_gsbxc"]
[ext_resource type="AudioStream" uid="uid://0wtm7wvori7d" path="res://assets/SFX/UIclick.wav" id="16_gu707"]
[ext_resource type="Texture2D" uid="uid://d35ww86cfnnqg" path="res://assets/images/Group 220.png" id="17_3i52n"]
[ext_resource type="Texture2D" uid="uid://bu5itwu52tp2t" path="res://assets/icons/profile_buttons/profile_name.png" id="17_hu0nb"]
[ext_resource type="Texture2D" uid="uid://dj4idusxt2unm" path="res://assets/images/Group 221.png" id="18_ae7pf"]
[ext_resource type="Texture2D" uid="uid://bhksr8s5dtoim" path="res://assets/icons/profile_buttons/edit_profile_name.png" id="18_wq64q"]
[ext_resource type="Texture2D" uid="uid://d3l6l02183ucw" path="res://assets/icons/profile_buttons/copy_profile_name.png" id="19_6wvqk"]
[ext_resource type="Texture2D" uid="uid://dpqmlbfh5t3f7" path="res://assets/images/Group 222.png" id="19_ae7pf"]
[ext_resource type="Texture2D" uid="uid://p1iiqf3enirk" path="res://assets/icons/profile_buttons/copy_check.png" id="20_17dce"]
[ext_resource type="FontFile" uid="uid://b14qykl1gecp1" path="res://assets/icons/ranking/Light.ttf" id="21_kyv72"]
[ext_resource type="Texture2D" uid="uid://dc0oy0moqi6cv" path="res://assets/icons/profile_buttons/coin_long.png" id="22_k7six"]
[ext_resource type="Texture2D" uid="uid://bsmspoeic4i1" path="res://assets/icons/profile_buttons/cup_long.png" id="23_eamum"]
[ext_resource type="Texture2D" uid="uid://b1clta4amqp4f" path="res://assets/icons/profile_buttons/bio_add.png" id="24_5gxp2"]
[ext_resource type="Texture2D" uid="uid://d2mbvesylfw5k" path="res://assets/new/Group 218.png" id="25_dm4j0"]
[ext_resource type="AudioStream" uid="uid://bchonrcbifm6c" path="res://assets/SFX/skin_change.wav" id="35_mural"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_mural"]
sky_top_color = Color(0.228725, 0.486073, 0.75, 1)
sky_horizon_color = Color(0.543182, 0.630007, 0.6708, 1)
sky_curve = 0.0861524
ground_bottom_color = Color(0.227451, 0.486275, 0.74902, 1)
ground_horizon_color = Color(0.545098, 0.631373, 0.670588, 1)
ground_curve = 0.0207053

[sub_resource type="Sky" id="Sky_mural"]
sky_material = SubResource("ProceduralSkyMaterial_mural")

[sub_resource type="Environment" id="Environment_dm4j0"]
background_mode = 2
background_energy_multiplier = 1.5
sky = SubResource("Sky_mural")
ambient_light_color = Color(1, 1, 1, 1)
ambient_light_sky_contribution = 0.0
ambient_light_energy = 1.3

[sub_resource type="ShaderMaterial" id="ShaderMaterial_pb55e"]
render_priority = 0
shader = ExtResource("3_q0gxe")
shader_parameter/curve_amount = 0.0
shader_parameter/cloud_density = 0.512
shader_parameter/cloud_speed = Vector2(0.05, 0)
shader_parameter/scale_x = 20.0
shader_parameter/scale_y = 25.0
shader_parameter/softness = 0.087

[sub_resource type="SphereMesh" id="SphereMesh_8y6ae"]
flip_faces = true
radius = 20.0
height = 15.0
is_hemisphere = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_2voq8"]
resource_name = "colormap"
cull_mode = 2
albedo_color = Color(0.601223, 0.601223, 0.601223, 1)
albedo_texture = ExtResource("5_e5ac4")
texture_filter = 2

[sub_resource type="ArrayMesh" id="ArrayMesh_4ngdb"]
_surfaces = [{
"aabb": AABB(-1.04106, -4.56364e-30, -1.04106, 2.08212, 1, 2.08212),
"format": 34896613377,
"index_count": 432,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABAAGAAUABgAHAAUABgAIAAcACAAJAAcACAAKAAkACgALAAkADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAEwARABwAHAAdABMAFwAVAB4AHgAfABcAIgAgACEAIQAjACIAGQAgACIAIgAbABkAFAAWABAAEAASABQAIQAfAB4AHgAjACEADAAdABwAHAANAAwADgAPABgAGAAaAA4AHgAVAAsACwAKAB4AHgAKAAgACAAjAB4ABgAiACMAIwAIAAYAGwAiAAYABgAEABsAAgAMAA4ADgAAAAIABQATAB0AHQADAAUABwASABMAEwAFAAcAAwAdAAwADAACAAMACQAUABIAEgAHAAkAFQAUAAkACQALABUAAQAaABsAGwAEAAEAAQAAAA4ADgAaAAEAJQAkAA4AJgAlAA4ADgAnACYAKQAoACcAKgApACcAJwArACoAKwAMACwALAAtACsALQAuACsAGgAOAC8ALwAwABoAMwAxADIAMgA0ADMANAA1ADMANQAbADYANwA1ADYANgA4ADcAOQAiADQAOQA0ADoAOgA7ADkAGgAwADwAPAAbABoAPQAeACMAIwA+AD0APwA+ACMAIwAiAD8AQQAVAEAAQABCAEEAQABDAEIARQBAAEQARABGAEUARABHAEYASABEAB4AHgBJAEgAHgBKAEkASwAUABUAFQBMAEsATwBNAE4ATgBQAE8AUABRAE8AUQASAFIAUwBRAFIAUgBUAFMAVQATAFAAVQBQAFYAVgBXAFUADAAdAFgAWABZAAwAWgBYAB0AHQATAFoAFABLAFsAWwASABQAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZQBnAGYAZwBlAGgAaABpAGcAZABqAGsAawBdAGQAXQBlAGQAXQBcAGUAXABoAGUAXABsAGgAbABtAGgAbABgAG0AYABuAG0AYABiAG4AbwBsAFwAXABeAG8AaQBoAG0AbQBwAGkAZgBxAGoAagBkAGYAbgBiAGMAYwByAG4AbQBuAHIAcgBwAG0AcQBzAGsAawBqAHEAYQBgAGwAbABvAGEAcwBfAF0AXQBrAHMA"),
"lods": [1.29755, PackedByteArray("DgAMAA0ADQAPAA4AHAANAAwADgAPABgADAATABwAEwARABwAWABZAAwADAATAFgAWgBYABMAKwAMACwALAAtACsALQAuACsAEQATABIAEgAQABEABwASABMAEgAWABAAJwArACoAKgApACcAKQAoACcAFQASAAcAFgASABUAFQAXABYASwASABUAEgBLAFsAFQBMAEsAFwAVAB4AHgAfABcAQQAVAEAAQABCAEEAQABDAEIAIQAfAB4ARQBAAEQARABGAEUARABHAEYASABEAB4AHgBJAEgAHgBKAEkAHgAiACEAIgAgACEAPQAeACIAIgA+AD0APwA+ACIAGQAgACIAIgAbABkAGwAYABkAOQAiADQAOQA0ADoAOgA7ADkAMgA0ADMAMwAxADIANAA1ADMANQAbADYANwA1ADYANgA4ADcAGwAwADwALwAwABsAGwAOAC8ADgAbAAEAGAAbAA4AJQAkAA4AJgAlAA4ADgAnACYAHgAVAAcABwAiAB4AGwAiAAcAGwAHAAEADgABAAcABwAMAA4ABwATAAwATwBNAE4ATgBQAE8AUABRAE8AUQASAFIAUwBRAFIAUgBUAFMAVQATAFAAVQBQAFYAVgBXAFUAbABfAF4AbABeAG8AbABvAGEAYQBjAGwAYwByAGwAcgBwAGwAcwBfAGwAcQBzAGwAbABwAGkAbABpAGcAbABnAGYAZgBxAGwA")],
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 116,
"vertex_data": PackedByteArray("8vr//0rZAAAW8f//FvEAAPL6//+0JgAAFvH//+gOAABK2f//8voAAErZ//8MBQAAtCb///L6AAC0Jv//DAUAAOgO//8W8QAA6A7//+gOAAAMBf//StkAAAwF//+0JgAA////31cjAADy+v+/tCYAAP///9+n3AAA8vr/v0rZAAC0Jv+/DAUAAErZ/78MBQAAVyP/3wAAAACn3P/fAAAAAFkK/99ZCgAAAAD/31cjAADoDv+/6A4AAAwF/7+0JgAAFvH/vxbxAABK2f+/8voAAKX1/9+l9QAAp9z/3///AAAW8f+/6A4AAKX1/99ZCgAAAAD/36fcAAAMBf+/StkAALQm/7/y+gAA6A7/vxbxAABXI//f//8AAFkK/9+l9QAA//+wrrbUAAD//6yZxb0AAP//sK7TpgAA////3+KeAAD//7Cu8ZYAAP//rJn/fwAA//+wrg1pAAD////fHGEAAP//sK5IKwAA//+smTlCAAD//7CuK1kAADL9Ha5q4wAApfWsmaX1AAD/f6yZ//8AAA1psK7//wAA8Zawrv//AAAcYf/f//8AAOKe/9///wAAttSwrv//AADTprCu//8AAMW9rJn//wAASCuwrv//AAArWbCu//8AADlCrJn//wAAauMdrjL9AADMAh2uauMAAFkKrJml9QAAlBwdrjL9AAAAAP/fHGEAAAAAsK5IKwAAAACsmTlCAAAAALCuK1kAAAAA/9/ingAAAACwrg1pAAAAAKyZ/38AAAAAsK7xlgAAAACwrtOmAAAAAKyZxb0AAAAAsK621AAAWQqsmVkKAADMAh2ulBwAAP9/rJkAAAAA8ZawrgAAAAANabCuAAAAAOKe/98AAAAAHGH/3wAAAABIK7CuAAAAACtZsK4AAAAAOUKsmQAAAAC21LCuAAAAANOmsK4AAAAAxb2smQAAAACl9ayZWQoAADL9Ha6UHAAAauMdrswCAACUHB2uzAIAAErZAAAMBQAAFvEAAOgOAABK2f+/DAUAABbx/7/oDgAA6A4AAOgOAADoDv+/6A4AAAwFAAC0JgAADAX/v7QmAAAW8QAAFvEAAErZAADy+gAAFvH/vxbxAABK2f+/8voAALQmAADy+gAAtCb/v/L6AADy+gAAStkAAPL6AAC0JgAAtCYAAAwFAADoDgAAFvEAAAwFAABK2QAAtCb/vwwFAADoDv+/FvEAAPL6/79K2QAADAX/v0rZAADy+v+/tCYAAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_dtoc6"]
resource_name = "block-grass-overhang-large_block-grass-overhang-large"
_surfaces = [{
"aabb": AABB(-1.04106, -4.56364e-30, -1.04106, 2.08212, 1, 2.08212),
"attribute_data": PackedByteArray("/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zf/n/83mLn/N/+f/zeYuf83mLn/N5i5/zf/n/83/5//N/+f/zf/n/83mLn/N5i5/zeYuf83mLn/N/+f/zf/n/83mLn/N5i5/zf/n/83/5//N/+f/zf/n/83mLn/N5i5/zeYuf83mLn/N/+f/zf/n/83mLn/N/+f/zeYuf83/5//N5i5/zeYuf83/5//N/+f/zeYuf83/5//N5i5/zf/n/83/5//N5i5/zf/n/83mLn/N5i5/zeYuf83/5//N/+f/zf/n/83ZYb/N/+f/zdlhv83ZYb/N2WG/zf/n/83/5//N/+f/zf/n/83ZYb/N2WG/zf/n/83ZYb/N/+f/zdlhv83/5//N/+f/zdlhv83ZYb/N/+f/zf/n/83ZYb/N2WG/zf/n/83/5//N2WG/zdlhv83/5//N/+f/zdlhv83ZYb/N/+f/zf/n/83ZYb/N2WG/zf/n/83ZYb/N/+f/zdlhv83/5//N/+f/zdlhv83ZYb/N2WG/zf/n/83ZYb/N/+f/zfysf83/5//N5i5/zfysf83/5//N/Kx/zeYuf838rH/N/+f/zf/n/838rH/N5i5/zfysf83/5//Nyey/zf/n/83mLn/N5i5/zfysf838rH/N/+f/zf/n/83/5//N/Kx/zfysf83mLn/N/+f/zfysf838rH/N5i5/zeYuf83J7L/N/+f/zf/n/83/5//N/+f/zcnsv83mLn/N5i5/zf/n/83J7L/N/+f/zf/n/83/5//N/Kx/zeYuf838rH/N/+f/zfysf83mLn/N/Kx/zf/n/838rH/N5i5/zfysf83/5//N/+f/zeYuf83J7L/N5i5/zfysf838rH/N/+f/zf/n/83/5//N/Kx/zfysf83mLn/N/+f/zfysf838rH/N5i5/zf/n/83mLn/N/+f/zcnsv83mLn/N/+f/zcnsv83/5//N5i5/zcnsv83/5//N/+f/7dlxv+3Zcb/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3mPn/t2XG/7dlxv+3mPn/t5j5/7dlxv+3Zcb/t5j5/7eY+f+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7eY+f+3mPn/t2XG/7dlxv+3mPn/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3Zcb/t5j5/7dlxv+3mPn/t2XG/7eY+f+3Zcb/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3Zcb/t2XG/7eY+f+3mPn/t5j5/7dlxv+3mPn/t2XG/zf/n/83/5//N/+f/zf/n/83/5//N5i5/zeYuf83/5//N/+f/zeYuf83/5//N/+f/zf/n/83ZYb/N2WG/zcnsv83J7L/t5j5/7eY+f+3mPn/t5j5/7dlxv+3Zcb/t5j5/7eY+f+3mPn/t5j5/7eY+f+3mPk="),
"format": 34896613399,
"index_count": 432,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABAAGAAUABgAHAAUABgAIAAcACAAJAAcACAAKAAkACgALAAkADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIANgA0ADUANQA3ADYAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIARgBEAEUARQBHAEYASgBIAEkASQBLAEoATgBMAE0ATQBPAE4AUgBQAFEAUQBTAFIAVgBUAFUAVQBXAFYAWgBYAFkAWQBbAFoAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZQBnAGYAagBoAGkAaQBrAGoAbgBsAG0AbwBuAG0AbQBwAG8AcgBxAHAAcwByAHAAcAB0AHMAdAB1AHYAdgB3AHQAdwB4AHQAewB5AHoAegB8AHsAfwB9AH4AfgCAAH8AgACBAH8AgQCCAIMAhACBAIMAgwCFAIQAhwCGAIAAhwCAAIgAiACJAIcAjACKAIsAiwCNAIwAkACOAI8AjwCRAJAAlACSAJMAkwCVAJQAmACWAJcAlwCZAJgAlwCaAJkAnACXAJsAmwCdAJwAmwCeAJ0AoACbAJ8AnwChAKAAnwCiAKEApQCjAKQApACmAKUAqQCnAKgAqACqAKkAqgCrAKkAqwCsAK0ArgCrAK0ArQCvAK4AsQCwAKoAsQCqALIAsgCzALEAtgC0ALUAtQC3ALYAugC4ALkAuQC7ALoAvgC8AL0AvQC/AL4AwgDAAMEAwQDDAMIAxgDEAMUAxQDHAMYAygDIAMkAyQDLAMoAzgDMAM0AzQDPAM4A0gDQANEA0QDTANIA0wDUANIA0wDVANQA1QDWANQA1QDXANYA1wDYANYA1wDZANgA2QDaANgA2QDbANoA3gDcAN0A3QDfAN4A4gDgAOEA4QDjAOIA5gDkAOUA5QDnAOYA6gDoAOkA6QDrAOoA7gDsAO0A7QDvAO4A8gDwAPEA8QDzAPIA9gD0APUA9QD3APYA+gD4APkA+QD7APoA"),
"lods": [1.29755, PackedByteArray("DgAMAA0ADQAPAA4ABQENAAwADgAPAAIBDAATAAUBEwARAAUBtQC3AAwADAATALUAugC1ABMAdAAMAHYAdgB3AHQAdwB4AHQAEQATABIAEgAQABEAVgASABMAEgABARAAcAB0AHMAcwByAHAAcgBxAHAA/wASAFYAAQESAP8A/wAXAAEBpQASAP8AEgClAL0A/wCmAKUAFwD/ACEAIQAjABcAmAD/AJcAlwCZAJgAlwCaAJkAJQAjACEAnACXAJsAmwCdAJwAmwCeAJ0AoACbACEAIQChAKAAIQCiAKEAIQAHASUABwEkACUADAEhAAcBBwGRAAwBlACRAAcBGQAkAAcBBwEDARkAAwECARkAhwAHAYAAhwCAAIgAiACJAIcAfgCAAH8AfwB9AH4AgACBAH8AgQADAYMAhACBAIMAgwCFAIQAAwF8AIsACwF8AAMBAwEOAAsBDgADAWYAAgEDAQ4AbgBsAA4AbwBuAA4ADgBwAG8ABgEAAQkBCQEIAQYBBAEIAQkBBAEJAQoB/QAKAQkBCQH8AP0ACQH+APwAqQCnAKgAqACqAKkAqgCrAKkAqwASAK0ArgCrAK0ArQCvAK4AsQATAKoAsQCqALIAsgCzALEA3AANAcIA3ADCAN4A3ADeAMUAxQDHANwAxwDrABIB6wAUARIBGAENAREBFgEXAREBEQETARABEQEQAQ8BEQEPAQ4BDgEVAREB")],
"material": SubResource("StandardMaterial3D_2voq8"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 281,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_4ngdb")

[sub_resource type="BoxShape3D" id="BoxShape3D_p7nmn"]
size = Vector3(2.04535, 0.953339, 1.5011)

[sub_resource type="BoxShape3D" id="BoxShape3D_x60dp"]
size = Vector3(7.45867, 4.43243, 7.97723)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6g3vb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_4ygdr"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_44lvw"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_i72mq"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_pb55e"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_dm4j0"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_mural"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gu707"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_hb3as"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_rol08"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_4p5qp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_n5mki"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_bd243"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_5ywx8"]
shader = ExtResource("14_pb55e")

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_mg82m"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_emgar"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_qwoen"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_wju7l"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_talwe"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_trfno"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_2o55e"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_01d4o"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_votfh"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_esgkt"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gc4dp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sbc4m"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_0yk8j"]
bg_color = Color(0.345098, 0.345098, 0.345098, 0.6)
corner_radius_top_left = 16
corner_radius_top_right = 16
corner_radius_bottom_right = 16
corner_radius_bottom_left = 16

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_tdn6f"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_hc0rt"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_0eoe8"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_y2d16"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_nv703"]
bg_color = Color(0.345098, 0.345098, 0.345098, 0.6)
corner_radius_top_left = 16
corner_radius_top_right = 16
corner_radius_bottom_right = 16
corner_radius_bottom_left = 16

[node name="ProfileMenu" type="Node3D"]
script = ExtResource("1_5x80j")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_dm4j0")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.887202, -0.423769, 0.182463, -0.0922675, 0.224523, 0.970091, -0.452062, -0.877502, 0.160097, 0, 0, 0)
light_energy = 2.25
light_specular = 2.5

[node name="Cloud" type="MeshInstance3D" parent="."]
transform = Transform3D(50, 0, 0, 0, 50, 0, 0, 0, 50, 3.39157, -53.3244, -0.590649)
material_override = SubResource("ShaderMaterial_pb55e")
cast_shadow = 0
mesh = SubResource("SphereMesh_8y6ae")
skeleton = NodePath("../..")

[node name="Ocean" parent="." instance=ExtResource("2_yym5i")]
transform = Transform3D(8, 0, 0, 0, 8, 0, 0, 0, 8, 0, 0, -187.752)

[node name="block-grass-overhang-large" parent="." instance=ExtResource("4_i0c74")]
transform = Transform3D(3.83022, 0, 3.21393, 0, 5, 0, -3.21393, 0, 3.83022, 0, -1.42524, 0)

[node name="block-grass-overhang-large" parent="block-grass-overhang-large" index="0"]
transform = Transform3D(0.819152, 0, 0.573576, 0, 1, 0, -0.573576, 0, 0.819152, -0.0880596, -3.30687e-05, 0.0379544)
mesh = SubResource("ArrayMesh_dtoc6")

[node name="CollisionShape3D" type="CollisionShape3D" parent="block-grass-overhang-large"]
transform = Transform3D(0.819152, 0, 0.573576, 0, 1, 0, -0.573576, 0, 0.819152, 0.0916301, 0.520737, 0.212595)
shape = SubResource("BoxShape3D_p7nmn")

[node name="Model" parent="block-grass-overhang-large" instance=ExtResource("6_gsbxc")]
unique_name_in_owner = true
transform = Transform3D(0.917725, 0, -1.31064, 0, 1.6, 0, 1.31064, 0, 0.917725, -0.128416, 0.997407, 0.143921)

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 0.997526, 0.0702962, 0, -0.0702962, 0.997526, -12.8059, 9.7188, 17.9242)

[node name="EditCharacter" type="Sprite3D" parent="."]
transform = Transform3D(0.8, 0, 0, 0, 0.8, 0, 0, 0, 0.8, 2.23896, 12, 4)
billboard = 1
texture = ExtResource("7_3i52n")

[node name="Area3D" type="Area3D" parent="EditCharacter"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="EditCharacter/Area3D"]
transform = Transform3D(0.6, 0, 0, 0, 0.6, 0, 0, 0, 0.6, 0.399247, 0.632273, 0.0294824)
shape = SubResource("BoxShape3D_x60dp")

[node name="GUI" type="CanvasLayer" parent="."]
script = ExtResource("8_ae7pf")

[node name="back" type="Button" parent="GUI"]
z_index = 7
offset_left = 48.0
offset_top = 32.0
offset_right = 176.0
offset_bottom = 160.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_6g3vb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_4ygdr")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_44lvw")
theme_override_styles/normal = SubResource("StyleBoxEmpty_i72mq")
icon = ExtResource("9_2d2ru")

[node name="SetSkin" type="Button" parent="GUI"]
visible = false
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -144.0
offset_top = -104.0
offset_right = 144.0
offset_bottom = -32.0
grow_horizontal = 2
grow_vertical = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_pb55e")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_dm4j0")
theme_override_styles/hover = SubResource("StyleBoxEmpty_mural")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_gu707")
theme_override_styles/normal = SubResource("StyleBoxEmpty_hb3as")
icon = ExtResource("10_2d2ru")

[node name="Label" type="Label" parent="GUI/SetSkin"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -128.0
offset_top = -25.5
offset_right = 4.0
offset_bottom = 25.5
grow_horizontal = 2
grow_vertical = 2
theme_override_fonts/font = ExtResource("11_hb3as")
theme_override_font_sizes/font_size = 36
text = "Set Skin "
vertical_alignment = 1

[node name="price_label" type="Label" parent="GUI/SetSkin"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = 24.0
offset_top = -25.5
offset_right = 88.0
offset_bottom = 25.5
grow_horizontal = 2
grow_vertical = 2
theme_override_fonts/font = ExtResource("11_hb3as")
theme_override_font_sizes/font_size = 36
text = "100"
horizontal_alignment = 1
vertical_alignment = 1

[node name="coin_icon" type="TextureRect" parent="GUI/SetSkin"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -56.0
offset_top = -22.0
offset_right = -12.0
offset_bottom = 22.0
grow_horizontal = 0
grow_vertical = 2
texture = ExtResource("13_ae7pf")
expand_mode = 1

[node name="skin_coin" type="TextureRect" parent="GUI"]
visible = false
offset_left = 192.0
offset_top = 56.0
offset_right = 448.0
offset_bottom = 149.0
scale = Vector2(0.8, 0.8)
texture = ExtResource("25_dm4j0")

[node name="skin_coin_value" type="Label" parent="GUI/skin_coin"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -193.5
offset_top = -19.5
offset_right = -7.5
offset_bottom = 18.5
grow_horizontal = 0
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.105882, 0.282353, 0.478431, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 12
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 32
text = "0"
horizontal_alignment = 1

[node name="ChooseCharacter" type="Control" parent="GUI"]
layout_mode = 3
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -780.0
offset_top = -863.0
grow_horizontal = 0
grow_vertical = 0

[node name="CategoryScrollContainer" type="ScrollContainer" parent="GUI/ChooseCharacter"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -780.0
offset_top = -800.0
offset_right = -20.0
grow_horizontal = 0
grow_vertical = 0

[node name="CategoryGrid" type="GridContainer" parent="GUI/ChooseCharacter/CategoryScrollContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/h_separation = 48
theme_override_constants/v_separation = 40
columns = 3

[node name="Shirts" type="Control" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid"]
custom_minimum_size = Vector2(208, 232)
layout_mode = 2
script = ExtResource("10_hb3as")

[node name="TextureRect" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shirts"]
layout_mode = 0
offset_right = 512.0
offset_bottom = 580.0
scale = Vector2(0.4, 0.4)
texture = ExtResource("11_yym5i")

[node name="Button" type="Button" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shirts"]
layout_mode = 0
offset_right = 208.0
offset_bottom = 224.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_rol08")
theme_override_styles/hover = SubResource("StyleBoxEmpty_4p5qp")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_n5mki")
theme_override_styles/normal = SubResource("StyleBoxEmpty_bd243")

[node name="Label" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shirts"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "shirts"

[node name="Price" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shirts"]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Shirts"
horizontal_alignment = 1

[node name="Price2" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shirts"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Shirts"
horizontal_alignment = 1

[node name="coin_icon" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shirts"]
visible = false
layout_mode = 0
offset_left = 117.0
offset_top = 194.0
offset_right = 147.0
offset_bottom = 224.0
texture = ExtResource("13_ae7pf")
expand_mode = 1

[node name="Click" type="AudioStreamPlayer" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shirts"]
unique_name_in_owner = true
stream = ExtResource("16_gu707")

[node name="Pants" type="Control" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid"]
custom_minimum_size = Vector2(208, 232)
layout_mode = 2
script = ExtResource("10_hb3as")

[node name="item" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Pants"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "Pants"

[node name="TextureRect" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Pants"]
layout_mode = 0
offset_right = 512.0
offset_bottom = 580.0
scale = Vector2(0.4, 0.4)
texture = ExtResource("12_yym5i")

[node name="Button" type="Button" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Pants"]
layout_mode = 0
offset_right = 208.0
offset_bottom = 224.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_rol08")
theme_override_styles/hover = SubResource("StyleBoxEmpty_4p5qp")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_n5mki")
theme_override_styles/normal = SubResource("StyleBoxEmpty_bd243")

[node name="Label" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Pants"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "pants"

[node name="Price" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Pants"]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Pants"
horizontal_alignment = 1

[node name="Price2" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Pants"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Pants"
horizontal_alignment = 1

[node name="coin_icon" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Pants"]
visible = false
layout_mode = 0
offset_left = 117.0
offset_top = 194.0
offset_right = 147.0
offset_bottom = 224.0
texture = ExtResource("13_ae7pf")
expand_mode = 1

[node name="Click" type="AudioStreamPlayer" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Pants"]
stream = ExtResource("16_gu707")

[node name="Shoes" type="Control" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid"]
custom_minimum_size = Vector2(208, 232)
layout_mode = 2
script = ExtResource("10_hb3as")

[node name="item" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shoes"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "Shoes"

[node name="TextureRect" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shoes"]
layout_mode = 0
offset_right = 512.0
offset_bottom = 580.0
scale = Vector2(0.4, 0.4)
texture = ExtResource("13_q0gxe")

[node name="Button" type="Button" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shoes"]
layout_mode = 0
offset_right = 208.0
offset_bottom = 224.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_rol08")
theme_override_styles/hover = SubResource("StyleBoxEmpty_4p5qp")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_n5mki")
theme_override_styles/normal = SubResource("StyleBoxEmpty_bd243")

[node name="Label" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shoes"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "shoes"

[node name="Price" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shoes"]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Shoes"
horizontal_alignment = 1

[node name="Price2" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shoes"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Shoes"
horizontal_alignment = 1

[node name="coin_icon" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shoes"]
visible = false
layout_mode = 0
offset_left = 117.0
offset_top = 194.0
offset_right = 147.0
offset_bottom = 224.0
texture = ExtResource("13_ae7pf")
expand_mode = 1

[node name="Click" type="AudioStreamPlayer" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shoes"]
stream = ExtResource("16_gu707")

[node name="Hair" type="Control" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid"]
custom_minimum_size = Vector2(208, 232)
layout_mode = 2
script = ExtResource("10_hb3as")

[node name="item" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hair"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "Shoes"

[node name="TextureRect" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hair"]
layout_mode = 0
offset_right = 512.0
offset_bottom = 580.0
scale = Vector2(0.4, 0.4)
texture = ExtResource("14_i0c74")

[node name="Button" type="Button" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hair"]
layout_mode = 0
offset_right = 208.0
offset_bottom = 224.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_rol08")
theme_override_styles/hover = SubResource("StyleBoxEmpty_4p5qp")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_n5mki")
theme_override_styles/normal = SubResource("StyleBoxEmpty_bd243")

[node name="Label" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hair"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "hair"

[node name="Price" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hair"]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Hair"
horizontal_alignment = 1

[node name="Price2" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hair"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Hair"
horizontal_alignment = 1

[node name="coin_icon" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hair"]
visible = false
layout_mode = 0
offset_left = 117.0
offset_top = 194.0
offset_right = 147.0
offset_bottom = 224.0
texture = ExtResource("13_ae7pf")
expand_mode = 1

[node name="Click" type="AudioStreamPlayer" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hair"]
stream = ExtResource("16_gu707")

[node name="Hat" type="Control" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid"]
custom_minimum_size = Vector2(208, 232)
layout_mode = 2
script = ExtResource("10_hb3as")

[node name="item" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hat"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "Shoes"

[node name="TextureRect" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hat"]
layout_mode = 0
offset_right = 512.0
offset_bottom = 580.0
scale = Vector2(0.4, 0.4)
texture = ExtResource("15_e5ac4")

[node name="Button" type="Button" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hat"]
layout_mode = 0
offset_right = 208.0
offset_bottom = 224.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_rol08")
theme_override_styles/hover = SubResource("StyleBoxEmpty_4p5qp")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_n5mki")
theme_override_styles/normal = SubResource("StyleBoxEmpty_bd243")

[node name="Label" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hat"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "hat"

[node name="Price" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hat"]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Hat"
horizontal_alignment = 1

[node name="Price2" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hat"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Hat"
horizontal_alignment = 1

[node name="coin_icon" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hat"]
visible = false
layout_mode = 0
offset_left = 117.0
offset_top = 194.0
offset_right = 147.0
offset_bottom = 224.0
texture = ExtResource("13_ae7pf")
expand_mode = 1

[node name="Click" type="AudioStreamPlayer" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hat"]
stream = ExtResource("16_gu707")

[node name="Gloves" type="Control" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid"]
custom_minimum_size = Vector2(208, 232)
layout_mode = 2
script = ExtResource("10_hb3as")

[node name="item" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Gloves"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "Shoes"

[node name="TextureRect" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Gloves"]
layout_mode = 0
offset_right = 512.0
offset_bottom = 580.0
scale = Vector2(0.4, 0.4)
texture = ExtResource("16_gsbxc")

[node name="Button" type="Button" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Gloves"]
layout_mode = 0
offset_right = 208.0
offset_bottom = 224.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_rol08")
theme_override_styles/hover = SubResource("StyleBoxEmpty_4p5qp")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_n5mki")
theme_override_styles/normal = SubResource("StyleBoxEmpty_bd243")

[node name="Label" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Gloves"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "gloves"

[node name="Price" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Gloves"]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Gloves"
horizontal_alignment = 1

[node name="Price2" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Gloves"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Gloves"
horizontal_alignment = 1

[node name="coin_icon" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Gloves"]
visible = false
layout_mode = 0
offset_left = 117.0
offset_top = 194.0
offset_right = 147.0
offset_bottom = 224.0
texture = ExtResource("13_ae7pf")
expand_mode = 1

[node name="Click" type="AudioStreamPlayer" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Gloves"]
stream = ExtResource("16_gu707")

[node name="Glasses" type="Control" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid"]
custom_minimum_size = Vector2(208, 232)
layout_mode = 2
script = ExtResource("10_hb3as")

[node name="item" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Glasses"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "Shoes"

[node name="TextureRect" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Glasses"]
layout_mode = 0
offset_right = 512.0
offset_bottom = 580.0
scale = Vector2(0.4, 0.4)
texture = ExtResource("17_3i52n")

[node name="Button" type="Button" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Glasses"]
layout_mode = 0
offset_right = 208.0
offset_bottom = 224.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_rol08")
theme_override_styles/hover = SubResource("StyleBoxEmpty_4p5qp")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_n5mki")
theme_override_styles/normal = SubResource("StyleBoxEmpty_bd243")

[node name="Label" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Glasses"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "glasses"

[node name="Price" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Glasses"]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Glasses"
horizontal_alignment = 1

[node name="Price2" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Glasses"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Glasses"
horizontal_alignment = 1

[node name="coin_icon" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Glasses"]
visible = false
layout_mode = 0
offset_left = 117.0
offset_top = 194.0
offset_right = 147.0
offset_bottom = 224.0
texture = ExtResource("13_ae7pf")
expand_mode = 1

[node name="Click" type="AudioStreamPlayer" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Glasses"]
stream = ExtResource("16_gu707")

[node name="Emotion" type="Control" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid"]
custom_minimum_size = Vector2(208, 232)
layout_mode = 2
script = ExtResource("10_hb3as")

[node name="item" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Emotion"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "Shoes"

[node name="TextureRect" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Emotion"]
layout_mode = 0
offset_right = 512.0
offset_bottom = 580.0
scale = Vector2(0.4, 0.4)
texture = ExtResource("18_ae7pf")

[node name="Button" type="Button" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Emotion"]
layout_mode = 0
offset_right = 208.0
offset_bottom = 224.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_rol08")
theme_override_styles/hover = SubResource("StyleBoxEmpty_4p5qp")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_n5mki")
theme_override_styles/normal = SubResource("StyleBoxEmpty_bd243")

[node name="Label" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Emotion"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "emotion"

[node name="Price" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Emotion"]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Emotion"
horizontal_alignment = 1

[node name="Price2" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Emotion"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Emotion"
horizontal_alignment = 1

[node name="coin_icon" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Emotion"]
visible = false
layout_mode = 0
offset_left = 117.0
offset_top = 194.0
offset_right = 147.0
offset_bottom = 224.0
texture = ExtResource("13_ae7pf")
expand_mode = 1

[node name="Click" type="AudioStreamPlayer" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Emotion"]
stream = ExtResource("16_gu707")

[node name="Mustache" type="Control" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid"]
custom_minimum_size = Vector2(208, 232)
layout_mode = 2
script = ExtResource("10_hb3as")

[node name="item" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Mustache"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "Shoes"

[node name="TextureRect" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Mustache"]
layout_mode = 0
offset_right = 512.0
offset_bottom = 580.0
scale = Vector2(0.4, 0.4)
texture = ExtResource("19_ae7pf")

[node name="Button" type="Button" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Mustache"]
layout_mode = 0
offset_right = 208.0
offset_bottom = 224.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_rol08")
theme_override_styles/hover = SubResource("StyleBoxEmpty_4p5qp")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_n5mki")
theme_override_styles/normal = SubResource("StyleBoxEmpty_bd243")

[node name="Label" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Mustache"]
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 23.0
text = "mustache"

[node name="Price" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Mustache"]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Mustache"
horizontal_alignment = 1

[node name="Price2" type="Label" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Mustache"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -58.0
offset_top = -42.0
offset_right = 54.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 0
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 30
text = "Mustache"
horizontal_alignment = 1

[node name="coin_icon" type="TextureRect" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Mustache"]
visible = false
layout_mode = 0
offset_left = 117.0
offset_top = 194.0
offset_right = 147.0
offset_bottom = 224.0
texture = ExtResource("13_ae7pf")
expand_mode = 1

[node name="Click" type="AudioStreamPlayer" parent="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Mustache"]
stream = ExtResource("16_gu707")

[node name="ItemScrollContainer" type="ScrollContainer" parent="GUI/ChooseCharacter"]
unique_name_in_owner = true
visible = false
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -780.0
offset_top = -800.0
offset_right = -20.0
grow_horizontal = 0
grow_vertical = 0
vertical_scroll_mode = 3

[node name="ItemGrid" type="GridContainer" parent="GUI/ChooseCharacter/ItemScrollContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/h_separation = 48
theme_override_constants/v_separation = 32
columns = 3

[node name="Profile" type="Control" parent="GUI"]
layout_mode = 3
anchors_preset = 0
offset_left = 163.0
offset_right = 1064.0
offset_bottom = 864.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ProfilePicture" type="TextureRect" parent="GUI/Profile"]
material = SubResource("ShaderMaterial_5ywx8")
layout_mode = 1
anchors_preset = -1
anchor_left = -0.177
anchor_right = -0.177
offset_left = 175.477
offset_top = 71.0
offset_right = 1199.48
offset_bottom = 1095.0
scale = Vector2(0.3, 0.3)
texture = ExtResource("15_8y6ae")
expand_mode = 2

[node name="ChangeProfile" type="Button" parent="GUI/Profile"]
layout_mode = 1
anchors_preset = -1
anchor_left = -0.178
anchor_right = -0.178
offset_left = 403.378
offset_top = 9.0
offset_right = 959.378
offset_bottom = 517.0
scale = Vector2(0.25, 0.25)
theme_override_styles/focus = SubResource("StyleBoxEmpty_mg82m")
theme_override_styles/hover = SubResource("StyleBoxEmpty_emgar")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_qwoen")
theme_override_styles/normal = SubResource("StyleBoxEmpty_wju7l")
icon = ExtResource("7_3i52n")
icon_alignment = 1

[node name="TextureRect2" type="TextureRect" parent="GUI/Profile"]
layout_mode = 1
anchors_preset = -1
anchor_left = -0.183
anchor_right = -0.183
offset_left = 565.883
offset_top = 88.0
offset_right = 1457.64
offset_bottom = 303.0
scale = Vector2(0.55, 0.55)
texture = ExtResource("17_hu0nb")
expand_mode = 2

[node name="EditUserName" type="Button" parent="GUI/Profile/TextureRect2"]
layout_mode = 1
anchors_preset = -1
anchor_left = 1.0
anchor_top = 0.449
anchor_right = 1.0
anchor_bottom = 0.449
offset_left = -166.76
offset_top = -79.535
offset_right = -9.76001
offset_bottom = 77.465
grow_horizontal = 0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_talwe")
theme_override_styles/hover = SubResource("StyleBoxEmpty_trfno")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_2o55e")
theme_override_styles/normal = SubResource("StyleBoxEmpty_01d4o")
icon = ExtResource("18_wq64q")
icon_alignment = 1

[node name="CopyUserName" type="Button" parent="GUI/Profile/TextureRect2"]
layout_mode = 1
anchors_preset = -1
anchor_top = 0.449
anchor_bottom = 0.449
offset_left = 43.75
offset_top = -44.535
offset_right = 131.75
offset_bottom = 43.465
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_votfh")
theme_override_styles/hover = SubResource("StyleBoxEmpty_esgkt")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_gc4dp")
theme_override_styles/normal = SubResource("StyleBoxEmpty_sbc4m")
icon = ExtResource("19_6wvqk")

[node name="Copy_Check" type="Button" parent="GUI/Profile/TextureRect2"]
layout_mode = 1
anchors_preset = -1
anchor_top = 0.449
anchor_bottom = 0.449
offset_left = 43.75
offset_top = -44.535
offset_right = 131.75
offset_bottom = 43.465
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_votfh")
theme_override_styles/hover = SubResource("StyleBoxEmpty_esgkt")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_gc4dp")
theme_override_styles/normal = SubResource("StyleBoxEmpty_sbc4m")
icon = ExtResource("20_17dce")

[node name="UserName" type="Label" parent="GUI/Profile/TextureRect2"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -192.5
offset_top = -55.0
offset_right = 192.5
offset_bottom = 37.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 20
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 80
text = "Nima Belak"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Coin_img" type="TextureRect" parent="GUI/Profile/TextureRect2"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.461
anchor_top = 1.0
anchor_right = 0.461
anchor_bottom = 1.0
offset_left = -486.212
offset_top = -6.25002
offset_right = 471.788
offset_bottom = 177.75
grow_horizontal = 2
grow_vertical = 0
texture = ExtResource("22_k7six")

[node name="coin" type="Label" parent="GUI/Profile/TextureRect2/Coin_img"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.573
anchor_top = 0.5
anchor_right = 0.573
anchor_bottom = 0.5
offset_left = -350.187
offset_top = -37.1137
offset_right = 398.398
offset_bottom = 37.8863
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 64
horizontal_alignment = 1
vertical_alignment = 1

[node name="Cup_img" type="TextureRect" parent="GUI/Profile/TextureRect2"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -453.5
offset_top = 187.5
offset_right = 438.5
offset_bottom = 339.5
grow_horizontal = 2
grow_vertical = 0
texture = ExtResource("23_eamum")

[node name="cup" type="Label" parent="GUI/Profile/TextureRect2/Cup_img"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.567
anchor_top = 0.5
anchor_right = 0.567
anchor_bottom = 0.5
offset_left = -347.631
offset_top = -29.4091
offset_right = 347.311
offset_bottom = 29.4091
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 64
horizontal_alignment = 1
vertical_alignment = 1

[node name="Bio" type="Panel" parent="GUI/Profile"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_top = 0.47
anchor_right = 0.988
anchor_bottom = 0.7
offset_left = 0.0990152
offset_top = -0.0800171
offset_right = 380.812
offset_bottom = 85.2
scale = Vector2(0.7, 0.7)
theme_override_styles/panel = SubResource("StyleBoxFlat_0yk8j")

[node name="Title" type="Label" parent="GUI/Profile/Bio"]
layout_mode = 0
offset_left = 25.0
offset_top = 13.0
offset_right = 143.0
offset_bottom = 88.0
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 14
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 60
text = "Bio :"

[node name="ChangeBio" type="Button" parent="GUI/Profile/Bio"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -140.0
offset_top = -125.0
offset_right = 56.0
offset_bottom = 52.0
grow_horizontal = 0
grow_vertical = 0
scale = Vector2(0.7, 0.7)
theme_override_styles/focus = SubResource("StyleBoxEmpty_tdn6f")
theme_override_styles/hover = SubResource("StyleBoxEmpty_hc0rt")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_0eoe8")
theme_override_styles/normal = SubResource("StyleBoxEmpty_y2d16")
icon = ExtResource("24_5gxp2")
icon_alignment = 1

[node name="Bio" type="Label" parent="GUI/Profile/Bio"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.48
anchor_top = 0.576
anchor_right = 0.48
anchor_bottom = 0.576
offset_left = -479.6
offset_top = -79.584
offset_right = 480.4
offset_bottom = 79.416
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 44
text = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt."
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="Panel2" type="Panel" parent="GUI/Profile"]
layout_mode = 1
anchors_preset = -1
anchor_top = 0.714
anchor_right = 0.99
anchor_bottom = 0.955
offset_left = 1.52588e-05
offset_top = 0.103943
offset_right = 382.01
offset_bottom = 88.8799
scale = Vector2(0.7, 0.7)
theme_override_styles/panel = SubResource("StyleBoxFlat_nv703")

[node name="VBoxContainer" type="VBoxContainer" parent="GUI/Profile/Panel2"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 23.0
offset_top = -93.0
offset_right = 230.0
offset_bottom = 93.0
grow_vertical = 2
theme_override_constants/separation = 24

[node name="kol" type="Label" parent="GUI/Profile/Panel2/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 14
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 44
text = "Games Played :"

[node name="win" type="Label" parent="GUI/Profile/Panel2/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 14
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 44
text = "Games Won :"

[node name="age" type="Label" parent="GUI/Profile/Panel2/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 14
theme_override_fonts/font = ExtResource("21_kyv72")
theme_override_font_sizes/font_size = 44
text = "Account Age :"

[node name="SkinChange" type="AudioStreamPlayer" parent="."]
unique_name_in_owner = true
stream = ExtResource("35_mural")

[connection signal="input_event" from="EditCharacter/Area3D" to="." method="_on_area_3d_input_event"]
[connection signal="pressed" from="GUI/back" to="." method="_on_back_pressed"]
[connection signal="pressed" from="GUI/SetSkin" to="." method="_on_button_pressed"]
[connection signal="pressed" from="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shirts/Button" to="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shirts" method="_on_button_pressed"]
[connection signal="pressed" from="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Pants/Button" to="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Pants" method="_on_button_pressed"]
[connection signal="pressed" from="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shoes/Button" to="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Shoes" method="_on_button_pressed"]
[connection signal="pressed" from="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hair/Button" to="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hair" method="_on_button_pressed"]
[connection signal="pressed" from="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hat/Button" to="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Hat" method="_on_button_pressed"]
[connection signal="pressed" from="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Gloves/Button" to="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Gloves" method="_on_button_pressed"]
[connection signal="pressed" from="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Glasses/Button" to="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Glasses" method="_on_button_pressed"]
[connection signal="pressed" from="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Emotion/Button" to="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Emotion" method="_on_button_pressed"]
[connection signal="pressed" from="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Mustache/Button" to="GUI/ChooseCharacter/CategoryScrollContainer/CategoryGrid/Mustache" method="_on_button_pressed"]
[connection signal="pressed" from="GUI/Profile/ChangeProfile" to="." method="_on_change_profile_pressed"]
[connection signal="pressed" from="GUI/Profile/TextureRect2/EditUserName" to="." method="_on_edit_user_name_pressed"]
[connection signal="pressed" from="GUI/Profile/TextureRect2/CopyUserName" to="." method="_on_copy_user_name_pressed"]
[connection signal="pressed" from="GUI/Profile/Bio/ChangeBio" to="." method="_on_change_bio_pressed"]

[editable path="block-grass-overhang-large"]
