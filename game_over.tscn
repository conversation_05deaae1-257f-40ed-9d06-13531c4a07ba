[gd_scene load_steps=42 format=4 uid="uid://cy22ttd61kqqd"]

[ext_resource type="Script" uid="uid://cwbmy7dmv820o" path="res://game_over.gd" id="1_ftari"]
[ext_resource type="Script" uid="uid://b3yrpkeq1kwwf" path="res://world_environment.gd" id="2_dd2jf"]
[ext_resource type="PackedScene" uid="uid://uy4saus24gv8" path="res://scenes/Utils/ocean.tscn" id="2_pi44w"]
[ext_resource type="PackedScene" uid="uid://crnq1makno6d1" path="res://assets/Kenny/block-grass-overhang-large.glb" id="3_x5j47"]
[ext_resource type="Texture2D" uid="uid://bymde2j4vycbh" path="res://assets/Kenny/Textures/colormap.png" id="4_lek4k"]
[ext_resource type="PackedScene" uid="uid://cxc3n0k7aovh8" path="res://scenes/NewPlayer/model/model.tscn" id="5_28kla"]
[ext_resource type="Texture2D" uid="uid://bg2qtboledbqj" path="res://assets/new/defeat_card.png" id="5_ku74v"]
[ext_resource type="FontFile" uid="uid://cdh63neq4ginm" path="res://addons/toastparty/fonts/Light.ttf" id="6_28vn7"]
[ext_resource type="Texture2D" uid="uid://cq578g0xksuv2" path="res://assets/new/button_green_empty.png" id="8_745m4"]
[ext_resource type="Texture2D" uid="uid://dakvwcdctrspd" path="res://assets/new/cup_menu2.png" id="9_745m4"]
[ext_resource type="Texture2D" uid="uid://bsph3tbuvvu2p" path="res://assets/new/button_red_empty.png" id="9_kjrwi"]
[ext_resource type="Texture2D" uid="uid://bqc52fdjnjdt6" path="res://assets/new/coin_without_plus.png" id="10_kjrwi"]
[ext_resource type="FontFile" uid="uid://b14qykl1gecp1" path="res://assets/icons/ranking/Light.ttf" id="12_nm4t2"]
[ext_resource type="Texture2D" uid="uid://dckdyxwukw2t6" path="res://assets/new/ChatGPT Image Jul 23, 2025, 10_56_15 AM 1.png" id="13_745m4"]

[sub_resource type="Gradient" id="Gradient_a3qwo"]
offsets = PackedFloat32Array(0.556548, 0.967262)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_4qykk"]
seed = 1
frequency = 0.035

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_dd2jf"]
seamless = true
color_ramp = SubResource("Gradient_a3qwo")
noise = SubResource("FastNoiseLite_4qykk")

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_vqd0t"]
sky_top_color = Color(0, 0.415686, 1, 1)
sky_horizon_color = Color(0.388235, 0.819608, 1, 1)
sky_cover = SubResource("NoiseTexture2D_dd2jf")
ground_bottom_color = Color(0.388235, 0.819608, 1, 1)
ground_horizon_color = Color(0.388235, 0.819608, 1, 1)

[sub_resource type="Sky" id="Sky_ku74v"]
sky_material = SubResource("ProceduralSkyMaterial_vqd0t")
radiance_size = 1

[sub_resource type="Environment" id="Environment_op7wq"]
background_mode = 2
sky = SubResource("Sky_ku74v")
sky_custom_fov = 80.0
ambient_light_source = 3
ambient_light_color = Color(1, 0.718667, 0.597296, 1)
ambient_light_sky_contribution = 0.56
tonemap_mode = 2
glow_enabled = true

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_y0fv6"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3u7xm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_woa3c"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_nm4t2"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_dd2jf"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ku74v"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_745m4"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jhou3"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3xhsb"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_7eg23"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_0o5rp"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_didoo"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_kjrwi"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_olga5"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_13ri0"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_lmfuw"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_k7c8f"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_2voq8"]
resource_name = "colormap"
cull_mode = 2
albedo_color = Color(0.601223, 0.601223, 0.601223, 1)
albedo_texture = ExtResource("4_lek4k")
texture_filter = 2

[sub_resource type="ArrayMesh" id="ArrayMesh_4ngdb"]
_surfaces = [{
"aabb": AABB(-1.04106, -4.56364e-30, -1.04106, 2.08212, 1, 2.08212),
"format": 34896613377,
"index_count": 432,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABAAGAAUABgAHAAUABgAIAAcACAAJAAcACAAKAAkACgALAAkADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAEwARABwAHAAdABMAFwAVAB4AHgAfABcAIgAgACEAIQAjACIAGQAgACIAIgAbABkAFAAWABAAEAASABQAIQAfAB4AHgAjACEADAAdABwAHAANAAwADgAPABgAGAAaAA4AHgAVAAsACwAKAB4AHgAKAAgACAAjAB4ABgAiACMAIwAIAAYAGwAiAAYABgAEABsAAgAMAA4ADgAAAAIABQATAB0AHQADAAUABwASABMAEwAFAAcAAwAdAAwADAACAAMACQAUABIAEgAHAAkAFQAUAAkACQALABUAAQAaABsAGwAEAAEAAQAAAA4ADgAaAAEAJQAkAA4AJgAlAA4ADgAnACYAKQAoACcAKgApACcAJwArACoAKwAMACwALAAtACsALQAuACsAGgAOAC8ALwAwABoAMwAxADIAMgA0ADMANAA1ADMANQAbADYANwA1ADYANgA4ADcAOQAiADQAOQA0ADoAOgA7ADkAGgAwADwAPAAbABoAPQAeACMAIwA+AD0APwA+ACMAIwAiAD8AQQAVAEAAQABCAEEAQABDAEIARQBAAEQARABGAEUARABHAEYASABEAB4AHgBJAEgAHgBKAEkASwAUABUAFQBMAEsATwBNAE4ATgBQAE8AUABRAE8AUQASAFIAUwBRAFIAUgBUAFMAVQATAFAAVQBQAFYAVgBXAFUADAAdAFgAWABZAAwAWgBYAB0AHQATAFoAFABLAFsAWwASABQAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZQBnAGYAZwBlAGgAaABpAGcAZABqAGsAawBdAGQAXQBlAGQAXQBcAGUAXABoAGUAXABsAGgAbABtAGgAbABgAG0AYABuAG0AYABiAG4AbwBsAFwAXABeAG8AaQBoAG0AbQBwAGkAZgBxAGoAagBkAGYAbgBiAGMAYwByAG4AbQBuAHIAcgBwAG0AcQBzAGsAawBqAHEAYQBgAGwAbABvAGEAcwBfAF0AXQBrAHMA"),
"lods": [1.29755, PackedByteArray("DgAMAA0ADQAPAA4AHAANAAwADgAPABgADAATABwAEwARABwAWABZAAwADAATAFgAWgBYABMAKwAMACwALAAtACsALQAuACsAEQATABIAEgAQABEABwASABMAEgAWABAAJwArACoAKgApACcAKQAoACcAFQASAAcAFgASABUAFQAXABYASwASABUAEgBLAFsAFQBMAEsAFwAVAB4AHgAfABcAQQAVAEAAQABCAEEAQABDAEIAIQAfAB4ARQBAAEQARABGAEUARABHAEYASABEAB4AHgBJAEgAHgBKAEkAHgAiACEAIgAgACEAPQAeACIAIgA+AD0APwA+ACIAGQAgACIAIgAbABkAGwAYABkAOQAiADQAOQA0ADoAOgA7ADkAMgA0ADMAMwAxADIANAA1ADMANQAbADYANwA1ADYANgA4ADcAGwAwADwALwAwABsAGwAOAC8ADgAbAAEAGAAbAA4AJQAkAA4AJgAlAA4ADgAnACYAHgAVAAcABwAiAB4AGwAiAAcAGwAHAAEADgABAAcABwAMAA4ABwATAAwATwBNAE4ATgBQAE8AUABRAE8AUQASAFIAUwBRAFIAUgBUAFMAVQATAFAAVQBQAFYAVgBXAFUAbABfAF4AbABeAG8AbABvAGEAYQBjAGwAYwByAGwAcgBwAGwAcwBfAGwAcQBzAGwAbABwAGkAbABpAGcAbABnAGYAZgBxAGwA")],
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 116,
"vertex_data": PackedByteArray("8vr//0rZAAAW8f//FvEAAPL6//+0JgAAFvH//+gOAABK2f//8voAAErZ//8MBQAAtCb///L6AAC0Jv//DAUAAOgO//8W8QAA6A7//+gOAAAMBf//StkAAAwF//+0JgAA////31cjAADy+v+/tCYAAP///9+n3AAA8vr/v0rZAAC0Jv+/DAUAAErZ/78MBQAAVyP/3wAAAACn3P/fAAAAAFkK/99ZCgAAAAD/31cjAADoDv+/6A4AAAwF/7+0JgAAFvH/vxbxAABK2f+/8voAAKX1/9+l9QAAp9z/3///AAAW8f+/6A4AAKX1/99ZCgAAAAD/36fcAAAMBf+/StkAALQm/7/y+gAA6A7/vxbxAABXI//f//8AAFkK/9+l9QAA//+wrrbUAAD//6yZxb0AAP//sK7TpgAA////3+KeAAD//7Cu8ZYAAP//rJn/fwAA//+wrg1pAAD////fHGEAAP//sK5IKwAA//+smTlCAAD//7CuK1kAADL9Ha5q4wAApfWsmaX1AAD/f6yZ//8AAA1psK7//wAA8Zawrv//AAAcYf/f//8AAOKe/9///wAAttSwrv//AADTprCu//8AAMW9rJn//wAASCuwrv//AAArWbCu//8AADlCrJn//wAAauMdrjL9AADMAh2uauMAAFkKrJml9QAAlBwdrjL9AAAAAP/fHGEAAAAAsK5IKwAAAACsmTlCAAAAALCuK1kAAAAA/9/ingAAAACwrg1pAAAAAKyZ/38AAAAAsK7xlgAAAACwrtOmAAAAAKyZxb0AAAAAsK621AAAWQqsmVkKAADMAh2ulBwAAP9/rJkAAAAA8ZawrgAAAAANabCuAAAAAOKe/98AAAAAHGH/3wAAAABIK7CuAAAAACtZsK4AAAAAOUKsmQAAAAC21LCuAAAAANOmsK4AAAAAxb2smQAAAACl9ayZWQoAADL9Ha6UHAAAauMdrswCAACUHB2uzAIAAErZAAAMBQAAFvEAAOgOAABK2f+/DAUAABbx/7/oDgAA6A4AAOgOAADoDv+/6A4AAAwFAAC0JgAADAX/v7QmAAAW8QAAFvEAAErZAADy+gAAFvH/vxbxAABK2f+/8voAALQmAADy+gAAtCb/v/L6AADy+gAAStkAAPL6AAC0JgAAtCYAAAwFAADoDgAAFvEAAAwFAABK2QAAtCb/vwwFAADoDv+/FvEAAPL6/79K2QAADAX/v0rZAADy+v+/tCYAAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_nm4t2"]
resource_name = "block-grass-overhang-large_block-grass-overhang-large"
_surfaces = [{
"aabb": AABB(-1.04106, -4.56364e-30, -1.04106, 2.08212, 1, 2.08212),
"attribute_data": PackedByteArray("/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zdlhv83ZYb/N2WG/zf/n/83mLn/N/+f/zeYuf83mLn/N5i5/zf/n/83/5//N/+f/zf/n/83mLn/N5i5/zeYuf83mLn/N/+f/zf/n/83mLn/N5i5/zf/n/83/5//N/+f/zf/n/83mLn/N5i5/zeYuf83mLn/N/+f/zf/n/83mLn/N/+f/zeYuf83/5//N5i5/zeYuf83/5//N/+f/zeYuf83/5//N5i5/zf/n/83/5//N5i5/zf/n/83mLn/N5i5/zeYuf83/5//N/+f/zf/n/83ZYb/N/+f/zdlhv83ZYb/N2WG/zf/n/83/5//N/+f/zf/n/83ZYb/N2WG/zf/n/83ZYb/N/+f/zdlhv83/5//N/+f/zdlhv83ZYb/N/+f/zf/n/83ZYb/N2WG/zf/n/83/5//N2WG/zdlhv83/5//N/+f/zdlhv83ZYb/N/+f/zf/n/83ZYb/N2WG/zf/n/83ZYb/N/+f/zdlhv83/5//N/+f/zdlhv83ZYb/N2WG/zf/n/83ZYb/N/+f/zfysf83/5//N5i5/zfysf83/5//N/Kx/zeYuf838rH/N/+f/zf/n/838rH/N5i5/zfysf83/5//Nyey/zf/n/83mLn/N5i5/zfysf838rH/N/+f/zf/n/83/5//N/Kx/zfysf83mLn/N/+f/zfysf838rH/N5i5/zeYuf83J7L/N/+f/zf/n/83/5//N/+f/zcnsv83mLn/N5i5/zf/n/83J7L/N/+f/zf/n/83/5//N/Kx/zeYuf838rH/N/+f/zfysf83mLn/N/Kx/zf/n/838rH/N5i5/zfysf83/5//N/+f/zeYuf83J7L/N5i5/zfysf838rH/N/+f/zf/n/83/5//N/Kx/zfysf83mLn/N/+f/zfysf838rH/N5i5/zf/n/83mLn/N/+f/zcnsv83mLn/N/+f/zcnsv83/5//N5i5/zcnsv83/5//N/+f/7dlxv+3Zcb/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3mPn/t2XG/7dlxv+3mPn/t5j5/7dlxv+3Zcb/t5j5/7eY+f+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7dlxv+3Zcb/t2XG/7eY+f+3mPn/t2XG/7dlxv+3mPn/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3Zcb/t5j5/7dlxv+3mPn/t2XG/7eY+f+3Zcb/t5j5/7eY+f+3Zcb/t5j5/7dlxv+3Zcb/t2XG/7eY+f+3mPn/t5j5/7dlxv+3mPn/t2XG/zf/n/83/5//N/+f/zf/n/83/5//N5i5/zeYuf83/5//N/+f/zeYuf83/5//N/+f/zf/n/83ZYb/N2WG/zcnsv83J7L/t5j5/7eY+f+3mPn/t5j5/7dlxv+3Zcb/t5j5/7eY+f+3mPn/t5j5/7eY+f+3mPk="),
"format": 34896613399,
"index_count": 432,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABAAGAAUABgAHAAUABgAIAAcACAAJAAcACAAKAAkACgALAAkADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIANgA0ADUANQA3ADYAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIARgBEAEUARQBHAEYASgBIAEkASQBLAEoATgBMAE0ATQBPAE4AUgBQAFEAUQBTAFIAVgBUAFUAVQBXAFYAWgBYAFkAWQBbAFoAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZQBnAGYAagBoAGkAaQBrAGoAbgBsAG0AbwBuAG0AbQBwAG8AcgBxAHAAcwByAHAAcAB0AHMAdAB1AHYAdgB3AHQAdwB4AHQAewB5AHoAegB8AHsAfwB9AH4AfgCAAH8AgACBAH8AgQCCAIMAhACBAIMAgwCFAIQAhwCGAIAAhwCAAIgAiACJAIcAjACKAIsAiwCNAIwAkACOAI8AjwCRAJAAlACSAJMAkwCVAJQAmACWAJcAlwCZAJgAlwCaAJkAnACXAJsAmwCdAJwAmwCeAJ0AoACbAJ8AnwChAKAAnwCiAKEApQCjAKQApACmAKUAqQCnAKgAqACqAKkAqgCrAKkAqwCsAK0ArgCrAK0ArQCvAK4AsQCwAKoAsQCqALIAsgCzALEAtgC0ALUAtQC3ALYAugC4ALkAuQC7ALoAvgC8AL0AvQC/AL4AwgDAAMEAwQDDAMIAxgDEAMUAxQDHAMYAygDIAMkAyQDLAMoAzgDMAM0AzQDPAM4A0gDQANEA0QDTANIA0wDUANIA0wDVANQA1QDWANQA1QDXANYA1wDYANYA1wDZANgA2QDaANgA2QDbANoA3gDcAN0A3QDfAN4A4gDgAOEA4QDjAOIA5gDkAOUA5QDnAOYA6gDoAOkA6QDrAOoA7gDsAO0A7QDvAO4A8gDwAPEA8QDzAPIA9gD0APUA9QD3APYA+gD4APkA+QD7APoA"),
"lods": [1.29755, PackedByteArray("DgAMAA0ADQAPAA4ABQENAAwADgAPAAIBDAATAAUBEwARAAUBtQC3AAwADAATALUAugC1ABMAdAAMAHYAdgB3AHQAdwB4AHQAEQATABIAEgAQABEAVgASABMAEgABARAAcAB0AHMAcwByAHAAcgBxAHAA/wASAFYAAQESAP8A/wAXAAEBpQASAP8AEgClAL0A/wCmAKUAFwD/ACEAIQAjABcAmAD/AJcAlwCZAJgAlwCaAJkAJQAjACEAnACXAJsAmwCdAJwAmwCeAJ0AoACbACEAIQChAKAAIQCiAKEAIQAHASUABwEkACUADAEhAAcBBwGRAAwBlACRAAcBGQAkAAcBBwEDARkAAwECARkAhwAHAYAAhwCAAIgAiACJAIcAfgCAAH8AfwB9AH4AgACBAH8AgQADAYMAhACBAIMAgwCFAIQAAwF8AIsACwF8AAMBAwEOAAsBDgADAWYAAgEDAQ4AbgBsAA4AbwBuAA4ADgBwAG8ABgEAAQkBCQEIAQYBBAEIAQkBBAEJAQoB/QAKAQkBCQH8AP0ACQH+APwAqQCnAKgAqACqAKkAqgCrAKkAqwASAK0ArgCrAK0ArQCvAK4AsQATAKoAsQCqALIAsgCzALEA3AANAcIA3ADCAN4A3ADeAMUAxQDHANwAxwDrABIB6wAUARIBGAENAREBFgEXAREBEQETARABEQEQAQ8BEQEPAQ4BDgEVAREB")],
"material": SubResource("StandardMaterial3D_2voq8"),
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 281,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_4ngdb")

[sub_resource type="BoxShape3D" id="BoxShape3D_745m4"]
size = Vector3(2.04535, 0.953339, 1.5011)

[node name="GameOver" type="Node3D"]
script = ExtResource("1_ftari")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_op7wq")
script = ExtResource("2_dd2jf")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.887202, -0.423769, 0.182463, -0.0922677, 0.224523, 0.970091, -0.452062, -0.877502, 0.160097, 0, 0, 0)
light_energy = 2.0
light_angular_distance = 2.07

[node name="Ocean" parent="." instance=ExtResource("2_pi44w")]
transform = Transform3D(4.61411, 0, 0, 0, 4.61411, 0, 0, 0, 4.61411, 0, 0, -187.752)

[node name="Camera3D" type="Camera3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 0.984808, 0.173648, 0, -0.173648, 0.984808, 0, 9.0944, 15.8283)
fov = 70.0

[node name="CountDownTimer" type="Timer" parent="."]
unique_name_in_owner = true

[node name="UI" type="CanvasLayer" parent="."]

[node name="Victory" type="Control" parent="UI"]
unique_name_in_owner = true
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="UI/Victory"]
layout_mode = 0
offset_left = 1136.0
offset_top = 146.0
offset_right = 1656.0
offset_bottom = 747.0
theme_override_styles/panel = SubResource("StyleBoxEmpty_y0fv6")

[node name="win_text" type="Label" parent="UI/Victory/Panel"]
visible = false
z_index = 1
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -130.0
offset_top = -262.5
offset_right = 130.0
offset_bottom = -187.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.443137, 0.258824, 0, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 24
theme_override_fonts/font = ExtResource("12_nm4t2")
theme_override_font_sizes/font_size = 80
text = "VICTORY !"

[node name="lose_text" type="Label" parent="UI/Victory/Panel"]
z_index = 1
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -130.0
offset_top = -262.5
offset_right = 130.0
offset_bottom = -187.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.453125, 0.027237, 0.0123805, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 24
theme_override_fonts/font = ExtResource("12_nm4t2")
theme_override_font_sizes/font_size = 80
text = "DEFEAT !"

[node name="GameOverCard" type="TextureRect" parent="UI/Victory/Panel"]
layout_mode = 0
offset_right = 2090.0
offset_bottom = 2416.0
scale = Vector2(0.248684, 0.248684)
texture = ExtResource("5_ku74v")

[node name="coin_win" type="Label" parent="UI/Victory/Panel"]
layout_mode = 0
offset_left = 224.0
offset_top = 194.0
offset_right = 350.0
offset_bottom = 269.0
theme_override_colors/font_outline_color = Color(0.443137, 0.258824, 0, 1)
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("6_28vn7")
theme_override_font_sizes/font_size = 64
text = "+ 30"

[node name="coin_lose" type="Label" parent="UI/Victory/Panel"]
layout_mode = 0
offset_left = 224.0
offset_top = 194.0
offset_right = 350.0
offset_bottom = 269.0
theme_override_colors/font_outline_color = Color(0.454902, 0.027451, 0.0117647, 1)
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("6_28vn7")
theme_override_font_sizes/font_size = 64
text = "+ 30"

[node name="cup_win" type="Label" parent="UI/Victory/Panel"]
layout_mode = 0
offset_left = 224.0
offset_top = 326.0
offset_right = 350.0
offset_bottom = 401.0
theme_override_colors/font_outline_color = Color(0.443137, 0.258824, 0, 1)
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("6_28vn7")
theme_override_font_sizes/font_size = 64
text = "+ 1"

[node name="cup_lose" type="Label" parent="UI/Victory/Panel"]
layout_mode = 0
offset_left = 224.0
offset_top = 326.0
offset_right = 350.0
offset_bottom = 401.0
theme_override_colors/font_outline_color = Color(0.454902, 0.027451, 0.0117647, 1)
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("6_28vn7")
theme_override_font_sizes/font_size = 64
text = "+ 1"

[node name="PlayAgain" type="Button" parent="UI/Victory/Panel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -220.0
offset_top = 201.5
offset_right = 601.0
offset_bottom = 533.5
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(0.25, 0.208)
theme_override_styles/focus = SubResource("StyleBoxEmpty_3u7xm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_woa3c")
theme_override_styles/hover = SubResource("StyleBoxEmpty_nm4t2")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_dd2jf")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ku74v")
icon = ExtResource("8_745m4")

[node name="Leave" type="Button" parent="UI/Victory/Panel"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -248.0
offset_top = -99.0
offset_right = 573.0
offset_bottom = 239.0
grow_horizontal = 0
grow_vertical = 0
scale = Vector2(0.25, 0.208)
theme_override_styles/focus = SubResource("StyleBoxEmpty_3u7xm")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_woa3c")
theme_override_styles/hover = SubResource("StyleBoxEmpty_745m4")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_dd2jf")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ku74v")
icon = ExtResource("9_kjrwi")

[node name="Panel" type="Panel" parent="UI/Victory/Panel"]
layout_mode = 0
offset_left = 40.0
offset_top = 502.0
offset_right = 245.0
offset_bottom = 571.0
theme_override_styles/panel = SubResource("StyleBoxEmpty_jhou3")

[node name="Label" type="Label" parent="UI/Victory/Panel/Panel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -38.5
offset_top = -15.5
offset_right = 41.5
offset_bottom = 7.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.00154842, 0.34375, 0.23536, 1)
theme_override_constants/outline_size = 6
theme_override_fonts/font = ExtResource("12_nm4t2")
theme_override_font_sizes/font_size = 28
text = "Play Again"

[node name="Panel2" type="Panel" parent="UI/Victory/Panel"]
layout_mode = 0
offset_left = 272.0
offset_top = 502.0
offset_right = 477.0
offset_bottom = 571.0
theme_override_styles/panel = SubResource("StyleBoxEmpty_jhou3")

[node name="Label" type="Label" parent="UI/Victory/Panel/Panel2"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -34.0
offset_top = -21.5
offset_right = 34.0
offset_bottom = 11.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.359375, 0.00161881, 0.00161881, 1)
theme_override_constants/outline_size = 6
theme_override_fonts/font = ExtResource("12_nm4t2")
theme_override_font_sizes/font_size = 28
text = "Leave"

[node name="cup" type="Button" parent="UI/Victory"]
layout_mode = 1
offset_left = 64.0
offset_top = 32.0
offset_right = 508.0
offset_bottom = 241.0
scale = Vector2(0.5, 0.5)
theme_override_styles/focus = SubResource("StyleBoxEmpty_3xhsb")
theme_override_styles/hover = SubResource("StyleBoxTexture_7eg23")
theme_override_styles/pressed = SubResource("StyleBoxTexture_0o5rp")
theme_override_styles/normal = SubResource("StyleBoxTexture_didoo")
icon = ExtResource("9_745m4")

[node name="Label" type="Label" parent="UI/Victory/cup"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -256.417
offset_top = -28.0
offset_right = -70.417
offset_bottom = 28.0
grow_horizontal = 0
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.105882, 0.282353, 0.478431, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("12_nm4t2")
theme_override_font_sizes/font_size = 48
text = "212"
horizontal_alignment = 1

[node name="coin" type="Button" parent="UI/Victory"]
layout_mode = 1
offset_left = 312.0
offset_top = 32.0
offset_right = 756.0
offset_bottom = 241.0
scale = Vector2(0.5, 0.5)
theme_override_styles/focus = SubResource("StyleBoxEmpty_3xhsb")
theme_override_styles/hover = SubResource("StyleBoxTexture_7eg23")
theme_override_styles/pressed = SubResource("StyleBoxTexture_0o5rp")
theme_override_styles/normal = SubResource("StyleBoxTexture_didoo")
icon = ExtResource("10_kjrwi")

[node name="Label" type="Label" parent="UI/Victory/coin"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -256.417
offset_top = -28.0
offset_right = -70.417
offset_bottom = 28.0
grow_horizontal = 0
grow_vertical = 2
theme_override_colors/font_outline_color = Color(0.105882, 0.282353, 0.478431, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("12_nm4t2")
theme_override_font_sizes/font_size = 48
text = "212"
horizontal_alignment = 1

[node name="Search" type="Control" parent="UI"]
unique_name_in_owner = true
visible = false
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="UI/Search"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.033
anchor_top = 0.296
anchor_right = 0.471
anchor_bottom = 0.975
offset_left = 0.639999
offset_top = 0.255997
offset_right = -0.320007
offset_bottom = -1.40002
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxEmpty_kjrwi")

[node name="PlayerJoinContainer" type="GridContainer" parent="UI/Search/Panel"]
unique_name_in_owner = true
layout_mode = 0
offset_right = 840.0
offset_bottom = 584.0
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/h_separation = 62
theme_override_constants/v_separation = 40
columns = 7

[node name="Label" type="Label" parent="UI/Search"]
layout_mode = 0
offset_left = 64.0
offset_top = 56.0
offset_right = 725.0
offset_bottom = 131.0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("12_nm4t2")
theme_override_font_sizes/font_size = 64
text = "Searching for players . . ."
vertical_alignment = 1

[node name="players_count" type="Label" parent="UI/Search"]
layout_mode = 0
offset_left = 744.0
offset_top = 56.0
offset_right = 896.0
offset_bottom = 131.0
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 4
theme_override_constants/outline_size = 16
theme_override_fonts/font = ExtResource("12_nm4t2")
theme_override_font_sizes/font_size = 64
text = "0/10"
vertical_alignment = 1

[node name="Label2" type="Label" parent="UI/Search"]
layout_mode = 0
offset_left = 64.0
offset_top = 152.0
offset_right = 399.0
offset_bottom = 194.0
theme_override_colors/font_color = Color(0.223529, 0.682353, 0.878431, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("12_nm4t2")
theme_override_font_sizes/font_size = 36
text = "Estimated Wait Time : "
vertical_alignment = 1

[node name="wait_time" type="Label" parent="UI/Search"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 408.0
offset_top = 152.0
offset_right = 464.0
offset_bottom = 194.0
theme_override_colors/font_color = Color(0.223529, 0.682353, 0.878431, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 2
theme_override_constants/outline_size = 8
theme_override_fonts/font = ExtResource("12_nm4t2")
theme_override_font_sizes/font_size = 36
text = "20"
horizontal_alignment = 1
vertical_alignment = 1

[node name="back" type="Button" parent="UI/Search"]
z_index = 7
layout_mode = 0
offset_left = 1728.0
offset_top = 32.0
offset_right = 1856.0
offset_bottom = 160.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_olga5")
theme_override_styles/hover = SubResource("StyleBoxEmpty_13ri0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_lmfuw")
theme_override_styles/normal = SubResource("StyleBoxEmpty_k7c8f")
icon = ExtResource("13_745m4")

[node name="platform" parent="." instance=ExtResource("3_x5j47")]
unique_name_in_owner = true
transform = Transform3D(0.86824, 0, 4.92403, 0, 5, 0, -4.92403, 0, 0.86824, -8, -1.58475, 0)

[node name="block-grass-overhang-large" parent="platform" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -3.30746e-05, 0)
mesh = SubResource("ArrayMesh_nm4t2")

[node name="CollisionShape3D" type="CollisionShape3D" parent="platform"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00357056, 0.520737, 0.250549)
shape = SubResource("BoxShape3D_745m4")

[node name="Model" parent="platform" instance=ExtResource("5_28kla")]
unique_name_in_owner = true
transform = Transform3D(0.707107, 0, -0.707107, 0, 1, 0, 0.707107, 0, 0.707107, -0.211882, 1, 0.211882)

[connection signal="timeout" from="CountDownTimer" to="." method="_on_count_down_timer_timeout"]
[connection signal="pressed" from="UI/Victory/Panel/PlayAgain" to="." method="_on_play_again_pressed"]

[editable path="platform"]
